stages:
  - code-security
  - pre-validate
  - build
  - test
  - package
  - manifest
  - post-validate
  - release
  - dast
  - qa-governance

include:
  - project: bersama/backend-engineering/apps-ci
    ref: main
    file:
      - pipelines/go/service.yml
      - pipelines/go/db.yml

variables:
  CMD_NAME: onedash-api
  ECR_URI: 303083450960.dkr.ecr.ap-southeast-3.amazonaws.com/backend/onedash-api
  DISABLE_UNIT_TEST: true
  DISABLE_CODE_QUALITY: true
  ARM_SUPPORT: true

.golang:
  image: ${ECR_BACKEND_REPO}/golang:1.23.4

.golang-arm:
  image: ${ECR_BACKEND_REPO}/golang:1.23.4-arm

code-quality:
  image: ${ECR_BACKEND_REPO}/golangci-lint:1.23.4-1.59.1
