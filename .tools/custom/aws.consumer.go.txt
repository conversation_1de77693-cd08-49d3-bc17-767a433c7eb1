package consumer

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	awssqs "github.com/aws/aws-sdk-go/service/sqs"
	dakotacreds "gitlab.myteksi.net/dakota/common/aws/credentials"
	"gitlab.myteksi.net/dakota/common/aws/sqsClient"
	"gitlab.myteksi.net/gophers/go/commons/util/log/logging"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

const (
	logTag = "sqsClient-consumer"

	minMaxNumberOfMessages = 1
	maxMaxNumberOfMessages = 10

	minVisibilityTimeoutInSec = 0
	maxVisibilityTimeoutInSec = 12 * 60 * 60 // 12 hour
)

var (
	defaultClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 50,
			DialContext: (&net.Dialer{
				Timeout:   2 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			TLSHandshakeTimeout: 10 * time.Second},
		Timeout: 25 * time.Second,
	}
	defaultAwsRegion = "ap-southeast-1"
)

// isLocalStack checks if we're running in LocalStack mode
func isLocalStack() bool {
	endpoint := os.Getenv("AWS_ENDPOINT_URL")
	return endpoint != "" && (endpoint == "http://localhost:4566" || endpoint == "http://localstack:4566")
}

// getDefaultRegion returns the appropriate default region
func getDefaultRegion() string {
	if region := os.Getenv("AWS_DEFAULT_REGION"); region != "" {
		return region
	}
	return defaultAwsRegion
}

type QueueConsumer struct {
	sqsClient.SQSCommon // SQSCommon implements grabsqs.CommonFeature interface
	sqs                 sqsClient.SQS
	queueURL            string
	waitTimeSeconds     int64
}

var NewConsumer = func(queueURL, awsRegion string, waitTimeSeconds int64) (QueueConsumer, error) {
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(getDefaultRegion()),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Override region if provided
	if awsRegion != "" {
		cfg.Region = aws.String(awsRegion)
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return QueueConsumer{}, err
		}
		
		svc := awssqs.New(session)
		sqsCommon := sqsClient.NewSQSCommon(svc)
		
		return QueueConsumer{
			SQSCommon:       *sqsCommon,
			queueURL:        queueURL,
			sqs:             svc,
			waitTimeSeconds: waitTimeSeconds,
		}, nil
	}
	
	// Original logic for non-LocalStack environments
	//creds, session, err := dakotacreds.GetCredentialsForEKSPod(*slog.Noop(), cfg)
	//if err != nil {
	//	return QueueConsumer{}, err
	//}
	//
	//cfg.Credentials = creds
	session, err := session.NewSessionWithOptions(session.Options{
		SharedConfigState: session.SharedConfigEnable, // Must be set to enable
		Profile:           "dev_backend",
	})
	if err != nil {
		return QueueConsumer{}, err
	}
	svc := awssqs.New(session, cfg)
	sqsCommon := sqsClient.NewSQSCommon(svc)

	return QueueConsumer{
		SQSCommon:       *sqsCommon,
		queueURL:        queueURL,
		sqs:             svc,
		waitTimeSeconds: waitTimeSeconds,
	}, nil

}

var NewConsumerWithDevelopmentMode = func(queueURL, awsRegion string, waitTimeSeconds int64) (QueueConsumer, error) {
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(getDefaultRegion()),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Override region if provided
	if awsRegion != "" {
		cfg.Region = aws.String(awsRegion)
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected in development mode, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return QueueConsumer{}, err
		}
		
		svc := awssqs.New(session)
		sqsCommon := sqsClient.NewSQSCommon(svc)
		
		return QueueConsumer{
			SQSCommon:       *sqsCommon,
			queueURL:        queueURL,
			sqs:             svc,
			waitTimeSeconds: waitTimeSeconds,
		}, nil
	}
	
	// Original logic for non-LocalStack environments
	creds, session, err := dakotacreds.GetCredentialsForDevelopmentMode(*slog.Noop(), cfg)
	if err != nil {
		return QueueConsumer{}, err
	}

	cfg.Credentials = creds
	svc := awssqs.New(session, cfg)
	sqsCommon := sqsClient.NewSQSCommon(svc)

	return QueueConsumer{
		SQSCommon:       *sqsCommon,
		queueURL:        queueURL,
		sqs:             svc,
		waitTimeSeconds: waitTimeSeconds,
	}, nil

}

func (q *QueueConsumer) ReceiveMessage(parent context.Context,
	attributeName sqsClient.AttributeNames,
	maxNumberOfMessages int64, // 1 to 10
	messageAttributeName sqsClient.AttributeNames,
	visibilityTimeout time.Duration, // maximum 12 hours
) ([]*awssqs.Message, error) {

	// Check input variable ranges
	maxNumberOfMessages = q.rangeCheckerInt64(
		maxNumberOfMessages, minMaxNumberOfMessages, maxMaxNumberOfMessages,
	)
	visibilityTimeoutInSec := q.rangeCheckerInt64(
		int64(visibilityTimeout.Seconds()), minVisibilityTimeoutInSec, maxVisibilityTimeoutInSec,
	)

	input := &awssqs.ReceiveMessageInput{
		AttributeNames:        attributeName,
		MaxNumberOfMessages:   &maxNumberOfMessages,
		MessageAttributeNames: messageAttributeName,
		QueueUrl:              &q.queueURL,
		VisibilityTimeout:     &visibilityTimeoutInSec,
		WaitTimeSeconds:       &q.waitTimeSeconds,
	}

	resp, rErr := q.sqs.ReceiveMessage(input)
	if rErr != nil {
		logging.Error(logTag, "Failed to do ReceiveMessage. Error: %s", rErr)
		return nil, rErr
	}
	logging.Debug(logTag, "ReceiveMessage response: %#v", resp)
	return resp.Messages, nil
}

func (q *QueueConsumer) ChangeMessageVisibility(parent context.Context,
	receiptHandle string,
	visibilityTimeout time.Duration, // maximum 12 hours
) error {
	visibilityTimeoutInSec := q.rangeCheckerInt64(
		int64(visibilityTimeout.Seconds()), minVisibilityTimeoutInSec, maxVisibilityTimeoutInSec,
	)

	input := &awssqs.ChangeMessageVisibilityInput{
		QueueUrl:          &q.queueURL,
		ReceiptHandle:     &receiptHandle,
		VisibilityTimeout: &visibilityTimeoutInSec,
	}

	resp, rErr := q.sqs.ChangeMessageVisibility(input)
	if rErr != nil {
		logging.Error(logTag, "Failed to do ChangeMessageVisibility. Error: %s", rErr)
		return rErr
	}

	logging.Debug(logTag, "ChangeMessageVisibility response: %#v", resp)
	return nil
}

// DeleteMessage implements grabsqs.Producer interface
func (q *QueueConsumer) DeleteMessage(parent context.Context, receiptHandle string) error {
	input := &awssqs.DeleteMessageInput{
		QueueUrl:      &q.queueURL,
		ReceiptHandle: &receiptHandle,
	}

	resp, dErr := q.sqs.DeleteMessage(input)
	if dErr != nil {
		logging.Error(logTag, "Failed to do DeleteMessage. Error: %s", dErr)
	}
	logging.Debug(logTag, "DeleteMessage response: %#v", resp)

	return dErr
}

// DeleteMessageBatch implements grabsqs.Producer interface
func (q *QueueConsumer) DeleteMessageBatch(parent context.Context, receiptHandles ...string) ([]string, error) {
	length := len(receiptHandles)
	entries := make([]*awssqs.DeleteMessageBatchRequestEntry, length)
	for i := 0; i < length; i++ {
		id := strconv.Itoa(i)
		entry := &awssqs.DeleteMessageBatchRequestEntry{
			Id:            &id,
			ReceiptHandle: &receiptHandles[i],
		}
		entries[i] = entry
	}

	input := &awssqs.DeleteMessageBatchInput{
		Entries:  entries,
		QueueUrl: &q.queueURL,
	}

	resp, dErr := q.sqs.DeleteMessageBatch(input)
	if dErr != nil {
		logging.Error(logTag, "Failed to do DeleteMessage. Error: %s", dErr)
		return nil, dErr
	}

	failedLength := len(resp.Failed)
	failedReceiptHandles := make([]string, failedLength)
	if failedLength == 0 {
		// No partial failure, return directly
		return failedReceiptHandles, nil
	}

	errString := ""
	index := 0
	for _, entry := range resp.Failed {
		id, err := strconv.Atoi(*entry.Id)
		if err != nil {
			logging.Error(logTag, "Failed to convert ID back to number. Error: %s", err)
			continue
		}

		errString += fmt.Sprintf(
			"ID:%s\nCode:%s\nMessage:%s\nSenderFault:%v\n",
			*entry.Id, *entry.Code, *entry.Message, *entry.SenderFault,
		)

		failedReceiptHandles[index] = receiptHandles[id]
		index++
	}

	return failedReceiptHandles[:index], errors.New(errString)
}

func (q *QueueConsumer) rangeCheckerInt64(input, min, max int64) int64 {
	if input < min {
		return min
	}
	if input > max {
		return max
	}
	return input
}
