package producer

import (
	"context"
	"net"
	"net/http"
	"os"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	awssqs "github.com/aws/aws-sdk-go/service/sqs"
	dakotacreds "gitlab.myteksi.net/dakota/common/aws/credentials"
	"gitlab.myteksi.net/dakota/common/aws/sqsClient"
	"gitlab.myteksi.net/gophers/go/commons/util/log/logging"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

const (
	logTag = "sqsClient-producer"
)

var (
	defaultClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 50,
			DialContext: (&net.Dialer{
				Timeout:   2 * time.Second,
				KeepAlive: 30 * time.Second,
			}).<PERSON><PERSON><PERSON><PERSON>x<PERSON>,
			TLSHandshakeTimeout: 10 * time.Second},
		Timeout: 25 * time.Second,
	}
	defaultAwsRegion = "ap-southeast-1"
)

// isLocalStack checks if we're running in LocalStack mode
func isLocalStack() bool {
	endpoint := os.Getenv("AWS_ENDPOINT_URL")
	return endpoint != "" && (endpoint == "http://localhost:4566" || endpoint == "http://localstack:4566")
}

// getDefaultRegion returns the appropriate default region
func getDefaultRegion() string {
	if region := os.Getenv("AWS_DEFAULT_REGION"); region != "" {
		return region
	}
	return defaultAwsRegion
}

type QueueProducer struct {
	sqsClient.SQSCommon
	sqs          sqsClient.SQS
	queueURL     string
	delaySeconds int64
}

// Call this function from the adapter shall return a queue, using which functions like sendMessage can be used.
func NewProducer(queueURL, region string, delaySeconds int64) (QueueProducer, error) {
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(getDefaultRegion()),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Override region if provided
	if region != "" {
		cfg.Region = aws.String(region)
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return QueueProducer{}, err
		}
		
		svc := awssqs.New(session)
		sqsCommon := sqsClient.NewSQSCommon(svc)
		
		return QueueProducer{
			SQSCommon:    *sqsCommon,
			queueURL:     queueURL,
			sqs:          svc,
			delaySeconds: delaySeconds,
		}, nil
	}
	
	// Original logic for non-LocalStack environments
	//creds, session, err := dakotacreds.GetCredentialsForEKSPod(*slog.Noop(), cfg)
	//if err != nil {
	//	return QueueProducer{}, err
	//}
	//
	//cfg.Credentials = creds
	session, err := session.NewSessionWithOptions(session.Options{
		SharedConfigState: session.SharedConfigEnable, // Must be set to enable
		Profile:           "dev_backend",
	})
	if err != nil {
		return QueueProducer{}, err
	}
	svc := awssqs.New(session, cfg)
	sqsCommon := sqsClient.NewSQSCommon(svc)

	return QueueProducer{
		SQSCommon:    *sqsCommon,
		queueURL:     queueURL,
		sqs:          svc,
		delaySeconds: delaySeconds,
	}, nil
}

// Call this function from the adapter shall return a queue, using which functions like sendMessage can be used.
func NewProducerForDevelopmentMode(queueURL, region string, delaySeconds int64) (QueueProducer, error) {
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(getDefaultRegion()),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Override region if provided
	if region != "" {
		cfg.Region = aws.String(region)
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected in development mode, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return QueueProducer{}, err
		}
		
		svc := awssqs.New(session)
		sqsCommon := sqsClient.NewSQSCommon(svc)
		
		return QueueProducer{
			SQSCommon:    *sqsCommon,
			queueURL:     queueURL,
			sqs:          svc,
			delaySeconds: delaySeconds,
		}, nil
	}
	
	// Original logic for non-LocalStack environments
	creds, session, err := dakotacreds.GetCredentialsForDevelopmentMode(*slog.Noop(), cfg)
	if err != nil {
		return QueueProducer{}, err
	}

	cfg.Credentials = creds
	svc := awssqs.New(session, cfg)
	sqsCommon := sqsClient.NewSQSCommon(svc)

	return QueueProducer{
		SQSCommon:    *sqsCommon,
		queueURL:     queueURL,
		sqs:          svc,
		delaySeconds: delaySeconds,
	}, nil
}

func (q *QueueProducer) SendMessage(parent context.Context, messageBody string, attributes sqsClient.MessageAttributes) error {
	return q.sendMessageCommon(parent, messageBody, q.delaySeconds, attributes)
}
func (q *QueueProducer) SendMessageWithDelay(parent context.Context, messageBody string, delaySeconds int64, attributes sqsClient.MessageAttributes) error {
	return q.sendMessageCommon(parent, messageBody, delaySeconds, attributes)
}

func (q *QueueProducer) GetQueueAttributes(parent context.Context, attributes sqsClient.AttributeNames) (map[string]*string, error) {
	input := &awssqs.GetQueueAttributesInput{
		QueueUrl:       &q.queueURL,
		AttributeNames: attributes,
	}

	output, err := q.sqs.GetQueueAttributes(input)
	if err != nil {
		return nil, err
	}

	return output.Attributes, nil
}

func (q *QueueProducer) sendMessageCommon(parent context.Context, messageBody string, delaySeconds int64, attributes sqsClient.MessageAttributes) error {
	input := &awssqs.SendMessageInput{
		DelaySeconds:      &delaySeconds,
		QueueUrl:          &q.queueURL,
		MessageAttributes: nil,
		MessageBody:       &messageBody,
	}
	if attributes != nil {
		var aErr error
		attributeMap := make(sqsClient.MessageAttributeMap)
		attributeMap, aErr = attributes.ToMessageAttributeMap()
		if aErr != nil {
			logging.Error(logTag, "Failed to get attributes. Error: %s", aErr)
			return aErr
		}
		input.MessageAttributes = attributeMap
	}

	logging.Info("test", messageBody)

	resp, sErr := q.sqs.SendMessage(input)
	if sErr != nil {
		logging.Error(logTag, "Failed to do SendMessage. Error: %s", sErr)
	}
	logging.Debug(logTag, "SendMessage response: %#v", resp)

	return sErr
}
