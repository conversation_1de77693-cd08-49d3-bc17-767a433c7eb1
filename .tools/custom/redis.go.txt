// Package redis defines the client to communicate with redis instance.
package redis

import (
	"context"
	"crypto/tls"
	"fmt"
	"reflect"
	"strings"
	"time"

	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"

	"github.com/redis/go-redis/v9"

	dogstatsd "github.com/DataDog/datadog-go/statsd"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

// Config defines the configuration for redis connection.
type Config struct {
	Addr               string `json:"addr"`
	PoolSize           int    `json:"poolSize"`
	ReadTimeoutInSec   int    `json:"readTimeoutInSec"`
	WriteTimeoutInSec  int    `json:"writeTimeoutInSec"`
	IdleTimeoutInSec   int    `json:"idleTimeoutInSec"`
	ReadOnlyFromSlaves bool   `json:"readOnlyFromSlaves"`
	Password           string `json:"password"`
	TLSEnabled         bool   `json:"tlsEnabled"`
}

// ConfigV2 defines the configuration for redis connection.
type ConfigV2 struct {
	Config
	circuitbreaker.CircuitSettings
}

const (
	defaultDialTimeout = 5 * time.Second
	defaultMaxExpiry   = 168 * time.Hour

	logTag     = "redis_cli"
	elapsedKey = "elapsed"
)

var (
	redisNewCluster = redis.NewClusterClient
	redisNewClient  = redis.NewClient

	defaultCBConfigVals = map[string]int64{
		"Timeout":                     500,
		"MaxConcurrentRequests":       1000,
		"RequestVolumeThreshold":      100,
		"SleepWindow":                 5000,
		"ErrorPercentThreshold":       20,
		"QueueSizeRejectionThreshold": 1000,
	}
)

// StatsDConfig is the configuration for statsd
type StatsDConfig struct {
	Host string `json:"host"`
	Port int    `json:"port"`
}

// MakeClusterClientV2 creates a cluster client with circuit breaker
func MakeClusterClientV2(config *ConfigV2, options ...Option) Client {
	applyRedisDefaultCBValue(config)
	conf := makeClientConfig(&config.Config, options...)
	universalCli := redisNewCluster(conf.options.Cluster())
	circuitbreaker.ConfigureCircuitV2(config.CommandName,
		circuitbreaker.WithTimeoutMs(config.Timeout),
		circuitbreaker.WithErrorPercentageThreshold(config.ErrorPercentThreshold),
		circuitbreaker.WithMaxConcurrentRequests(config.MaxConcurrentRequests),
		circuitbreaker.WithRequestVolumeThreshold(config.RequestVolumeThreshold),
		circuitbreaker.WithSleepWindowMs(config.SleepWindow),
		circuitbreaker.WithQueueSizeRejectionThreshold(config.QueueSizeRejectionThreshold),
		circuitbreaker.WithCommandGroup(config.CommandGroup),
	)

	c := &clientV2{
		client: client{
			redisCli:      universalCli,
			clustered:     true,
			maxExpiryTime: defaultMaxExpiry,
			stats:         conf.stats,
			logger:        conf.logger,
		},
		cbOptions:   conf.cbOptions,
		circuitName: config.CommandName,
		wrappers:    []wrapper{},
	}

	if conf.cbStatsConfig != nil && len(conf.cbStatsConfig.Host) > 0 {
		dClient, _ := dogstatsd.New(fmt.Sprintf("%s:%d", conf.cbStatsConfig.Host, conf.cbStatsConfig.Port))
		circuitbreaker.SetupTurbineFromDatadogClient(dClient)
	}

	c.wrappers = append(c.wrappers, c.WithCircuitBreaker)
	return c
}

func applyRedisDefaultCBValue(config *ConfigV2) {
	v := reflect.ValueOf(config).Elem()
	for fieldName, defaultV := range defaultCBConfigVals {
		if v.FieldByName(fieldName).IsZero() {
			v.FieldByName(fieldName).SetInt(defaultV)
		}
	}
}

// MakeClusterClient creates a cluster client.
func MakeClusterClient(config *Config, options ...Option) Client {
	conf := makeClientConfig(config, options...)
	universalCli := redisNewClient(conf.options.Simple())
	return &client{
		redisCli:      universalCli,
		clustered:     true,
		maxExpiryTime: defaultMaxExpiry,
		stats:         conf.stats,
		logger:        conf.logger,
	}
}

func makeClientConfig(config *Config, options ...Option) *redisConfig {
	if len(config.Addr) == 0 {
		panic("no addr specified, cannot initiate the redis cluster client")
	}
	_, cancelFunc := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelFunc()

	conf := &redisConfig{
		options: redis.UniversalOptions{
			Addrs:           []string{config.Addr},
			ReadOnly:        true,
			RouteByLatency:  true,
			Password:        config.Password,
			DialTimeout:     defaultDialTimeout,
			ReadTimeout:     time.Duration(config.ReadTimeoutInSec) * time.Second,
			WriteTimeout:    time.Duration(config.WriteTimeoutInSec) * time.Second,
			PoolSize:        config.PoolSize,
			ConnMaxIdleTime: time.Duration(config.IdleTimeoutInSec) * time.Second,
		},
		stats:  statsd.NewNoop(),
		logger: slog.FallbackLogger(),
	}
	if config.ReadOnlyFromSlaves {
		ReadOnlyFromSlaves(true)(conf)
	}
	if config.TLSEnabled {
		WithTLSConfig(&tls.Config{
			MinVersion: tls.VersionTLS12,
		})(conf)
	}

	for _, opt := range options {
		opt(conf)
	}
	return conf
}

type universalClientWrapper interface {
	redis.UniversalClient
}

//go:generate mockery --case=underscore --name=universalClientWrapper --inpackage

type client struct {
	redisCli      universalClientWrapper
	maxExpiryTime time.Duration
	clustered     bool

	stats  statsd.Client
	logger slog.YallLogger
}

type clientV2 struct {
	client      client
	cbOptions   []circuitbreaker.Option
	circuitName string
	wrappers    []wrapper
}

func (c *client) setMaxExpiry(expiryTime time.Duration) time.Duration {
	if expiryTime > c.maxExpiryTime {
		return c.maxExpiryTime
	}
	return expiryTime
}

func (c *client) normalizeError(err error) error {
	if err == redis.Nil {
		return ErrNoData
	}
	return err
}

func (c *client) getStatsTags(operations ...redis.Cmder) []string {
	var statsTags []string
	statsTags = append(statsTags, fmt.Sprintf("clustered:%t", c.clustered))
	for _, operation := range operations {
		statsTags = append(statsTags, fmt.Sprintf("operation:%s", strings.ReplaceAll(operation.FullName(), " ", "_")))
		if err := operation.Err(); err != nil {
			statsTags = append(statsTags, fmt.Sprintf("error:%s", strings.ReplaceAll(err.Error(), " ", "_")))
		}
	}
	return statsTags
}

func (c *clientV2) execute(ctx context.Context, f handler, ptr interface{}) error {
	for _, w := range c.wrappers {
		f = w(f)
	}
	return f(ctx, ptr)
}

//func (c *clientV2) WithCircuitBreaker2(f handler) handler {
//	return func(ctx context.Context, ptr interface{}) error {
//		//time.Sleep(4 * time.Millisecond)
//		return f(ctx, ptr)
//	}
//}

func (c *clientV2) WithCircuitBreaker(f handler) handler {
	return func(ctx context.Context, ptr interface{}) error {
		errorChan := make(chan error, 1)
		hystrixChan := circuitbreaker.Go(ctx, c.circuitName, func() error {
			err := f(ctx, ptr)
			errorChan <- err
			return filterNonCriticalError(err)
		}, c.cbOptions...)

		select {
		case err := <-errorChan:
			return err
		case hystrixErr := <-hystrixChan:
			if hystrixErr == nil {
				return <-errorChan
			}
			return hystrixErr
		}
	}
}

type wrapper func(f handler) handler

type handler func(ctx context.Context, ptr interface{}) error
