package s3client

import (
	"context"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3iface"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/aws/aws-sdk-go/service/s3/s3manager/s3manageriface"
	"github.com/aws/aws-sdk-go/aws/session"

	dakotacreds "gitlab.myteksi.net/dakota/common/aws/credentials"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	logTag          = "s3Client"
	awsRegionEnvVar = "AWS_REGION"
)

var logging, _ = slog.FromConfig(&slog.Config{})

var (
	defaultClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 50,
			DialContext: (&net.Dialer{
				Timeout:   2 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			TLSHandshakeTimeout: 10 * time.Second},
		Timeout: 25 * time.Second,
	}
	defaultAwsRegion = "ap-southeast-3"
)

// isLocalStack checks if we're running in LocalStack mode
func isLocalStack() bool {
	endpoint := os.Getenv("AWS_ENDPOINT_URL")
	return endpoint != "" && (endpoint == "http://localhost:4566" || endpoint == "http://localstack:4566")
}

// getDefaultRegion returns the appropriate default region
func getDefaultRegion() string {
	if region := os.Getenv("AWS_DEFAULT_REGION"); region != "" {
		return region
	}
	return defaultAwsRegion
}


// s3API defines the interface for S3 API.
//
//go:generate mockery --name s3API --case underscore --inpackage
type s3API interface {
	s3iface.S3API
}

// s3UploaderAPI defines the interface for S3 Uploader API.
//
//go:generate mockery --name s3UploaderAPI --case underscore --inpackage
type s3UploaderAPI interface {
	s3manageriface.UploaderAPI
}

// s3DownloaderAPI is the interface type for S3 Downloader API.
//
//go:generate mockery --name s3DownloaderAPI --case underscore --inpackage
type s3DownloaderAPI interface {
	s3manageriface.DownloaderAPI
}

// client is the wrapper for the aws s3 client and conforms to the grabs3 interface
type client struct {
	client     s3API
	uploader   s3UploaderAPI
	downloader s3DownloaderAPI
}

// NewS3Client constructs a new s3 client
func NewS3Client() (S3, error) {
	region, present := os.LookupEnv(awsRegionEnvVar)
	if !present || len(strings.TrimSpace(region)) == 0 {
		logging.Warn(logTag, "no env var AWS_REGION, using default region")
		region = getDefaultRegion()
	}
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(region),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return nil, err
		}
		
		awsClient := s3.New(session)
		uploader := s3manager.NewUploaderWithClient(awsClient)
		downloader := s3manager.NewDownloaderWithClient(awsClient)
		
		instance := &client{
			client:     awsClient,
			uploader:   uploader,
			downloader: downloader,
		}
		return instance, nil
	}
	
	// Original logic for non-LocalStack environments
	session, err := session.NewSessionWithOptions(session.Options{
		SharedConfigState: session.SharedConfigEnable,  // Must be set to enable
		Profile:           "dev_backend",
	})

	if err != nil {
		return nil, err
	}

	awsClient := s3.New(session, cfg)
	uploader := s3manager.NewUploaderWithClient(awsClient)
	downloader := s3manager.NewDownloaderWithClient(awsClient)

	instance := &client{
		client:     awsClient,
		uploader:   uploader,
		downloader: downloader,
	}
	return instance, nil
}

// NewS3ClientForDevelopmentMode constructs a new s3 client
func NewS3ClientForDevelopmentMode() (S3, error) {
	region, present := os.LookupEnv(awsRegionEnvVar)
	if !present || len(strings.TrimSpace(region)) == 0 {
		logging.Warn(logTag, "no env var AWS_REGION, using default region")
		region = getDefaultRegion()
	}
	cfg := &aws.Config{
		CredentialsChainVerboseErrors: aws.Bool(true),
		Region:                        aws.String(region),
		HTTPClient:                    defaultClient,
		SleepDelay:                    time.Sleep,
		MaxRetries:                    aws.Int(5),
	}
	
	// Configure for LocalStack if detected
	if isLocalStack() {
		logging.Info(logTag, "LocalStack detected in development mode, using LocalStack configuration")
		endpoint := os.Getenv("AWS_ENDPOINT_URL")
		cfg.Endpoint = aws.String(endpoint)
		cfg.DisableSSL = aws.Bool(true)
		cfg.S3ForcePathStyle = aws.Bool(true)
		
		// Use static credentials for LocalStack
		accessKey := os.Getenv("AWS_ACCESS_KEY_ID")
		secretKey := os.Getenv("AWS_SECRET_ACCESS_KEY")
		if accessKey == "" {
			accessKey = "test"
		}
		if secretKey == "" {
			secretKey = "test"
		}
		
		cfg.Credentials = credentials.NewStaticCredentials(accessKey, secretKey, "")
		
		// Create session without shared config for LocalStack
		session, err := session.NewSession(cfg)
		if err != nil {
			return nil, err
		}
		
		awsClient := s3.New(session)
		uploader := s3manager.NewUploaderWithClient(awsClient)
		downloader := s3manager.NewDownloaderWithClient(awsClient)
		
		instance := &client{
			client:     awsClient,
			uploader:   uploader,
			downloader: downloader,
		}
		return instance, nil
	}
	
	// Original logic for non-LocalStack environments
	creds, session, err := dakotacreds.GetCredentialsForDevelopmentMode(*logging, cfg)
	if err != nil {
		return nil, err
	}

	cfg.Credentials = creds
	awsClient := s3.New(session, cfg)
	uploader := s3manager.NewUploaderWithClient(awsClient)
	downloader := s3manager.NewDownloaderWithClient(awsClient)

	instance := &client{
		client:     awsClient,
		uploader:   uploader,
		downloader: downloader,
	}
	return instance, nil
}

// PreSignedGetURL generates a pre signed GET URL that enables you to temporarily share a file without making it public
func (client *client) PreSignedGetURL(bucket string, key string, expiryMinutes int) (string, error) {
	req, _ := client.client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})

	urlStr, err := req.Presign(time.Duration(expiryMinutes) * time.Minute)

	return urlStr, err
}

// PreSignedPutURL generates a pre signed PUT URL that enables you to upload content to s3
func (client *client) PreSignedPutURL(bucket string, key string, expiryMinutes int, contentMD5 string, options ...PutObjectOption) (string, error) {
	putObjectInput := &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}
	for _, option := range options {
		option(putObjectInput)
	}
	req, _ := client.client.PutObjectRequest(putObjectInput)

	req.HTTPRequest.Header.Set("Content-MD5", contentMD5)
	urlStr, err := req.Presign(time.Duration(expiryMinutes) * time.Minute)

	return urlStr, err
}

// PreSignedPutURLWithHeaders generates a pre signed PUT URL by including the required headers that enables you to upload content to s3
func (client *client) PreSignedPutURLWithHeaders(bucket string, key string, expiryMinutes int, headers map[string]string, options ...PutObjectOption) (string, error) {
	putObjectInput := &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}
	for _, option := range options {
		option(putObjectInput)
	}
	req, _ := client.client.PutObjectRequest(putObjectInput)

	for key, value := range headers {
		req.HTTPRequest.Header.Set(key, value)
	}
	urlStr, err := req.Presign(time.Duration(expiryMinutes) * time.Minute)

	return urlStr, err
}

// IsObjectExist retrieves metadata from an object to verify whether object exists
func (client *client) IsObjectExist(bucket string, key string) (bool, error) {
	headObjectInput := &s3.HeadObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	_, err := client.client.HeadObject(headObjectInput)
	if err != nil {
		return false, err
	}

	return true, nil
}

// IsObjectExistWithWait verify whether an object exists with wait.
func (client *client) IsObjectExistWithWait(bucket string, key string) (bool, error) {
	headObjectInput := &s3.HeadObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	err := client.client.WaitUntilObjectExists(headObjectInput)
	if err != nil {
		return false, err
	}

	return true, nil
}

// GetContentLength efficiently retrieves object length from s3
func (client *client) GetContentLength(bucket string, key string) (int64, error) {
	headObjectInput := &s3.HeadObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	headObjectOutput, err := client.client.HeadObject(headObjectInput)
	if err != nil {
		return 0, err
	}

	return *headObjectOutput.ContentLength, nil
}

// GetHeadObject retrieve the headers for the key
func (client *client) GetHeadObject(bucket string, key string) (*s3.HeadObjectOutput, error) {
	headObjectInput := &s3.HeadObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	return client.client.HeadObject(headObjectInput)
}

// GetContentType efficiently retrieves object type from s3
func (client *client) GetContentType(bucket string, key string) (string, error) {
	getObjectInput := &s3.GetObjectInput{
		Bucket: &bucket,
		Key:    &key,
		// to identify the content type we only need first 512 bytes of data
		Range: aws.String("bytes=0-512"),
	}

	getObjectOutput, err := client.client.GetObject(getObjectInput)
	if err != nil {
		logging.Warn(logTag, "error getting object output from s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return "", err
	}

	defer func() { _ = getObjectOutput.Body.Close() }()
	body, err := ioutil.ReadAll(getObjectOutput.Body)
	if err != nil {
		logging.Warn(logTag, "error reading object content from object output of s3 ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return "", err
	}

	contentType := http.DetectContentType(body[0:512])
	return contentType, nil
}

// GetObject retrieves an object from s3
func (client *client) GetObject(bucket string, key string) ([]byte, error) {
	getObjectInput := &s3.GetObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	getObjectOutput, err := client.client.GetObject(getObjectInput)
	if err != nil {
		logging.Warn(logTag, "error getting object output from s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return nil, err
	}

	defer func() { _ = getObjectOutput.Body.Close() }()
	body, err := ioutil.ReadAll(getObjectOutput.Body)
	if err != nil {
		logging.Warn(logTag, "error reading object content from object output of s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
	}

	return body, err
}

// GetObjectWithContext retrieves an object from s3 with a context for cancellation.
func (client *client) GetObjectWithContext(ctx context.Context, bucket string, key string) ([]byte, error) {
	getObjectInput := &s3.GetObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}

	getObjectOutput, err := client.client.GetObjectWithContext(ctx, getObjectInput)
	if err != nil {
		logging.Warn(logTag, "error getting object output from s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return nil, err
	}

	defer func() { _ = getObjectOutput.Body.Close() }()
	body, err := ioutil.ReadAll(getObjectOutput.Body)
	if err != nil {
		logging.Warn(logTag, "error reading object content from object output of s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
	}

	return body, err
}

// PutObject puts an object into S3.
func (client *client) PutObject(bucket string, key string, body io.ReadSeeker, options ...PutObjectOption) error {
	putObjectInput := &s3.PutObjectInput{
		Bucket: &bucket,
		Key:    &key,
		Body:   body,
	}

	for _, option := range options {
		option(putObjectInput)
	}

	_, err := client.client.PutObject(putObjectInput)
	if err != nil {
		logging.Warn(logTag, "error putting object to s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return err
	}

	return nil
}

// PutObjectWithContext puts an object into S3 with a context for cancellation.
func (client *client) PutObjectWithContext(ctx context.Context, bucket string, key string, body io.ReadSeeker, options ...PutObjectOption) error {
	putObjectInput := &s3.PutObjectInput{
		Bucket: &bucket,
		Key:    &key,
		Body:   body,
	}

	for _, option := range options {
		option(putObjectInput)
	}

	_, err := client.client.PutObjectWithContext(ctx, putObjectInput)
	if err != nil {
		logging.Warn(logTag, "error putting object to s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return err
	}

	return nil
}

// DeleteObject deletes an object from S3.
func (client *client) DeleteObject(bucket string, key string) error {
	input := &s3.DeleteObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}

	_, err := client.client.DeleteObject(input)
	if err != nil {
		logging.Warn(logTag, "error deleting object to s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("key", key))
		return err
	}

	return nil
}

// ListObjectsV2 lists objects.
func (client *client) ListObjectsV2(bucket string, prefix string) (*s3.ListObjectsV2Output, error) {
	listObjectInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(bucket),
		Prefix: aws.String(prefix),
	}
	objects, err := client.client.ListObjectsV2(listObjectInput)
	if err != nil {
		logging.Warn(logTag, "error listing object to s3 for ", slog.CustomTag("bucket", bucket), slog.CustomTag("prefix", prefix))
		return nil, err
	}

	return objects, nil
}

// CopyObject copies the object from source bucket/prefix to destination bucket/prefix
func (client *client) CopyObject(sourceBucket, sourceKey, destinationBucket, destinationKey string, options ...CopyObjectOption) error {
	input := &s3.CopyObjectInput{
		CopySource: aws.String(url.PathEscape(sourceBucket + "/" + sourceKey)),
		Bucket:     aws.String(destinationBucket),
		Key:        aws.String(destinationKey),
	}

	for _, option := range options {
		option(input)
	}
	_, err := client.client.CopyObject(input)
	if err != nil {
		logging.Warn(logTag, "error copying object from source ", slog.CustomTag("bucket", sourceBucket),
			slog.CustomTag("key", sourceKey), slog.CustomTag("destinationBucket", destinationBucket),
			slog.CustomTag("destinationKey", destinationKey))

		return err
	}
	return nil
}

// ListObjectsContinueTokenV2 list objects, continuing from the index pointed to by continue token
func (client *client) ListObjectsContinueTokenV2(bucket, prefix, continueToken string) (*s3.ListObjectsV2Output, error) {
	listObjectInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(bucket),
		Prefix: aws.String(prefix),
	}
	if continueToken != "" {
		listObjectInput.SetContinuationToken(continueToken)
	}

	objects, err := client.client.ListObjectsV2(listObjectInput)
	if err != nil {
		logging.Warn(logTag, "error listing objects from s3 for bucket ", slog.CustomTag("bucket", bucket), slog.CustomTag("prefix", prefix), slog.CustomTag("continueToken", continueToken))
		return nil, err
	}

	return objects, nil
}

// ListObjectsWithPrefix list objects matching a specified prefix
func (client *client) ListObjectsWithPrefix(ctx context.Context, bucket, prefix string, maxKeys int, options ...ListObjectsV2Option) ([]s3.Object, error) {
	listObjectInput := &s3.ListObjectsV2Input{
		Bucket:  aws.String(bucket),
		Prefix:  aws.String(prefix),
		MaxKeys: aws.Int64(int64(maxKeys)),
	}
	for _, option := range options {
		option(listObjectInput)
	}

	var err error
	objects := []s3.Object{}
	err = client.client.ListObjectsV2PagesWithContext(ctx, listObjectInput,
		func(page *s3.ListObjectsV2Output, lastPage bool) bool {
			for _, obj := range page.Contents {
				objects = append(objects, *obj)
				if len(objects) >= maxKeys {
					return false
				}
			}
			return true
		})
	if err != nil {
		logging.Warn(logTag, "error listing objects from s3 for ", slog.CustomTag("bucket", bucket),
			slog.CustomTag("prefix", prefix))
		return nil, err
	}

	return objects, nil
}

// Upload upload an object into S3.
// This API leverage on S3 multipart upload API for better upload performance.
func (client *client) Upload(bucket string, key string, body io.ReadSeeker, options ...UploadOption) error {
	uploadInput := &s3manager.UploadInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		Body:   body,
	}

	for _, option := range options {
		option(uploadInput)
	}

	_, err := client.uploader.Upload(uploadInput)
	if err != nil {
		logging.Warn(logTag, "error uploading object to s3 for ", slog.CustomTag("bucket", bucket),
			slog.CustomTag("key", key))
		return err
	}

	return nil
}

// Download download an object from S3.
// This API leverage on S3 multipart download API for better download performance.
func (client *client) Download(bucket string, key string) ([]byte, error) {
	writer := aws.NewWriteAtBuffer([]byte{})
	getObjectInput := &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}

	_, err := client.downloader.Download(writer, getObjectInput)
	if err != nil {
		logging.Warn(logTag, "error getting object output from s3 for ", slog.CustomTag("bucket", bucket),
			slog.CustomTag("key", key))
		return nil, err
	}

	return writer.Bytes(), nil
}
