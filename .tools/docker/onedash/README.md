# Local Development Environment

This directory contains Docker Compose configuration and scripts for running the OneDash backend in a local development environment.

## Prerequisites

1. Install container runtime: [Colima](https://github.com/abiosoft/colima)

   ```sh
   brew install colima
   ```

2. Start colima runtime
   ```sh
   colima start --vm-type vz
   ```

## Quick Start

### Standard Development (with external services)

1. Initialize containers
   ```sh
   make docker-up
   ```
2. Apply db migrations (optional)
   ```sh
   make docker-migrate
   ```
3. Start the app
   ```sh
   make run-local
   ```

### LocalStack Development (fully local AWS services)

1. Start LocalStack and switch configuration
   ```sh
   ./.tools/env/setup-infra.sh start
   ./.tools/env/switch-env.sh localstack
   ```
2. Start other services
   ```sh
   make docker-up
   ```
3. Run the app with LocalStack config
   ```sh
   make run-local
   ```

## Configuration Management

The project supports two environment configurations:

### Development Configuration (External Services)

- **Config file**: `config_files/service-conf.json`
- **Environment**: `.tools/env/.env.dev`
- **Usage**: External AWS services, real database connections
- **Switch command**: `./.tools/env/switch-env.sh dev`

### LocalStack Configuration (Local Services)

- **Config file**: `config_files/service-conf.local.json`
- **Environment**: `.tools/env/.env.local`
- **Usage**: All AWS services run locally via LocalStack
- **Switch command**: `./.tools/env/switch-env.sh localstack`

## Services Available

### Core Services (Always Available)

- **MySQL** - Database (Port 30001)
- **Redis** - Cache (Port 30002)
- **Kafka** - Message streaming (Ports 9092, 29092)
- **Kafka UI** - Web interface (Port 8077)

### AWS Services (LocalStack)

- **SQS** - Queue service for blockAccountQueue, auditTrailQueue, etc.
- **SNS** - Notification service
- **Endpoint**: `http://localhost:4566`

## LocalStack Management

### Quick Commands

```bash
# Start LocalStack and create all SQS queues
./.tools/env/setup-infra.sh start

# Switch between LocalStack and Development configurations
./.tools/env/switch-env.sh localstack    # Use LocalStack (local)
./.tools/env/switch-env.sh dev           # Use Development (external services)
./.tools/env/switch-env.sh status        # Check current config

# View logs and manage
./.tools/env/setup-infra.sh logs
./.tools/env/setup-infra.sh stop
```

### Supported Queues

- `blockAccountQueue` - Account blocking operations
- `unblockAccountQueue` - Account unblocking operations
- `auditTrailQueue` - Audit logging
- `unlinkAccountQueue` - Account unlinking
- `transferOnBehalfQueue` - Transfer operations
- `updateAccountStatusQueue` - Status updates
- `deactivateLOCQueue` - LOC deactivation

## Configuration Files

- `docker-compose.yml` - Main services configuration
- `service-conf-localstack.json` - LocalStack service configuration
- `setup-localstack.sh` - LocalStack management script
- `switch-config.sh` - Configuration switcher
- `localstack-examples.sh` - Usage examples

## Testing Queue Integration

Send a test message to blockAccountQueue:

```bash
AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test \
aws --endpoint-url=http://localhost:4566 --region=eu-central-1 \
sqs send-message \
--queue-url "http://sqs.eu-central-1.localhost.localstack.cloud:4566/************/onedash-block-account-queue" \
--message-body '{"action":"block_account","account_id":"ACC123","reason":"Test"}'
```

## Troubleshooting

For detailed queue integration documentation, see `BLOCK-ACCOUNT-QUEUE-INTEGRATION.md`.

### LocalStack Issues

```bash
# Check if LocalStack is running
./setup-localstack.sh logs
curl http://localhost:4566/_localstack/health

# Recreate queues if needed
./setup-localstack.sh create-queues
```

### Configuration Issues

```bash
# Check current configuration
./switch-config.sh status
grep -i "localhost:4566\|amazonaws" ../../../config_files/service-conf.json
```

### Port Conflicts

If ports are in use, check running containers:

```bash
docker ps
docker-compose down
```

## File Structure

```
.tools/docker/onedash/
├── docker-compose.yml              # Main services
├── setup-localstack.sh             # LocalStack management
├── switch-config.sh                # Config switcher
├── service-conf-localstack.json    # LocalStack configuration
├── localstack-examples.sh          # Usage examples
├── localstack-block-account-example.go  # Go example
├── .aws/                           # Local AWS credentials (git-ignored)
└── README.md                       # This file
```
