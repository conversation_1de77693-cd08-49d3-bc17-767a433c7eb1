# Development Environment
# This file configures the application to use external AWS services

current_path := $(shell pwd)

# Use development configuration with external services
export SERVICE_CONF=$(current_path)/config_files/service-conf.json

export SECRET_CONF=$(current_path)/vault/secrets/
export SERVICE_NAME=onedash-be
export ALL_DEPRECATED_INITS_MIGRATED=true
export MYSQL_HOST=localhost
export SERVICE_RUNTIME_ENV=dev

# AWS Configuration
# For production, use real AWS credentials
export AWS_ACCESS_KEY_ID=0
export AWS_SECRET_ACCESS_KEY=0
export AWS_SESSION_TOKEN=0

# LocalStack Configuration (for reference, not used in dev mode)
export LOCALSTACK_AWS_ACCESS_KEY_ID=test
export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test
export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1
export LOCALSTACK_ENDPOINT_URL=http://localhost:4566
