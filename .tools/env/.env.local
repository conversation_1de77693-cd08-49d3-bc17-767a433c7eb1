# LocalStack Development Environment
# This file configures the application to use LocalStack for all AWS services

current_path := $(shell pwd)

# Use LocalStack configuration
export SERVICE_CONF=$(current_path)/config_files/service-conf.local.json

export SECRET_CONF=$(current_path)/vault/secrets/
export SERVICE_NAME=onedash-be
export ALL_DEPRECATED_INITS_MIGRATED=true
export MYSQL_HOST=localhost
export SERVICE_RUNTIME_ENV=local

# AWS Configuration (LocalStack)
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_SESSION_TOKEN=
export AWS_DEFAULT_REGION=eu-central-1
export AWS_ENDPOINT_URL=http://localhost:4566

# Disable AWS SSO and other credential sources that might interfere
export AWS_PROFILE=
export AWS_CONFIG_FILE=
export AWS_SHARED_CREDENTIALS_FILE=
export AWS_SDK_LOAD_CONFIG=false

# LocalStack Configuration (for local development)
export LOCALSTACK_AWS_ACCESS_KEY_ID=test
export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test
export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1
export LOCALSTACK_ENDPOINT_URL=http://localhost:4566
