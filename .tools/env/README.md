# OneDash Environment Management

This directory contains consolidated scripts for managing the OneDash development environment.

## File Structure

```
.tools/env/
├── setup-infra.sh      # Infrastructure management (LocalStack, Docker, SQS)
├── switch-env.sh       # Environment configuration switcher
├── .env.dev           # Development environment template
├── .env.local         # LocalStack environment template
└── README.md          # This documentation
```

The environment templates (`.env.dev` and `.env.local`) are copied to the project root as `.env` when switching configurations.

## Scripts

### 1. Infrastructure Setup (`setup-infra.sh`)

Handles all infrastructure setup including LocalStack, Docker services, and SQS queues.

**Usage:**

```bash
# Start everything (recommended)
./.tools/env/setup-infra.sh start

# Start only LocalStack
./.tools/env/setup-infra.sh start-localstack

# Start only Docker services
./.tools/env/setup-infra.sh start-docker

# Create SQS queues
./.tools/env/setup-infra.sh create-queues

# Create S3 buckets
./.tools/env/setup-infra.sh create-buckets

# Check infrastructure status
./.tools/env/setup-infra.sh status

# Check specific queue
./.tools/env/setup-infra.sh check-queue onedash-block-account-queue

# Test queue by sending a message
./.tools/env/setup-infra.sh test-queue onedash-block-account-queue

# Stop all services
./.tools/env/setup-infra.sh stop
```

### 2. Environment Switcher (`switch-env.sh`)

Switches between different development environments (LocalStack vs Development).

**Usage:**

```bash
# Switch to LocalStack (fully local development)
./.tools/env/switch-env.sh localstack

# Switch to Development (external AWS services)
./.tools/env/switch-env.sh development

# Check current configuration
./.tools/env/switch-env.sh status

# Generate environment export commands
./.tools/env/switch-env.sh export localstack
./.tools/env/switch-env.sh export development
```

## Quick Start

### Full LocalStack Development

```bash
# 1. Switch to LocalStack configuration
./.tools/env/switch-env.sh localstack

# 2. Start infrastructure
./.tools/env/setup-infra.sh start

# 3. Load environment variables
source .env

# 4. Run your application
make run-local
```

### Development with External Services

```bash
# 1. Switch to Development configuration
./.tools/env/switch-env.sh development

# 2. Start local services only
./.tools/env/setup-infra.sh start-docker

# 3. Load environment variables
source .env

# 4. Run your application
make run-local
```
