#!/bin/bash

# OneDash Infrastructure Setup Script
# This script handles all infrastructure setup including LocalStack, Docker services, and SQS queues

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/.tools/docker/onedash"

echo "🏗️  OneDash Infrastructure Setup"
echo "================================="

# Function to setup LocalStack AWS credentials
setup_localstack_credentials() {
    echo "🔐 Setting up LocalStack AWS credentials..."
    
    # Create a local AWS config directory if it doesn't exist
    LOCAL_AWS_DIR="$PROJECT_ROOT/.aws"
    mkdir -p "$LOCAL_AWS_DIR"
    
    # Create local AWS credentials file
    cat > "$LOCAL_AWS_DIR/credentials" << EOF
[default]
aws_access_key_id = test
aws_secret_access_key = test

[localstack]
aws_access_key_id = test
aws_secret_access_key = test
EOF
    
    # Create local AWS config file
    cat > "$LOCAL_AWS_DIR/config" << EOF
[default]
region = eu-central-1
output = json
endpoint_url = http://localhost:4566

[profile localstack]
region = eu-central-1
output = json
endpoint_url = http://localhost:4566
EOF
    
    echo "   ✅ LocalStack credentials configured in $LOCAL_AWS_DIR/"
}

# Function to run AWS CLI with LocalStack
aws_local() {
    AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test \
    aws --endpoint-url=http://localhost:4566 --region=eu-central-1 "$@"
}

# Function to check if LocalStack is running
check_localstack() {
    if curl -s http://localhost:4566/_localstack/health > /dev/null 2>&1; then
        echo "✅ LocalStack is running"
        return 0
    else
        echo "❌ LocalStack is not running"
        return 1
    fi
}

# Function to start LocalStack via Docker Compose
start_localstack() {
    echo "🚀 Starting LocalStack..."
    
    cd "$DOCKER_DIR"
    
    # Initialize kafka data directory with proper permissions
    mkdir -p ./kafka-data
    chmod -R 777 ./kafka-data
    
    # Start LocalStack service
    docker-compose up -d localstack
    
    echo "   ⏳ Waiting for LocalStack to be ready..."
    local retries=0
    while ! check_localstack && [ $retries -lt 30 ]; do
        sleep 2
        retries=$((retries + 1))
        echo "   ... waiting (attempt $retries/30)"
    done
    
    if check_localstack; then
        echo "   ✅ LocalStack is ready!"
    else
        echo "   ❌ LocalStack failed to start after 60 seconds"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# Function to create SQS queues
create_sqs_queues() {
    echo "📬 Creating SQS queues..."
    
    if ! check_localstack; then
        echo "   ❌ LocalStack is not running. Please start it first."
        return 1
    fi
    
    # Create OneDash SQS queues
    local queues=(
        "onedash-block-account-queue"
        "onedash-audit-trail-queue"
        "onedash-unblock-account-queue"
        "onedash-unlink-account-queue"
        "onedash-transfer-on-behalf-queue"
        "onedash-update-account-status-queue"
        "onedash-deactivate-loc-queue"
    )
    
    for queue in "${queues[@]}"; do
        echo "   Creating $queue..."
        if aws_local sqs create-queue \
            --queue-name "$queue" \
            --attributes VisibilityTimeout=30,MessageRetentionPeriod=1209600 \
            > /dev/null 2>&1; then
            echo "   ✅ Queue $queue created successfully"
        else
            # Check if queue already exists
            if aws_local sqs get-queue-url --queue-name "$queue" > /dev/null 2>&1; then
                echo "   ✅ Queue $queue already exists"
            else
                echo "   ❌ Failed to create queue $queue"
                aws_local sqs create-queue --queue-name "$queue" --attributes VisibilityTimeout=30,MessageRetentionPeriod=1209600
            fi
        fi
    done
    
    echo "   ✅ SQS queues created successfully!"
}

# Function to create S3 buckets
create_s3_buckets() {
    echo "🪣 Creating S3 buckets..."
    
    if ! check_localstack; then
        echo "   ❌ LocalStack is not running. Please start it first."
        return 1
    fi
    
    local buckets=(
        "idbank-dev-backend-onedash-api"
    )
    
    for bucket in "${buckets[@]}"; do
        echo "   Creating $bucket..."
        aws_local s3 mb "s3://$bucket" > /dev/null 2>&1 || echo "   (Bucket $bucket may already exist)"
    done
    
    echo "   ✅ S3 buckets created successfully!"
}

# Function to start all Docker services
start_docker_services() {
    echo "🐳 Starting Docker services..."
    
    cd "$DOCKER_DIR"
    
    # Initialize kafka data directory with proper permissions
    mkdir -p ./kafka-data
    chmod -R 777 ./kafka-data
    
    # Start all services
    docker-compose up -d
    
    echo "   ⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    echo "   📊 Service Status:"
    docker-compose ps
    
    cd "$PROJECT_ROOT"
    echo "   ✅ Docker services started!"
}

# Function to stop all services
stop_services() {
    echo "🛑 Stopping all services..."
    
    cd "$DOCKER_DIR"
    docker-compose down
    cd "$PROJECT_ROOT"
    
    echo "   ✅ All services stopped!"
}

# Function to show service status
show_status() {
    echo "📊 Infrastructure Status"
    echo "========================"
    
    echo ""
    echo "🐳 Docker Services:"
    cd "$DOCKER_DIR"
    docker-compose ps
    cd "$PROJECT_ROOT"
    
    echo ""
    echo "🔧 LocalStack:"
    if check_localstack; then
        echo "   ✅ LocalStack is running at http://localhost:4566"
        
        echo ""
        echo "📬 SQS Queues:"
        aws_local sqs list-queues 2>/dev/null | jq -r '.QueueUrls[]?' | while read -r url; do
            if [ -n "$url" ]; then
                queue_name=$(basename "$url")
                echo "   📨 $queue_name"
            fi
        done
        
        echo ""
        echo "🪣 S3 Buckets:"
        aws_local s3 ls 2>/dev/null | while read -r line; do
            if [ -n "$line" ]; then
                bucket_name=$(echo "$line" | awk '{print $3}')
                echo "   🪣 $bucket_name"
            fi
        done
    else
        echo "   ❌ LocalStack is not running"
    fi
}

# Function to check queue status
check_queue_status() {
    local queue_name="${1:-onedash-block-account-queue}"
    
    echo "🔍 Checking queue status: $queue_name"
    
    if ! check_localstack; then
        echo "   ❌ LocalStack is not running"
        return 1
    fi
    
    local queue_url="http://sqs.eu-central-1.localhost.localstack.cloud:4566/************/$queue_name"
    
    echo "📊 Queue Attributes:"
    local attributes=$(aws_local sqs get-queue-attributes \
        --queue-url "$queue_url" \
        --attribute-names All)
    
    echo "$attributes" | jq '.Attributes'
    
    # Extract message count from attributes for better debugging
    local approx_messages=$(echo "$attributes" | jq -r '.Attributes.ApproximateNumberOfMessages // "0"')
    local approx_not_visible=$(echo "$attributes" | jq -r '.Attributes.ApproximateNumberOfMessagesNotVisible // "0"')
    
    echo ""
    echo "📊 Queue Summary:"
    echo "   📬 Available messages: $approx_messages"
    echo "   👁️  In-flight messages: $approx_not_visible"
    
    echo ""
    echo "📬 Checking for messages..."
    local messages=$(aws_local sqs receive-message \
        --queue-url "$queue_url" \
        --max-number-of-messages 10 \
        --visibility-timeout 1 \
        --wait-time-seconds 2)
    
    # Check if messages is empty or null
    if [ -z "$messages" ] || [ "$messages" == "null" ]; then
        echo "📭 No messages in queue"
    else
        local message_count=$(echo "$messages" | jq -r '.Messages | length // 0')
        if [ "$message_count" == "0" ]; then
            echo "📭 No messages in queue"
        else
            echo "📨 Found $message_count message(s):"
            echo "$messages" | jq '.Messages[] | {MessageId, Body}'
        fi
    fi
}

# Function to test queue by sending a message
test_queue() {
    local queue_name="${1:-onedash-block-account-queue}"
    
    echo "📤 Testing queue: $queue_name"
    
    if ! check_localstack; then
        echo "   ❌ LocalStack is not running"
        return 1
    fi
    
    local queue_url="http://sqs.eu-central-1.localhost.localstack.cloud:4566/************/$queue_name"
    
    # Create test message payload
    local test_message='{
      "ticketID": "TICKET-123-TEST",
      "payload": {
        "AccountID": "ACC-TEST-123456",
        "IdempotencyKey": "test-idem-key-'$(date +%s)'",
        "HoldCodes": ["HOLD_001", "HOLD_002"],
        "UpdatedBy": "test-user",
        "TicketID": "TICKET-123-TEST",
        "SafeID": "SAFE-TEST-123",
        "IsSendNotification": true
      }
    }'
    
    echo "📨 Sending test message..."
    local result=$(aws_local sqs send-message \
        --queue-url "$queue_url" \
        --message-body "$test_message")
    
    local message_id=$(echo "$result" | jq -r '.MessageId')
    echo "   ✅ Message sent successfully! ID: $message_id"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start-localstack   Start LocalStack only"
    echo "  start-docker       Start all Docker services"
    echo "  start              Start all services (Docker + LocalStack setup)"
    echo "  stop               Stop all services"
    echo "  create-queues      Create SQS queues in LocalStack"
    echo "  create-buckets     Create S3 buckets in LocalStack"
    echo "  setup-aws          Setup LocalStack AWS credentials"
    echo "  status             Show infrastructure status"
    echo "  check-queue [name] Check specific queue status"
    echo "  test-queue [name]  Test queue by sending a message"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                              # Start everything"
    echo "  $0 create-queues                      # Create all SQS queues"
    echo "  $0 check-queue onedash-block-account-queue  # Check specific queue"
    echo "  $0 test-queue onedash-block-account-queue   # Test specific queue"
}

# Handle command line arguments
case "${1:-help}" in
    "start-localstack")
        setup_localstack_credentials
        start_localstack
        ;;
    "start-docker")
        start_docker_services
        ;;
    "start")
        setup_localstack_credentials
        start_docker_services
        sleep 5
        create_sqs_queues
        create_s3_buckets
        echo ""
        echo "🎉 Infrastructure setup complete!"
        echo "   LocalStack: http://localhost:4566"
        echo "   MySQL: localhost:30001"
        echo "   Redis: localhost:30002"
        echo "   Kafka: localhost:9092"
        echo "   Kafka UI: http://localhost:8077"
        ;;
    "stop")
        stop_services
        ;;
    "create-queues")
        create_sqs_queues
        ;;
    "create-buckets")
        create_s3_buckets
        ;;
    "setup-aws")
        setup_localstack_credentials
        ;;
    "status")
        show_status
        ;;
    "check-queue")
        check_queue_status "$2"
        ;;
    "test-queue")
        test_queue "$2"
        ;;
    "help")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
