#!/bin/bash

# OneDash Environment Switcher
# This script switches between different development environments (LocalStack vs Development)

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "🔄 OneDash Environment Switcher"
echo "================================"

# Function to export LocalStack environment variables
export_localstack_env() {
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    export AWS_SESSION_TOKEN=""
    export AWS_DEFAULT_REGION="eu-central-1"
    export AWS_ENDPOINT_URL="http://localhost:4566"
    
    # Disable AWS SSO and other credential sources that might interfere
    export AWS_PROFILE=""
    export AWS_CONFIG_FILE=""
    export AWS_SHARED_CREDENTIALS_FILE=""
    export AWS_SDK_LOAD_CONFIG=false
    
    # OneDash specific environment variables
    export SERVICE_CONF="$PROJECT_ROOT/config_files/service-conf.local.json"
    export SECRET_CONF="$PROJECT_ROOT/vault/secrets/"
    export SERVICE_NAME="onedash-be"
    export ALL_DEPRECATED_INITS_MIGRATED=true
    export MYSQL_HOST=localhost
    export SERVICE_RUNTIME_ENV=local
    
    # LocalStack Configuration
    export LOCALSTACK_AWS_ACCESS_KEY_ID=test
    export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test
    export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1
    export LOCALSTACK_ENDPOINT_URL=http://localhost:4566
}

# Function to export Development environment variables
export_development_env() {
    export AWS_ACCESS_KEY_ID="0"
    export AWS_SECRET_ACCESS_KEY="0"
    export AWS_SESSION_TOKEN="0"
    export AWS_DEFAULT_REGION="ap-southeast-1"
    unset AWS_ENDPOINT_URL
    
    # Reset AWS configurations to use external services
    unset AWS_PROFILE
    unset AWS_CONFIG_FILE
    unset AWS_SHARED_CREDENTIALS_FILE
    export AWS_SDK_LOAD_CONFIG=true
    
    # OneDash specific environment variables
    export SERVICE_CONF="$PROJECT_ROOT/config_files/service-conf.json"
    export SECRET_CONF="$PROJECT_ROOT/vault/secrets/"
    export SERVICE_NAME="onedash-be"
    export ALL_DEPRECATED_INITS_MIGRATED=true
    export MYSQL_HOST=localhost
    export SERVICE_RUNTIME_ENV=dev
    
    # Clear LocalStack specific configurations
    unset LOCALSTACK_AWS_ACCESS_KEY_ID
    unset LOCALSTACK_AWS_SECRET_ACCESS_KEY
    unset LOCALSTACK_AWS_DEFAULT_REGION
    unset LOCALSTACK_ENDPOINT_URL
}

# Function to switch to LocalStack configuration
switch_to_localstack() {
    echo "🐳 Switching to LocalStack configuration..."
    
    # Copy LocalStack environment configuration
    if [ -f "$SCRIPT_DIR/.env.local" ]; then
        cp "$SCRIPT_DIR/.env.local" "$PROJECT_ROOT/.env"
        echo "   ✅ Copied .env.local to .env"
    else
        echo "   ❌ .env.local file not found"
        return 1
    fi
    
    # Clean up AWS SSO cache to prevent conflicts
    if [ -d "$HOME/.aws/sso" ]; then
        rm -rf "$HOME/.aws/sso"
        echo "   🧹 Cleaned up AWS SSO cache"
    fi
    
    # Export LocalStack environment variables
    export_localstack_env
    
    # Create .env file for easy sourcing
    cat > "$PROJECT_ROOT/.env" << EOF
# LocalStack Development Environment
# This file configures the application to use LocalStack for all AWS services

current_path := \$(shell pwd)

# Use LocalStack configuration
export SERVICE_CONF=\$(current_path)/config_files/service-conf.local.json

export SECRET_CONF=\$(current_path)/vault/secrets/
export SERVICE_NAME=onedash-be
export ALL_DEPRECATED_INITS_MIGRATED=true
export MYSQL_HOST=localhost
export SERVICE_RUNTIME_ENV=local

# AWS Configuration (LocalStack)
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_SESSION_TOKEN=
export AWS_DEFAULT_REGION=eu-central-1
export AWS_ENDPOINT_URL=http://localhost:4566

# Disable AWS SSO and other credential sources that might interfere
export AWS_PROFILE=
export AWS_CONFIG_FILE=
export AWS_SHARED_CREDENTIALS_FILE=
export AWS_SDK_LOAD_CONFIG=false

# LocalStack Configuration (for local development)
export LOCALSTACK_AWS_ACCESS_KEY_ID=test
export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test
export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1
export LOCALSTACK_ENDPOINT_URL=http://localhost:4566
EOF
    
    echo "   ✅ Environment variables exported"
    echo "   ✅ .env file updated"
    echo ""
    echo "🎯 LocalStack Configuration Active"
    echo "   Config file: config_files/service-conf.local.json"
    echo "   Environment: LocalStack"
    echo "   All SQS queues will use LocalStack at http://localhost:4566"
    echo "   AWS SSO disabled, using static test credentials"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Start infrastructure: ./.tools/env/setup-infra.sh start"
    echo "   2. Source environment: source .env"
    echo "   3. Run your application: make run-local"
}

# Function to switch to Development configuration
switch_to_development() {
    echo "☁️  Switching to Development configuration..."
    
    # Copy development environment configuration
    if [ -f "$SCRIPT_DIR/.env.dev" ]; then
        cp "$SCRIPT_DIR/.env.dev" "$PROJECT_ROOT/.env"
        echo "   ✅ Copied .env.dev to .env"
    else
        echo "   ❌ .env.dev file not found"
        return 1
    fi
    
    # Export Development environment variables
    export_development_env
    
    # Create .env file for easy sourcing
    cat > "$PROJECT_ROOT/.env" << EOF
# Development Environment
# This file configures the application to use external AWS services

current_path := \$(shell pwd)

# Use development configuration with external services
export SERVICE_CONF=\$(current_path)/config_files/service-conf.json

export SECRET_CONF=\$(current_path)/vault/secrets/
export SERVICE_NAME=onedash-be
export ALL_DEPRECATED_INITS_MIGRATED=true
export MYSQL_HOST=localhost
export SERVICE_RUNTIME_ENV=dev

# AWS Configuration
# For production, use real AWS credentials
export AWS_ACCESS_KEY_ID=0
export AWS_SECRET_ACCESS_KEY=0
export AWS_SESSION_TOKEN=0

# LocalStack Configuration (for reference, not used in dev mode)
export LOCALSTACK_AWS_ACCESS_KEY_ID=test
export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test
export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1
export LOCALSTACK_ENDPOINT_URL=http://localhost:4566
EOF
    
    echo "   ✅ Environment variables exported"
    echo "   ✅ .env file updated"
    echo ""
    echo "🎯 Development Configuration Active"
    echo "   Config file: config_files/service-conf.json"
    echo "   Environment: Development"
    echo "   Uses external AWS and other services"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Start local services: ./.tools/env/setup-infra.sh start-docker"
    echo "   2. Source environment: source .env"
    echo "   3. Run your application: make run-local"
    echo "   4. Make sure your AWS credentials are properly configured"
}

# Function to show current configuration status
show_status() {
    echo "📊 Current Configuration Status"
    echo "==============================="
    echo ""
    
    # Check which config file is being used
    if [ -f "$PROJECT_ROOT/.env" ]; then
        echo "📄 Current .env file:"
        if grep -q "service-conf.local.json" "$PROJECT_ROOT/.env" 2>/dev/null; then
            echo "   🐳 LocalStack Configuration (service-conf.local.json)"
        elif grep -q "service-conf.json" "$PROJECT_ROOT/.env" 2>/dev/null; then
            echo "   ☁️  Development Configuration (service-conf.json)"
        else
            echo "   ❓ Unknown configuration"
        fi
    else
        echo "   ❌ No .env file found"
    fi
    
    echo ""
    echo "🔧 Current Environment Variables:"
    echo "   AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-<not set>}"
    echo "   AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-<not set>}"
    echo "   AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-<not set>}"
    echo "   AWS_ENDPOINT_URL: ${AWS_ENDPOINT_URL:-<not set>}"
    echo "   AWS_PROFILE: ${AWS_PROFILE:-<not set>}"
    echo "   AWS_SDK_LOAD_CONFIG: ${AWS_SDK_LOAD_CONFIG:-<not set>}"
    echo "   SERVICE_CONF: ${SERVICE_CONF:-<not set>}"
    echo "   SERVICE_RUNTIME_ENV: ${SERVICE_RUNTIME_ENV:-<not set>}"
    
    echo ""
    echo "📁 Configuration Files:"
    if [ -f "$PROJECT_ROOT/config_files/service-conf.local.json" ]; then
        echo "   ✅ service-conf.local.json (LocalStack)"
    else
        echo "   ❌ service-conf.local.json (LocalStack) - missing"
    fi
    
    if [ -f "$PROJECT_ROOT/config_files/service-conf.json" ]; then
        echo "   ✅ service-conf.json (Development)"
    else
        echo "   ❌ service-conf.json (Development) - missing"
    fi
    
    echo ""
    echo "🔌 Service Connectivity:"
    # Check LocalStack
    if curl -s http://localhost:4566/_localstack/health > /dev/null 2>&1; then
        echo "   ✅ LocalStack: Running at http://localhost:4566"
    else
        echo "   ❌ LocalStack: Not running"
    fi
    
    # Check MySQL
    if nc -z localhost 30001 2>/dev/null; then
        echo "   ✅ MySQL: Running at localhost:30001"
    else
        echo "   ❌ MySQL: Not running"
    fi
    
    # Check Redis
    if nc -z localhost 30002 2>/dev/null; then
        echo "   ✅ Redis: Running at localhost:30002"
    else
        echo "   ❌ Redis: Not running"
    fi
}

# Function to generate environment export commands
generate_env_export() {
    local env_type="${1:-localstack}"
    
    echo "# OneDash Environment Variables for $env_type"
    echo "# Copy and paste these commands to set up your environment"
    echo ""
    
    if [ "$env_type" = "localstack" ]; then
        echo "export AWS_ACCESS_KEY_ID=test"
        echo "export AWS_SECRET_ACCESS_KEY=test"
        echo "export AWS_SESSION_TOKEN="
        echo "export AWS_DEFAULT_REGION=eu-central-1"
        echo "export AWS_ENDPOINT_URL=http://localhost:4566"
        echo "export AWS_PROFILE="
        echo "export AWS_CONFIG_FILE="
        echo "export AWS_SHARED_CREDENTIALS_FILE="
        echo "export AWS_SDK_LOAD_CONFIG=false"
        echo "export SERVICE_CONF=$PROJECT_ROOT/config_files/service-conf.local.json"
        echo "export SERVICE_RUNTIME_ENV=local"
        echo "export LOCALSTACK_AWS_ACCESS_KEY_ID=test"
        echo "export LOCALSTACK_AWS_SECRET_ACCESS_KEY=test"
        echo "export LOCALSTACK_AWS_DEFAULT_REGION=eu-central-1"
        echo "export LOCALSTACK_ENDPOINT_URL=http://localhost:4566"
    else
        echo "export AWS_ACCESS_KEY_ID=0"
        echo "export AWS_SECRET_ACCESS_KEY=0"
        echo "export AWS_SESSION_TOKEN=0"
        echo "export AWS_DEFAULT_REGION=ap-southeast-1"
        echo "unset AWS_ENDPOINT_URL"
        echo "unset AWS_PROFILE"
        echo "unset AWS_CONFIG_FILE"
        echo "unset AWS_SHARED_CREDENTIALS_FILE"
        echo "export AWS_SDK_LOAD_CONFIG=true"
        echo "export SERVICE_CONF=$PROJECT_ROOT/config_files/service-conf.json"
        echo "export SERVICE_RUNTIME_ENV=dev"
        echo "unset LOCALSTACK_AWS_ACCESS_KEY_ID"
        echo "unset LOCALSTACK_AWS_SECRET_ACCESS_KEY"
        echo "unset LOCALSTACK_AWS_DEFAULT_REGION"
        echo "unset LOCALSTACK_ENDPOINT_URL"
    fi
    
    echo ""
    echo "export SECRET_CONF=$PROJECT_ROOT/vault/secrets/"
    echo "export SERVICE_NAME=onedash-be"
    echo "export ALL_DEPRECATED_INITS_MIGRATED=true"
    echo "export MYSQL_HOST=localhost"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  localstack            Switch to LocalStack configuration"
    echo "  development           Switch to Development configuration"
    echo "  dev                   Alias for development"
    echo "  status                Show current configuration status"
    echo "  export [env]          Generate environment export commands"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 localstack         # Switch to LocalStack"
    echo "  $0 development        # Switch to Development"
    echo "  $0 status             # Show current status"
    echo "  $0 export localstack  # Generate LocalStack export commands"
    echo "  $0 export development # Generate Development export commands"
    echo ""
    echo "After switching environments, you can:"
    echo "  source .env                    # Load environment variables"
    echo "  ./.tools/env/setup-infra.sh start  # Start infrastructure"
}

# Handle command line arguments
case "${1:-help}" in
    "localstack")
        switch_to_localstack
        ;;
    "development"|"dev")
        switch_to_development
        ;;
    "status")
        show_status
        ;;
    "export")
        generate_env_export "$2"
        ;;
    "help")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
