# Draft Ticket Functionality Analysis

## Executive Summary

After analyzing the Go backend codebase, **the draft ticket functionality is already fully implemented and ready for use**. The system supports creating tickets as drafts and transitioning them to submitted status through the existing API endpoints.

## Current Implementation Status

### ✅ **FULLY IMPLEMENTED** - No Backend Changes Required

The codebase already contains complete draft functionality:

1. **Draft Status**: `TicketStatusInDraft = 8` is defined in `constants/ticket_status.go`
2. **Draft Actions**: Both `ActionMakerDraft` and `ActionMakerSubmitDraft` constants exist
3. **Create Draft Logic**: Implemented in `pkg/logic/create_ticket.go`
4. **Update Draft Logic**: Implemented in `pkg/logic/update_ticket.go`
5. **API Endpoints**: Existing `/api/v1/ticket` (POST) and `/api/v1/ticket/{id}` (PUT) handle drafts

## Existing Draft Functionality

### 1. Creating a Draft Ticket

**Endpoint**: `POST /api/v1/ticket`

**Request Body**:
```json
{
  "data": {
    "payload": { /* ticket data */ },
    "description": "Draft ticket description"
  },
  "elementID": 123,
  "priorityID": 1,
  "source": "ONEDASH",
  "action": "MAKER_DRAFT",
  "note": "Draft notes",
  "ticketRequestorID": 456,
  "caseCategory": "Category",
  "caseSubcategory": "Subcategory",
  "domainID": "DOMAIN",
  "channel": "WEB"
}
```

**Key Implementation Details**:
- When `action = "MAKER_DRAFT"`, ticket gets `TicketStatusInDraft` status
- Draft tickets are automatically assigned to the creator (`assigneeUserID = userID`)
- Audit trail records "Ticket draft created" message

### 2. Updating a Draft Ticket

**Endpoint**: `PUT /api/v1/ticket/{id}`

**Request Body**:
```json
{
  "id": 123,
  "data": {
    "payload": { /* updated ticket data */ },
    "description": "Updated draft description"
  },
  "nextStatusID": 8,
  "action": "MAKER_DRAFT",
  "isUpdateData": true,
  "note": "Updated draft notes"
}
```

### 3. Submitting a Draft Ticket

**Endpoint**: `PUT /api/v1/ticket/{id}`

**Request Body**:
```json
{
  "id": 123,
  "data": {
    "payload": { /* final ticket data */ },
    "description": "Final ticket description"
  },
  "nextStatusID": 2,
  "action": "MAKER_SUBMIT_DRAFT",
  "isUpdateData": true,
  "note": "Submitting draft"
}
```

## Code Implementation Details

### Draft Creation Logic
<augment_code_snippet path="pkg/logic/create_ticket.go" mode="EXCERPT">
````go
if req.Action == constants.ActionMakerSubmit {
    ticketStatus = chains[constants.ActionMakerSubmit].NextStatusID
} else if req.Action == constants.ActionMakerDraft {
    ticketStatus = chains[constants.ActionMakerDraft].NextStatusID
    assigneeUserID = sql.NullInt64{Int64: userID, Valid: true}
}
````
</augment_code_snippet>

### Draft Update Logic
<augment_code_snippet path="pkg/logic/update_ticket.go" mode="EXCERPT">
````go
if req.Action == constants.ActionMakerDraft {
    updated.AssigneeUserID = sql.NullInt64{Int64: userID, Valid: true}
}
````
</augment_code_snippet>

### Audit Trail Support
<augment_code_snippet path="pkg/logic/update_ticket.go" mode="EXCERPT">
````go
case constants.ActionMakerDraft, constants.ActionMakerSubmitDraft:
    title = "Ticket draft updated"
    description = fmt.Sprintf("User %s updated ticket draft with id %d", userName, dto.ID)
````
</augment_code_snippet>

## Required Configuration

### Ticket Chain Configuration

The system requires proper ticket chain configuration in the database for each element. The chains must define:

1. **MAKER_DRAFT** action: `current_status_id = 0` → `next_status_id = 8` (Draft)
2. **MAKER_SUBMIT_DRAFT** action: `current_status_id = 8` → `next_status_id = 2` (In Progress Maker)

Example SQL for ticket chain setup:
```sql
INSERT INTO ticket_chain (current_status_id, next_status_id, element_id, action_name, bitwise_required)
VALUES 
(0, 8, {element_id}, 'MAKER_DRAFT', 2),
(8, 2, {element_id}, 'MAKER_SUBMIT_DRAFT', 2);
```

## Testing the Implementation

### Test Case 1: Create Draft
```bash
curl -X POST /api/v1/ticket \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "data": {"payload": {}, "description": "Test draft"},
    "elementID": 1,
    "priorityID": 1,
    "source": "ONEDASH",
    "action": "MAKER_DRAFT"
  }'
```

### Test Case 2: Update Draft
```bash
curl -X PUT /api/v1/ticket/{ticket_id} \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "id": {ticket_id},
    "data": {"payload": {}, "description": "Updated draft"},
    "nextStatusID": 8,
    "action": "MAKER_DRAFT",
    "isUpdateData": true
  }'
```

### Test Case 3: Submit Draft
```bash
curl -X PUT /api/v1/ticket/{ticket_id} \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "id": {ticket_id},
    "data": {"payload": {}, "description": "Final submission"},
    "nextStatusID": 2,
    "action": "MAKER_SUBMIT_DRAFT",
    "isUpdateData": true
  }'
```

## Conclusion

**No backend development work is required.** The draft ticket functionality is completely implemented and ready for use. The system supports:

- ✅ Creating tickets as drafts
- ✅ Updating draft tickets
- ✅ Submitting draft tickets
- ✅ Proper status transitions
- ✅ Audit trail logging
- ✅ User assignment for drafts

The only requirement is ensuring proper ticket chain configuration in the database for the specific elements that need draft functionality.
