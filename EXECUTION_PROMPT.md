# Hold and Resume Case Implementation - Execution Prompt

## OBJECTIVE
You are tasked with implementing the "Hold and Resume Case" feature by following the implementation plan in `HOLD_RESUME_TICKET_CHAIN_IMPLEMENTATION_PLAN.md`. This implementation leverages the existing ticket chain system instead of creating new endpoints, ensuring consistency with the current architecture.

## EXECUTION INSTRUCTIONS

### 1. READ THE IMPLEMENTATION PLAN
First, carefully read and understand the complete implementation plan in `HOLD_RESUME_TICKET_CHAIN_IMPLEMENTATION_PLAN.md`. This plan uses the existing ticket chain system to add hold/resume functionality through the current `UpdateTicket` workflow.

### 2. IMPLEMENTATION SEQUENCE
Follow this exact sequence to ensure proper implementation:

#### Phase 1: Database Schema (Priority: CRITICAL)
1. **Create migration files:**
   - `db/mysql/deploy/029-hold-resume-case.sql` - Add columns to tickets table
   - `db/mysql/deploy/030-hold-resume-ticket-chains.sql` - Add ticket chain entries
   - `db/mysql/revert/029-hold-resume-case.sql` - Revert scripts
   - `db/mysql/revert/030-hold-resume-ticket-chains.sql` - Revert scripts
   - `db/mysql/verify/029-hold-resume-case.sql` - Verification scripts
   - `db/mysql/verify/030-hold-resume-ticket-chains.sql` - Verification scripts
   - Update `db/mysql/sqitch.plan` with new entries

#### Phase 2: Constants and Configuration (Priority: HIGH)
2. **Update constants files:**
   - Add hold reason constants to `constants/constants.go`
   - Add audit event types to `constants/audit_trail.go`

#### Phase 3: Storage Layer (Priority: HIGH)
3. **Update storage layer:**
   - Add new fields to `TicketDTO` struct in `storage/dto.go`
   - Update all SQL queries in `storage/tickets.go` to include new columns:
     - `GetTicketByID` function
     - `CreateTicket` function  
     - `GetTicketList` function
     - Update all corresponding `Scan` calls

#### Phase 4: API Layer (Priority: HIGH)
4. **Update API definitions:**
   - Modify `UpdateTicketRequest` message in `api/service.proto`
   - Modify `Ticket` message in `api/service.proto`
   - Update corresponding Go structs in `api/service.api.go`

#### Phase 5: Business Logic (Priority: CRITICAL)
5. **Extend existing business logic:**
   - Modify `pkg/logic/update_ticket_status.go` to handle `HOLD_TICKET` and `RESUME_TICKET` actions
   - Add validation for hold reasons and remarks
   - Implement SLA recalculation logic
   - Add proper audit trail creation

### 3. CRITICAL REQUIREMENTS

#### Database Requirements
- **MUST** add all 6 new columns to tickets table: `hold_reason`, `hold_remarks`, `on_hold_at`, `resumed_at`, `total_hold_duration_sec`, `original_deadline_time`
- **MUST** create ticket chain entries for both `HOLD_TICKET` and `RESUME_TICKET` actions
- **MUST** use correct bitwise values: 8 for hold, 16 for resume
- **MUST** create proper indexes for performance

#### Business Logic Requirements
- **MUST** validate hold reason is required for hold action
- **MUST** validate remarks are required when reason is "Others"
- **MUST** store original deadline before first hold
- **MUST** calculate hold duration and adjust deadline on resume
- **MUST** create proper audit trail entries with specified descriptions
- **MUST** use database transactions for consistency

#### Integration Requirements
- **MUST** extend existing `UpdateTicket` functionality, not create new endpoints
- **MUST** leverage existing ticket chain system for status transitions
- **MUST** use existing permission system with bitwise values
- **MUST** integrate with existing audit trail system

### 4. VALIDATION CHECKLIST

After implementation, verify:
- [ ] Database migrations run successfully
- [ ] Ticket chain entries exist for all elements with ticketing enabled
- [ ] Hold action validates reason and remarks correctly
- [ ] Resume action calculates SLA correctly
- [ ] Audit trail entries are created with proper descriptions
- [ ] All SQL queries include new columns
- [ ] API structs include new fields with proper validation tags

### 5. CONSTRAINTS

#### Strict Adherence
- **DO NOT** create new API endpoints
- **DO NOT** modify the ticket chain table structure
- **DO NOT** change existing function signatures unnecessarily
- **MUST** follow existing code patterns and conventions
- **MUST** use existing error handling patterns

#### Preserve Existing Functionality
- **MUST** ensure existing ticket update functionality continues to work
- **MUST** maintain backward compatibility
- **MUST** not break existing ticket chain logic

### 6. SUCCESS CRITERIA

The implementation is successful when:
1. Database migrations execute without errors
2. Ticket chain entries are created for hold/resume actions
3. Frontend can see hold/resume actions in available actions list
4. Hold action properly validates and stores hold information
5. Resume action correctly calculates new deadline
6. Audit trail shows proper hold/resume events
7. All existing ticket functionality remains intact

### 7. EXECUTION COMMAND

Execute the implementation by reading the plan file and implementing each component according to the file-by-file implementation plan. Focus on extending existing functionality rather than creating new components.

**Start by reading:** `HOLD_RESUME_TICKET_CHAIN_IMPLEMENTATION_PLAN.md`

**Then implement in this order:**
1. Database schema changes
2. Constants updates  
3. Storage layer updates
4. API layer updates
5. Business logic extensions

Remember: This approach leverages the existing ticket chain system that the frontend already understands, making it a seamless integration that requires minimal frontend changes.
