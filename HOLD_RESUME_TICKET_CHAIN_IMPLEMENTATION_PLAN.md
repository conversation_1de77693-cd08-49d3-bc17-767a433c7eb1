# Hold and Resume Case Feature - Ticket Chain Implementation Plan

## Overview
This implementation leverages the existing ticket chain system to add hold and resume functionality. Instead of creating new endpoints, we'll extend the existing `UpdateTicket` functionality with new actions that integrate seamlessly with the current permission system and frontend workflow.

## Benefits of Ticket Chain Approach
- **Consistency**: Uses existing ticket chain system that frontend already understands
- **Permissions**: Leverages existing bitwise permission system
- **UI Integration**: Frontend automatically gets available actions based on status and permissions
- **Audit Trail**: Uses existing audit trail system
- **Status Transitions**: Proper status management through ticket chain
- **Reduced Code**: Extends existing functionality instead of creating new endpoints

## 1. Database Schema Changes

### 1.1 Tickets Table Modifications
```sql
-- Deploy onedash:029-hold-resume-case to mysql
BEGIN;

-- Add hold-related columns to tickets table
ALTER TABLE `tickets` 
    ADD COLUMN `hold_reason` VARCHAR(100) DEFAULT NULL,
    ADD COLUMN `hold_remarks` VARCHAR(100) DEFAULT NULL,
    ADD COLUMN `on_hold_at` TIMESTAMP NULL DEFAULT NULL,
    ADD COLUMN `resumed_at` TIMESTAMP NULL DEFAULT NULL,
    ADD COLUMN `total_hold_duration_sec` INT DEFAULT 0 NOT NULL,
    ADD COLUMN `original_deadline_time` TIMESTAMP NULL DEFAULT NULL;

-- Add indexes for performance
ALTER TABLE `tickets` ADD INDEX `idx_on_hold_at` (`on_hold_at`);

COMMIT;
```

### 1.2 Ticket Chain Entries
```sql
-- Deploy onedash:030-hold-resume-ticket-chains to mysql
BEGIN;

-- Add ticket chain entries for HOLD_TICKET action
-- Allow holding from active statuses (New, InProgress variants)
INSERT INTO `ticket_chain` 
    (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
SELECT 
    NOW(), 0, ts.id, 14, e.id, 'HOLD_TICKET', 8
FROM 
    elements e
CROSS JOIN 
    ticket_status ts
WHERE 
    e.has_ticketing = 1 
    AND ts.id IN (1, 2, 3, 4, 5, 10, 11, 12, 13); -- Active statuses only

-- Add ticket chain entries for RESUME_TICKET action
-- Allow resuming from on-hold (14) back to the status it was held from
INSERT INTO `ticket_chain` 
    (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
SELECT 
    NOW(), 0, 14, ts.id, e.id, 'RESUME_TICKET', 16
FROM 
    elements e
CROSS JOIN 
    ticket_status ts
WHERE 
    e.has_ticketing = 1 
    AND ts.id IN (1, 2, 3, 4, 5, 10, 11, 12, 13); -- Can resume to any active status

COMMIT;
```

## 2. Constants Updates

### 2.1 Update constants/constants.go
```go
// Add these constants to the existing file:

// Hold reason constants
const (
    HoldReasonRFICustomer = "RFI from customer"
    HoldReasonRFIDepartments = "RFI from other departments"
    HoldReasonSystemIssues = "System issues"
    HoldReasonCourtOrder = "Court order or police order"
    HoldReasonOthers = "Others"
)
```

### 2.2 Update constants/audit_trail.go
```go
// Add these event types:
const (
    CaseHeld = "CASE_HELD"
    CaseResumed = "CASE_RESUMED"
)
```

## 3. Storage Layer Updates

### 3.1 Update storage/dto.go
Add new fields to TicketDTO struct:
```go
type TicketDTO struct {
    // ... existing fields ...
    HoldReason             sql.NullString  `json:"hold_reason" db:"hold_reason"`
    HoldRemarks            sql.NullString  `json:"hold_remarks" db:"hold_remarks"`
    OnHoldAt               sql.NullTime    `json:"on_hold_at" db:"on_hold_at"`
    ResumedAt              sql.NullTime    `json:"resumed_at" db:"resumed_at"`
    TotalHoldDurationSec   sql.NullInt64   `json:"total_hold_duration_sec" db:"total_hold_duration_sec"`
    OriginalDeadlineTime   sql.NullTime    `json:"original_deadline_time" db:"original_deadline_time"`
}
```

### 3.2 Update storage/tickets.go
- Update `GetTicketByID` SELECT query to include new columns
- Update `CreateTicket` INSERT query to include new columns
- Update `GetTicketList` SELECT query to include new columns
- Update all corresponding Scan calls to include new fields

## 4. API Layer Updates

### 4.1 Update api/service.proto
```protobuf
// Update UpdateTicketRequest message
message UpdateTicketRequest {
    int64 id = 1 [(gxs.api.validate) = "required,gt=0"];
    TicketData data = 2;
    int64 nextStatusID = 3 [(gxs.api.validate) = "required,gt=0"];
    string action = 4 [(gxs.api.validate) = "required"];
    string note = 5;
    bool isUpdateData = 6;
    string targetUserID = 7;
    // Fields for hold/resume actions
    string holdReason = 8 [(gxs.api.validate) = "max=100"];
    string holdRemarks = 9 [(gxs.api.validate) = "max=100"];
}

// Update Ticket message to include hold fields
message Ticket {
    // ... existing fields ...
    string holdReason = 25;
    string holdRemarks = 26;
    string onHoldAt = 27;
    string resumedAt = 28;
    int64 totalHoldDurationSec = 29;
    string originalDeadlineTime = 30;
}
```

## 5. Business Logic Updates

### 5.1 Update pkg/logic/update_ticket_status.go
Extend the existing function to handle HOLD_TICKET and RESUME_TICKET actions:

```go
// Add validation for hold action
if req.Action == constants.ActionHoldTicket {
    // Validate reason is provided
    if req.HoldReason == "" {
        return nil, errorwrapper.Error(apiError.BadRequest, "Hold reason is required")
    }
    
    // Validate remarks when reason is "Others"
    if req.HoldReason == constants.HoldReasonOthers && req.HoldRemarks == "" {
        return nil, errorwrapper.Error(apiError.BadRequest, "Remarks are required when reason is 'Others'")
    }
    
    // Store original deadline if not already stored
    if !ticketDTO.OriginalDeadlineTime.Valid {
        originalDeadlineUpdate := `UPDATE tickets SET original_deadline_time = deadline_time WHERE id = ?`
        _, err = tx.ExecContext(ctx, originalDeadlineUpdate, req.Id)
        if err != nil {
            return nil, errorwrapper.Error(apiError.InternalServerError, "failed to store original deadline")
        }
    }
    
    // Set hold-related fields
    holdTime := time.Now()
    holdUpdate := `UPDATE tickets SET hold_reason = ?, hold_remarks = ?, on_hold_at = ? WHERE id = ?`
    _, err = tx.ExecContext(ctx, holdUpdate, req.HoldReason, req.HoldRemarks, holdTime, req.Id)
    if err != nil {
        return nil, errorwrapper.Error(apiError.InternalServerError, "failed to set hold fields")
    }
    
    // Set audit description
    auditDescription = fmt.Sprintf("Case placed on hold with no auto-resume time. Reason: %s", req.HoldReason)
    if req.HoldRemarks != "" {
        auditDescription += fmt.Sprintf(" %s", req.HoldRemarks)
    }
    auditEventType = constants.CaseHeld
    
} else if req.Action == constants.ActionResumeTicket {
    // Calculate hold duration
    var holdDuration time.Duration
    if ticketDTO.OnHoldAt.Valid {
        resumeTime := time.Now()
        holdDuration = resumeTime.Sub(ticketDTO.OnHoldAt.Time)
        
        // Recalculate deadline by adding hold duration to original deadline
        var newDeadline time.Time
        if ticketDTO.OriginalDeadlineTime.Valid {
            newDeadline = ticketDTO.OriginalDeadlineTime.Time.Add(holdDuration)
        } else {
            newDeadline = ticketDTO.DeadlineTime.Time.Add(holdDuration)
        }
        
        // Update ticket with resume information
        resumeUpdate := `UPDATE tickets SET 
            deadline_time = ?, 
            resumed_at = ?, 
            total_hold_duration_sec = ?,
            hold_reason = NULL,
            hold_remarks = NULL
            WHERE id = ?`
        _, err = tx.ExecContext(ctx, resumeUpdate, newDeadline, resumeTime, int64(holdDuration.Seconds()), req.Id)
        if err != nil {
            return nil, errorwrapper.Error(apiError.InternalServerError, "failed to resume ticket")
        }
    }
    
    auditDescription = "Case resumed"
    auditEventType = constants.CaseResumed
}
```

## 6. File-by-File Implementation Plan

### Database Files
1. **db/mysql/deploy/029-hold-resume-case.sql** (NEW) - Add columns to tickets table
2. **db/mysql/deploy/030-hold-resume-ticket-chains.sql** (NEW) - Add ticket chain entries
3. **db/mysql/revert/029-hold-resume-case.sql** (NEW) - Revert tickets table changes
4. **db/mysql/revert/030-hold-resume-ticket-chains.sql** (NEW) - Remove ticket chain entries
5. **db/mysql/verify/029-hold-resume-case.sql** (NEW) - Verify tickets table changes
6. **db/mysql/verify/030-hold-resume-ticket-chains.sql** (NEW) - Verify ticket chain entries
7. **db/mysql/sqitch.plan** (MODIFY) - Add new migration entries

### Constants
8. **constants/constants.go** (MODIFY) - Add hold reason constants
9. **constants/audit_trail.go** (MODIFY) - Add audit event types

### Storage Layer
10. **storage/dto.go** (MODIFY) - Add hold fields to TicketDTO
11. **storage/tickets.go** (MODIFY) - Update queries to include new columns

### API Layer
12. **api/service.proto** (MODIFY) - Update UpdateTicketRequest and Ticket messages
13. **api/service.api.go** (MODIFY) - Update corresponding Go structs

### Business Logic
14. **pkg/logic/update_ticket_status.go** (MODIFY) - Add hold/resume logic to existing function

## 7. Frontend Integration

The frontend will automatically receive the new actions through the existing flow:

1. **GetTicketByID** returns available actions based on ticket chain and user permissions
2. Frontend shows "Hold Case" button when `HOLD_TICKET` action is available
3. Frontend shows "Resume Case" button when `RESUME_TICKET` action is available
4. Frontend uses existing **UpdateTicket** endpoint with new action and hold-related fields

## 8. Testing Strategy

1. **Database Migration Testing**: Verify migrations run successfully and can be reverted
2. **Permission Testing**: Verify only authorized users can hold/resume cases
3. **Business Logic Testing**: Test hold reason validation, SLA recalculation
4. **Integration Testing**: Test complete flow from frontend to database
5. **Audit Trail Testing**: Verify proper audit entries are created

## 9. Deployment Steps

1. Run database migrations (029 and 030)
2. Deploy backend code changes
3. Verify ticket chain entries are created for all elements
4. Test hold/resume functionality
5. Monitor audit trail entries

This approach maintains consistency with existing architecture while adding the required hold/resume functionality through the proven ticket chain system.
