# onedash

## File structure

`cmd/onedash/main.go`: entrypoint to the service
`config_files`: contains server configuration files to set environments like _SERVICE_CONF_. These files are required
to run the service and must be copied to the docker container. An environment variable is required for the service to locate the configuration files. <br>

- `service-conf.json`: locally stored service configuration. If the service is unable to connect to the remote server to fetch configuration, it should fallback to using local configuration from this file. <br>

`deploy/docker/onedash/Dockerfile`: contains docker script to package the application in CI pipelines. <br>
`dto`: contains request and response objects <br>
`handlers`: contains service service definition and API handlers <br>
`server`: contains

- `config`: service configuration definition
- `routes`: API routes
- `serve.go`: dependency initialisation, service initialisation, service start <br>

`storage`: contains storage interface and a MYSQL implementation <br>
`go.mod` `go.sum`: go modules related files <br>

FOLLOWING FILES ARE TO PLAY WITH THE SERVICE ON LOCAL MACHINE <br>

`.env`: default environment variables for docker-compose <br>
`.golangci.yml`: local linting configuration. Run `make lint` to run linter on your code <br>
`Makefile`: to install, lint, test, build and run the service

> **_NOTE:_** The files and directories with names z\_\* must not be modified manually.

## Setup

Configure all environment variables required to run the server in `.env` file at service root. This file is used by `Makefile` to configure local environment to run the server locally. `Makefile` uses the same `.env` file to setup cloud environment to run the containerized application in cloud.

### Configuration files

Make sure that the files and the filepaths mentioned in `.env` file exist in relative to the `main.go` file at service root.

## Linting

From service root, run

> _make_

or

> _make ci_

As part of liniting, it runs both `go vet` and `golint   `. For more reference on linting, see: [golang.org/x/lint/golint](golang.org/x/lint/golint)

## Unit Testing

From service root, run

> _make test_

It runs unit tests included in the service and checks for any race condition.

## Development Environments

OneDash supports two development environments that can be easily switched:

### 1. Development (External Services)

Uses external AWS and other services for development.

```bash
cd .tools/docker/onedash/
./switch-config.sh dev
make run-local
```

### 2. LocalStack (Fully Local)

Uses LocalStack for all AWS services, enabling completely offline development.

```bash
cd .tools/docker/onedash/
./setup-localstack.sh start    # Start LocalStack
./switch-config.sh localstack  # Switch configuration
make run-local
```

### Configuration Management

The system uses environment variable pointers to switch between configurations:

- `.tools/env/.env.dev` → `service-conf.json` (External services)
- `.tools/env/.env.local` → `service-conf.local.json` (LocalStack)

Use the consolidated environment management scripts:

```bash
# Switch between configurations
./.tools/env/switch-env.sh localstack  # Use LocalStack (local AWS services)
./.tools/env/switch-env.sh dev         # Use Development (external services)
./.tools/env/switch-env.sh status      # Check current configuration

# Setup infrastructure
./.tools/env/setup-infra.sh start      # Start LocalStack and create queues
./.tools/env/setup-infra.sh logs       # View LocalStack logs
./.tools/env/setup-infra.sh stop       # Stop LocalStack
```

For detailed LocalStack documentation, see [.tools/docker/onedash/README.md](.tools/docker/onedash/README.md)

## Running Locally

From service root, run

> _make run-local_

It picks dependencies from _vendor_ directory inside the service and runs the service locally.

## Building and running containerized app

From service root, run

> _make build_ <br> > _make up_

It will update the dependencies to match with _go.mod_ file, vendor those dependencies in service _vendor_ directory, and then build the service using the vendor directory.

## Updating dependencies

Change dependencies inside `go.mod` file to the desired version. Then from the service root, run

> _make ensure-mod_
