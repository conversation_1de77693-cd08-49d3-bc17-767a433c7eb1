// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: ops.proto
package client

import (
	bytes "bytes"
	context "context"
	_go "github.com/json-iterator/go"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
	http "net/http"
)

// OpsClient makes calls to Ops service.
type OpsClient struct {
	machinery klient.RoundTripper
}

// MakeOpsClient instantiates a new OpsClient.
// Deprecated: Use NewOpsClient instead
func MakeOpsClient(initializer klient.Initializer) (*OpsClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &OpsClient{
		machinery: roundTripper,
	}, nil
}

// NewOpsClient instantiates a new OpsClient.
func NewOpsClient(baseURL string, options ...klient.Option) (*OpsClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &OpsClient{
		machinery: roundTripper,
	}, nil
}

// ValidateBulkFile is API to validate file.
func (o *OpsClient) ValidateFile(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	reqShell := (*ValidateFileRequestShell)(req)
	resShell := &ValidateFileResponseShell{}
	clientCtx := klient.MakeContext(ctx, &validateFileDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ValidateFileResponse)(resShell), err
}

// ExecuteFile is API to process file.
func (o *OpsClient) ExecuteFile(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	reqShell := (*ExecuteFileRequestShell)(req)
	resShell := &ExecuteFileResponseShell{}
	clientCtx := klient.MakeContext(ctx, &executeFileDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ExecuteFileResponse)(resShell), err
}

// ValidateBulkFile is API to validate file.
func (o *OpsClient) ValidateFileV2(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	reqShell := (*ValidateFileV2RequestShell)(req)
	resShell := &ValidateFileV2ResponseShell{}
	clientCtx := klient.MakeContext(ctx, &validateFileV2Descriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ValidateFileResponse)(resShell), err
}

// ExecuteFile is API to process file.
func (o *OpsClient) ExecuteFileV2(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	reqShell := (*ExecuteFileV2RequestShell)(req)
	resShell := &ExecuteFileV2ResponseShell{}
	clientCtx := klient.MakeContext(ctx, &executeFileV2Descriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ExecuteFileResponse)(resShell), err
}

// Get Status Validation Document
func (o *OpsClient) GetStatusDocumentV2(ctx context.Context, req *api.GetStatusRequest) (*api.GetStatusResponse, error) {
	reqShell := (*GetStatusDocumentV2RequestShell)(req)
	resShell := &GetStatusDocumentV2ResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getStatusDocumentV2Descriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetStatusResponse)(resShell), err
}

// ValidateFileRequestShell is a wrapper to make the object a klient.Request
type ValidateFileRequestShell api.ValidateFileRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (v *ValidateFileRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ops-service/validate-file"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(v)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ValidateFileResponseShell is a wrapper to make the object a klient.Request
type ValidateFileResponseShell api.ValidateFileResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (v *ValidateFileResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(v)
}

// ExecuteFileRequestShell is a wrapper to make the object a klient.Request
type ExecuteFileRequestShell api.ExecuteFileRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (e *ExecuteFileRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ops-service/execute-file"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(e)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ExecuteFileResponseShell is a wrapper to make the object a klient.Request
type ExecuteFileResponseShell api.ExecuteFileResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (e *ExecuteFileResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(e)
}

// ValidateFileV2RequestShell is a wrapper to make the object a klient.Request
type ValidateFileV2RequestShell api.ValidateFileRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (v *ValidateFileV2RequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v2/ops-service/validate-file"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(v)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ValidateFileV2ResponseShell is a wrapper to make the object a klient.Request
type ValidateFileV2ResponseShell api.ValidateFileResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (v *ValidateFileV2ResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(v)
}

// ExecuteFileV2RequestShell is a wrapper to make the object a klient.Request
type ExecuteFileV2RequestShell api.ExecuteFileRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (e *ExecuteFileV2RequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v2/ops-service/execute-file"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(e)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ExecuteFileV2ResponseShell is a wrapper to make the object a klient.Request
type ExecuteFileV2ResponseShell api.ExecuteFileResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (e *ExecuteFileV2ResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(e)
}

// GetStatusDocumentV2RequestShell is a wrapper to make the object a klient.Request
type GetStatusDocumentV2RequestShell api.GetStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetStatusDocumentV2RequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v2/ops-service/validate-file/:fileName/status"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetStatusDocumentV2ResponseShell is a wrapper to make the object a klient.Request
type GetStatusDocumentV2ResponseShell api.GetStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetStatusDocumentV2ResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

var validateFileDescriptor = klient.EndpointDescriptor{
	Name:        "ValidateFile",
	Description: "ValidateBulkFile is API to validate file.",
	Method:      "POST",
	Path:        "/api/v1/ops-service/validate-file",
}

var executeFileDescriptor = klient.EndpointDescriptor{
	Name:        "ExecuteFile",
	Description: "ExecuteFile is API to process file.",
	Method:      "POST",
	Path:        "/api/v1/ops-service/execute-file",
}

var validateFileV2Descriptor = klient.EndpointDescriptor{
	Name:        "ValidateFileV2",
	Description: "ValidateBulkFile is API to validate file.",
	Method:      "POST",
	Path:        "/api/v2/ops-service/validate-file",
}

var executeFileV2Descriptor = klient.EndpointDescriptor{
	Name:        "ExecuteFileV2",
	Description: "ExecuteFile is API to process file.",
	Method:      "POST",
	Path:        "/api/v2/ops-service/execute-file",
}

var getStatusDocumentV2Descriptor = klient.EndpointDescriptor{
	Name:        "GetStatusDocumentV2",
	Description: "Get Status Validation Document",
	Method:      "GET",
	Path:        "/api/v2/ops-service/validate-file/:fileName/status",
}
