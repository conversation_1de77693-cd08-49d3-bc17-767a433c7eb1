// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: ops.proto
package api

import (
	context "context"
)

type ValidateFileRequest struct {
	FileName string `json:"fileName,omitempty"`
	Payload  string `json:"payload,omitempty"`
	Type     string `json:"type,omitempty"`
}

type ValidateFileResponse struct {
	FileName  string `json:"fileName,omitempty"`
	Status    string `json:"status,omitempty"`
	Message   string `json:"message,omitempty"`
	ProcessID string `json:"processID,omitempty"`
}

type ExecuteFileRequest struct {
	FileName string `json:"fileName,omitempty"`
	Type     string `json:"type,omitempty"`
}

type ExecuteFileResponse struct {
	Status  string `json:"status,omitempty"`
	Message string `json:"message,omitempty"`
}

type FilePayload struct {
	Id       int64  `json:"id,omitempty"`
	FileName string `json:"fileName,omitempty"`
	Payload  string `json:"payload,omitempty"`
	Count    int64  `json:"count,omitempty"`
}

type GetStatusRequest struct {
	FileName string `json:"fileName,omitempty"`
}

type GetStatusResponse struct {
	FileName string                    `json:"fileName,omitempty"`
	Payload  string                    `json:"payload,omitempty"`
	Count    int64                     `json:"count,omitempty"`
	Status   string                    `json:"status,omitempty"`
	Result   *GetStatusResponse_Result `json:"result,omitempty"`
}

type GetStatusResponse_Result struct {
	SuccessFile *FilePayload `json:"successFile,omitempty"`
	FailedFile  *FilePayload `json:"failedFile,omitempty"`
}

type Ops interface {
	// ValidateBulkFile is API to validate file.
	ValidateFile(ctx context.Context, req *ValidateFileRequest) (*ValidateFileResponse, error)
	// ExecuteFile is API to process file.
	ExecuteFile(ctx context.Context, req *ExecuteFileRequest) (*ExecuteFileResponse, error)
	// ValidateBulkFile is API to validate file.
	ValidateFileV2(ctx context.Context, req *ValidateFileRequest) (*ValidateFileResponse, error)
	// ExecuteFile is API to process file.
	ExecuteFileV2(ctx context.Context, req *ExecuteFileRequest) (*ExecuteFileResponse, error)
	// Get Status Validation Document
	GetStatusDocumentV2(ctx context.Context, req *GetStatusRequest) (*GetStatusResponse, error)
}
