syntax = "proto3";

package onedash;

option go_package = "gitlab.super-id.net/bersama/opsce/onedash-be/api";

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "gxs/api/annotations.proto";

message ValidateFileRequest {
    string fileName = 1;
    string payload = 2;
    string type = 3;
}

message ValidateFileResponse {
    string fileName = 1;
    string status = 2;
    string message = 3;
    string processID = 4;
}

message ExecuteFileRequest {
    string fileName = 1;
    string type = 2;
}

message ExecuteFileResponse {
    string status = 1;
    string message = 2;
}

message FilePayload {
    int64 id = 1;
    string fileName = 2;
    string payload = 3;
    int64 count = 4;
}

message GetStatusRequest {
    string fileName = 1;
}

message GetStatusResponse {
    string fileName = 1;
    string payload = 2;
    int64 count = 3;
    string status = 4;
    Result result = 5;

    message Result {
        FilePayload successFile = 1;
        FilePayload failedFile = 2;
    }
}

service Ops {
    // ValidateBulkFile is API to validate file.
    rpc ValidateFile (ValidateFileRequest) returns (ValidateFileResponse) {
        option (google.api.http) = {
            post: "/api/v1/ops-service/validate-file",
            body: "*",
        };
       option (gxs.api.auth) = {
           client_identities: ["servicename.SentryPartnerT6"]
       };
    }

    // ExecuteFile is API to process file.
    rpc ExecuteFile (ExecuteFileRequest) returns (ExecuteFileResponse) {
        option (google.api.http) = {
            post: "/api/v1/ops-service/execute-file",
            body: "*",
        };
        option (gxs.api.auth) = {
            client_identities: ["servicename.SentryPartnerT6"]
        };
    }

    // ValidateBulkFile is API to validate file.
    rpc ValidateFileV2 (ValidateFileRequest) returns (ValidateFileResponse) {
        option (google.api.http) = {
            post: "/api/v2/ops-service/validate-file",
            body: "*",
        };
    }

    // ExecuteFile is API to process file.
    rpc ExecuteFileV2 (ExecuteFileRequest) returns (ExecuteFileResponse) {
        option (google.api.http) = {
            post: "/api/v2/ops-service/execute-file",
            body: "*",
        };
    }

    // Get Status Validation Document
    rpc GetStatusDocumentV2 (GetStatusRequest) returns (GetStatusResponse) {
        option (google.api.http) = {
            get: "/api/v2/ops-service/validate-file/:fileName/status"
        };
    }
}