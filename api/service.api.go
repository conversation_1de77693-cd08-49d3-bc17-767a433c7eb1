// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package api

import (
	context "context"
	time "time"
)

type TicketSource string

const (
	TicketSource_DEFAULT     TicketSource = "DEFAULT"
	TicketSource_ONEDASH     TicketSource = "ONEDASH"
	TicketSource_APPIAN      TicketSource = "APPIAN"
	TicketSource_CRM         TicketSource = "CRM"
	TicketSource_AML_SERVICE TicketSource = "AML_SERVICE"
)

// SendNotificationType
type SendNotificationType string

const (
	SendNotificationType_EMAIL      SendNotificationType = "EMAIL"
	SendNotificationType_PUSH       SendNotificationType = "PUSH"
	SendNotificationType_PUSH_INBOX SendNotificationType = "PUSH_INBOX"
)

type AccountStatus string

const (
	AccountStatus_ACTIVE  AccountStatus = "ACTIVE"
	AccountStatus_DORMANT AccountStatus = "DORMANT"
	AccountStatus_CLOSED  AccountStatus = "CLOSED"
)

type SortOrder string

const (
	SortOrder_ASC  SortOrder = "ASC"
	SortOrder_DESC SortOrder = "DESC"
)

type OptionType string

const (
	OptionType_roles        OptionType = "roles"
	OptionType_elements     OptionType = "elements"
	OptionType_permissions  OptionType = "permissions"
	OptionType_modules      OptionType = "modules"
	OptionType_reasons      OptionType = "reasons"
	OptionType_ticketFilter OptionType = "ticketFilter"
)

type IdentifierType string

const (
	IdentifierType_CIF            IdentifierType = "CIF"
	IdentifierType_ID_NUMBER      IdentifierType = "ID_NUMBER"
	IdentifierType_PHONE_NUMBER   IdentifierType = "PHONE_NUMBER"
	IdentifierType_SAFE_ID        IdentifierType = "SAFE_ID"
	IdentifierType_NAME           IdentifierType = "NAME"
	IdentifierType_ACCOUNT_NUMBER IdentifierType = "ACCOUNT_NUMBER"
)

type LogType string

const (
	LogType_LOGIN                           LogType = "LOGIN"
	LogType_INTERACTION                     LogType = "INTERACTION"
	LogType_ACTIVITY                        LogType = "ACTIVITY"
	LogType_FACIALANDLIVENESS               LogType = "FACIALANDLIVENESS"
	LogType_ECOSYSTEM                       LogType = "ECOSYSTEM"
	LogType_MFA                             LogType = "MFA"
	LogType_TRANSACTIONLIMITCHANGESHISTORY  LogType = "TRANSACTIONLIMITCHANGESHISTORY"
	LogType_MARKETINGNOTIFICATIONPREFERENCE LogType = "MARKETINGNOTIFICATIONPREFERENCE"
	LogType_OVONABUNG                       LogType = "OVONABUNG"
	LogType_AGGREGATEMFA                    LogType = "AGGREGATEMFA"
	LogType_RESETFMLC                       LogType = "RESETFMLC"
)

type Document struct {
	Id          int64  `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Url         string `json:"url,omitempty"`
	TicketID    int64  `json:"ticketID,omitempty"`
	CreatedAt   string `json:"createdAt,omitempty"`
	UpdatedAt   string `json:"updatedAt,omitempty"`
	CreatedBy   string `json:"createdBy,omitempty"`
	UpdatedBy   string `json:"updatedBy,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
}

type TicketData struct {
	Payload     interface{} `json:"payload,omitempty"`
	Description string      `json:"description,omitempty"`
	Documents   []Document  `json:"documents,omitempty"`
	Capture     interface{} `json:"capture,omitempty"`
	Preview     interface{} `json:"preview,omitempty"`
}

// CreateTicketRequest ...
type CreateTicketRequest struct {
	Data              *TicketData  `json:"data,omitempty"`
	ElementID         int64        `json:"elementID,omitempty"`
	PriorityID        int64        `json:"priorityID,omitempty"`
	Source            TicketSource `json:"source,omitempty"`
	Action            string       `json:"action,omitempty"`
	Note              string       `json:"note,omitempty"`
	TicketRequestorID int64        `json:"ticketRequestorID,omitempty"`
	CaseCategory      string       `json:"caseCategory,omitempty"`
	CaseSubcategory   string       `json:"caseSubcategory,omitempty"`
	DomainID          string       `json:"domainID,omitempty"`
	Channel           string       `json:"channel,omitempty"`
	StatusID          int64        `json:"statusID,omitempty"`
	DocumentIDs       []int64      `json:"documentIDs,omitempty"`
	ParentTicketId    int64        `json:"parentTicketId,omitempty"`
}

// CreateTicketResponse ...
type CreateTicketResponse struct {
	Id int64 `json:"id,omitempty"`
}

// UpdateTicketRequest ...
type UpdateTicketRequest struct {
	Id           int64       `json:"id,omitempty" validate:"required,gt=0"`
	Data         *TicketData `json:"data,omitempty"`
	NextStatusID int64       `json:"nextStatusID,omitempty" validate:"required,gt=0"`
	Action       string      `json:"action,omitempty" validate:"required"`
	Note         string      `json:"note,omitempty"`
	IsUpdateData bool        `json:"isUpdateData,omitempty"`
	TargetUserID string      `json:"targetUserID,omitempty"`
	HoldReason   string      `json:"holdReason,omitempty" validate:"max=100"`
	HoldRemarks  string      `json:"holdRemarks,omitempty" validate:"max=100"`
}

// UpdateTicketResponse ...
type UpdateTicketResponse struct {
	Id int64 `json:"id,omitempty"`
}

// UpdateTicketAssigneeRequest ...
type UpdateTicketAssigneeRequest struct {
	Id           int64  `json:"id,omitempty"`
	TargetUserID string `json:"targetUserID,omitempty"`
}

// UpdateTicketAssigneeResponse ...
type UpdateTicketAssigneeResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetTicketListRequest struct {
	SearchKey           string   `json:"searchKey,omitempty"`
	Offset              int64    `json:"offset,omitempty" validate:"offset"`
	Limit               int64    `json:"limit,omitempty" validate:"limit"`
	ElementID           int64    `json:"elementID,omitempty"`
	StatusID            int64    `json:"statusID,omitempty"`
	AssigneeUserID      string   `json:"assigneeUserID,omitempty"`
	ModuleID            int64    `json:"moduleID,omitempty"`
	CaseType            string   `json:"caseType,omitempty"`
	CaseSubcategory     string   `json:"caseSubcategory,omitempty"`
	PriorityID          int64    `json:"priorityID,omitempty"`
	CreatedDateStart    string   `json:"createdDateStart,omitempty"`
	CreatedDateEnd      string   `json:"createdDateEnd,omitempty"`
	DueDateStart        string   `json:"dueDateStart,omitempty"`
	DueDateEnd          string   `json:"dueDateEnd,omitempty"`
	ClosedDateStart     string   `json:"closedDateStart,omitempty"`
	ClosedDateEnd       string   `json:"closedDateEnd,omitempty"`
	ActionedUserID      string   `json:"actionedUserID,omitempty"`
	SourceSystem        string   `json:"sourceSystem,omitempty"`
	Channel             string   `json:"channel,omitempty"`
	CustomerSegmentName string   `json:"customerSegmentName,omitempty"`
	SortBy              *Sort    `json:"sortBy,omitempty" validate:"omitempty,ticket_sort"`
	CreatedOn           string   `json:"createdOn,omitempty"`
	Deadline            string   `json:"deadline,omitempty"`
	Column              []string `json:"Column,omitempty"`
}

type GetTicketListResponse struct {
	Tickets []Ticket `json:"tickets,omitempty"`
	Count   int64    `json:"count,omitempty"`
	Offset  int64    `json:"offset,omitempty"`
}

type GetTicketExportResponse struct {
	DataExport string `json:"dataExport,omitempty"`
}

type Ticket struct {
	Id                   int64        `json:"id,omitempty"`
	ElementID            int64        `json:"elementID,omitempty"`
	PriorityID           int64        `json:"priorityID,omitempty"`
	CreatedAt            string       `json:"createdAt,omitempty"`
	UpdatedAt            string       `json:"updatedAt,omitempty"`
	CreatedBy            string       `json:"createdBy,omitempty"`
	UpdatedBy            string       `json:"updatedBy,omitempty"`
	Data                 *TicketData  `json:"data,omitempty" validate:"required"`
	StatusID             int64        `json:"statusID,omitempty"`
	Source               TicketSource `json:"source,omitempty"`
	DeadlineTime         string       `json:"deadlineTime,omitempty"`
	AssigneeUserID       int64        `json:"assigneeUserID,omitempty"`
	AssigneeUserName     string       `json:"assigneeUserName,omitempty"`
	ElementName          string       `json:"elementName,omitempty"`
	PriorityName         string       `json:"priorityName,omitempty"`
	TicketRequestorName  string       `json:"ticketRequestorName,omitempty"`
	CaseCategory         string       `json:"caseCategory,omitempty"`
	CaseSubcategory      string       `json:"caseSubcategory,omitempty"`
	DomainID             string       `json:"domainID,omitempty"`
	Channel              string       `json:"channel,omitempty"`
	CustomerSegmentName  string       `json:"customerSegmentName,omitempty"`
	TicketCloseDatetime  string       `json:"ticketCloseDatetime,omitempty"`
	ActionedUsers        []string     `json:"actionedUsers,omitempty"`
	ParentTicketID       int64        `json:"parentTicketID,omitempty"`
	StatusName           string       `json:"statusName,omitempty"`
	ModuleName           string       `json:"moduleName,omitempty"`
	HoldReason           string       `json:"holdReason,omitempty"`
	HoldRemarks          string       `json:"holdRemarks,omitempty"`
	OnHoldAt             string       `json:"onHoldAt,omitempty"`
	ResumedAt            string       `json:"resumedAt,omitempty"`
	TotalHoldDurationSec int64        `json:"totalHoldDurationSec,omitempty"`
	OriginalDeadlineTime string       `json:"originalDeadlineTime,omitempty"`
}

type TicketHistory struct {
	Id           int64       `json:"id,omitempty"`
	CreatedBy    int64       `json:"createdBy,omitempty" validate:"required,gt=0"`
	CreatedAt    string      `json:"createdAt,omitempty" validate:"required"`
	Action       string      `json:"action,omitempty" validate:"required,max=255"`
	Note         string      `json:"note,omitempty" validate:"max=255"`
	TicketID     int64       `json:"ticketID,omitempty" validate:"required,gt=0"`
	Data         *TicketData `json:"data,omitempty"`
	PrevStatusID int64       `json:"prevStatusID,omitempty" validate:"required,gt=0"`
	NextStatusID int64       `json:"nextStatusID,omitempty" validate:"required,gt=0"`
}

type LinkedTicket struct {
	Id              int64  `json:"id,omitempty"`
	CreatedAt       string `json:"createdAt,omitempty"`
	CreatedBy       string `json:"createdBy,omitempty"`
	StatusID        int64  `json:"statusID,omitempty"`
	ElementID       int64  `json:"elementID,omitempty"`
	CaseCategory    string `json:"caseCategory,omitempty"`
	CaseSubcategory string `json:"caseSubcategory,omitempty"`
	ParentTicketID  int64  `json:"parentTicketID,omitempty"`
	Relationship    string `json:"relationship,omitempty"`
}

type GetTicketByIDRequest struct {
	Id int64 `json:"id,omitempty"`
}

type GetTicketByIDResponse struct {
	Ticket               *Ticket          `json:"ticket,omitempty"`
	Histories            []TicketHistory  `json:"histories,omitempty"`
	ActionsNextStatusMap map[string]int64 `json:"actionsNextStatusMap,omitempty"`
	LinkedTickets        []LinkedTicket   `json:"linkedTickets,omitempty"`
}

type CreateModuleRequest struct {
	Name         string `json:"name,omitempty"`
	Status       int32  `json:"status,omitempty"`
	HasTicketing bool   `json:"hasTicketing,omitempty"`
}

type CreateModuleResponse struct {
	Id int64 `json:"id,omitempty"`
}

type UpdateModuleRequest struct {
	Id           int64  `json:"id,omitempty"`
	Name         string `json:"name,omitempty"`
	Status       int32  `json:"status,omitempty"`
	HasTicketing bool   `json:"hasTicketing,omitempty"`
}

type UpdateModuleResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetModulesRequest struct {
	SearchKey    string `json:"searchKey,omitempty"`
	Offset       int64  `json:"offset,omitempty" validate:"offset"`
	Limit        int64  `json:"limit,omitempty" validate:"limit"`
	Name         string `json:"name,omitempty"`
	HasTicketing bool   `json:"hasTicketing,omitempty"`
}

type GetModulesResponse struct {
	Modules []Module `json:"modules,omitempty"`
	Count   int64    `json:"count,omitempty"`
	Offset  int64    `json:"offset,omitempty"`
}

type Module struct {
	Id           int64  `json:"id,omitempty"`
	Name         string `json:"name,omitempty"`
	CreatedAt    string `json:"createdAt,omitempty"`
	UpdatedAt    string `json:"updatedAt,omitempty"`
	CreatedBy    string `json:"createdBy,omitempty"`
	UpdatedBy    string `json:"updatedBy,omitempty"`
	Status       int32  `json:"status"`
	HasTicketing bool   `json:"hasTicketing,omitempty"`
}

type CreateElementRequest struct {
	Name                     string        `json:"name,omitempty"`
	ModuleID                 int64         `json:"moduleID,omitempty"`
	Code                     string        `json:"code,omitempty"`
	DefaultPriorityID        int64         `json:"defaultPriorityID,omitempty"`
	Status                   int32         `json:"status,omitempty"`
	HasTicketing             bool          `json:"hasTicketing,omitempty"`
	TicketChains             []TicketChain `json:"ticketChains,omitempty"`
	DefaultCustomerSegmentID int64         `json:"defaultCustomerSegmentID,omitempty"`
	DefaultTicketRequestorID int64         `json:"defaultTicketRequestorID,omitempty"`
}

type CreateElementResponse struct {
	Id int64 `json:"id,omitempty"`
}

type UpdateElementRequest struct {
	Id                       int64         `json:"id,omitempty"`
	Name                     string        `json:"name,omitempty"`
	ModuleID                 int64         `json:"moduleID,omitempty"`
	Code                     string        `json:"code,omitempty"`
	DefaultPriorityID        int64         `json:"defaultPriorityID,omitempty"`
	Status                   int32         `json:"status,omitempty"`
	HasTicketing             bool          `json:"hasTicketing,omitempty"`
	TicketChains             []TicketChain `json:"ticketChains,omitempty"`
	DefaultCustomerSegmentID int64         `json:"defaultCustomerSegmentID,omitempty"`
	DefaultTicketRequestorID int64         `json:"defaultTicketRequestorID,omitempty"`
}

type UpdateElementResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetElementsRequest struct {
	ModuleID     int64  `json:"moduleID,omitempty"`
	SearchKey    string `json:"searchKey,omitempty"`
	Offset       int64  `json:"offset,omitempty" validate:"offset"`
	Limit        int64  `json:"limit,omitempty" validate:"limit"`
	HasTicketing bool   `json:"hasTicketing,omitempty"`
}

type GetElementsResponse struct {
	Elements []Element `json:"elements,omitempty"`
	Count    int64     `json:"count,omitempty"`
	Offset   int64     `json:"offset,omitempty"`
}

type GetElementByIDRequest struct {
	Id int64 `json:"id,omitempty"`
}

type GetElementByIDResponse struct {
	Element *Element `json:"element,omitempty"`
}

type Element struct {
	Id                       int64  `json:"id,omitempty"`
	Name                     string `json:"name,omitempty"`
	Code                     string `json:"code,omitempty"`
	ModuleID                 int64  `json:"moduleID,omitempty"`
	DefaultPriorityID        int64  `json:"defaultPriorityID,omitempty"`
	CreatedAt                string `json:"createdAt,omitempty"`
	UpdatedAt                string `json:"updatedAt,omitempty"`
	CreatedBy                string `json:"createdBy,omitempty"`
	UpdatedBy                string `json:"updatedBy,omitempty"`
	Status                   int32  `json:"status"`
	HasTicketing             bool   `json:"hasTicketing,omitempty"`
	DefaultCustomerSegmentID int64  `json:"defaultCustomerSegmentID,omitempty"`
	DefaultTicketRequestorID int64  `json:"defaultTicketRequestorID,omitempty"`
}

type CreateTicketCommentRequest struct {
	Id      int64  `json:"id,omitempty"`
	Comment string `json:"comment,omitempty"`
}

type CreateTicketCommentResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetTicketCommentsRequest struct {
	Id int64 `json:"id,omitempty"`
}

type GetTicketCommentsResponse struct {
	Comments []TicketComment `json:"comments,omitempty"`
}

type TicketComment struct {
	Id        int64  `json:"id,omitempty"`
	Comment   string `json:"comment,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	CreatedBy int64  `json:"createdBy,omitempty"`
}

type LogAuditTrailRequest struct {
	UserID      string      `json:"userID,omitempty"`
	Name        string      `json:"name,omitempty"`
	Event       string      `json:"event,omitempty"`
	Email       string      `json:"email,omitempty"`
	Action      string      `json:"action,omitempty"`
	RelatedID   string      `json:"relatedID,omitempty"`
	Metadata    interface{} `json:"metadata,omitempty"`
	ServiceName string      `json:"serviceName,omitempty"`
	SafeID      string      `json:"safeID,omitempty"`
}

type LogAuditTrailResponse struct {
	Status string `json:"Status,omitempty"`
}

type LogAuditTrail struct {
	ID         int64       `json:"ID,omitempty"`
	Name       string      `json:"name,omitempty"`
	UserID     string      `json:"userID,omitempty"`
	Email      string      `json:"email,omitempty"`
	Event      string      `json:"event,omitempty"`
	Action     string      `json:"action,omitempty"`
	Metadata   interface{} `json:"metadata,omitempty"`
	RelatedID  string      `json:"relatedID,omitempty"`
	Service    string      `json:"service,omitempty"`
	ActionTime time.Time   `json:"actionTime,omitempty"`
	SafeID     string      `json:"safeID,omitempty"`
}

type GetLogAuditTrailsRequest struct {
	UserID         string `json:"userID,omitempty"`
	StartTime      string `json:"startTime,omitempty"`
	EndTime        string `json:"endTime,omitempty"`
	RelatedID      string `json:"relatedID,omitempty"`
	Event          string `json:"event,omitempty"`
	Action         string `json:"action,omitempty"`
	Limit          int64  `json:"limit,omitempty"`
	StartingBefore string `json:"startingBefore,omitempty"`
	EndingAfter    string `json:"endingAfter,omitempty"`
	ServiceName    string `json:"serviceName,omitempty"`
	SafeID         string `json:"safeID,omitempty"`
}

type GetLogAuditTrailsResponse struct {
	Links map[string]string `json:"Links,omitempty"`
	Logs  []LogAuditTrail   `json:"logs,omitempty"`
}

type EmailAttachment struct {
	Filename string `json:"filename,omitempty"`
	File     []byte `json:"file,omitempty"`
	Url      string `json:"url,omitempty"`
}

type Template struct {
	Id       string            `json:"id,omitempty"`
	Language string            `json:"language,omitempty"`
	Params   map[string]string `json:"params,omitempty"`
}

// SendNotificationRequest ...
type SendNotificationRequest struct {
	RecipientID      string               `json:"recipientID,omitempty"`
	Template         *Template            `json:"template,omitempty"`
	Attachments      []EmailAttachment    `json:"attachments,omitempty"`
	NotificationType SendNotificationType `json:"notificationType,omitempty"`
}

// SendNotificationResponse
type SendNotificationResponse struct {
	MessageID string `json:"messageID,omitempty"`
}

// UnlinkAccountRequest
type UnlinkAccountRequest struct {
	BillingAgreementID string `json:"billingAgreementID,omitempty"`
	SafeID             string `json:"safeID,omitempty"`
	ActionBy           string `json:"actionBy,omitempty"`
	SkipNotification   bool   `json:"skipNotification,omitempty"`
	TicketID           string `json:"ticketID,omitempty"`
}

// UnlinkAccountResponse
type UnlinkAccountResponse struct {
	Status string `json:"status,omitempty"`
}

type AccountDetail struct {
	PairingID      string `json:"pairingID,omitempty"`
	Number         string `json:"number,omitempty"`
	SwiftCode      string `json:"swiftCode,omitempty"`
	DisplayName    string `json:"displayName,omitempty"`
	BankCode       string `json:"bankCode,omitempty"`
	AccountAddress string `json:"accountAddress,omitempty"`
	IsGLAccount    bool   `json:"isGLAccount,omitempty"`
}

// PaymentRailRPP
type PaymentRailRPP struct {
	PurposeCode string `json:"purposeCode,omitempty"`
}

// PaymentRailFast
type PaymentRailFAST struct {
	PurposeCode string `json:"purposeCode,omitempty"`
}

// PaymentTransferRequest
type PaymentTransferRequest struct {
	TransferType       string           `json:"transferType,omitempty"`
	Amount             int64            `json:"amount,omitempty"`
	Currency           string           `json:"currency,omitempty"`
	SourceAccount      *AccountDetail   `json:"sourceAccount,omitempty"`
	DestinationAccount *AccountDetail   `json:"destinationAccount,omitempty"`
	Remarks            string           `json:"remarks,omitempty"`
	IdempotencyKey     string           `json:"idempotencyKey,omitempty"`
	Fast               *PaymentRailFAST `json:"fast,omitempty"`
	Rpp                *PaymentRailRPP  `json:"rpp,omitempty"`
	TransactionDomain  string           `json:"transactionDomain,omitempty"`
	TransactionType    string           `json:"transactionType,omitempty"`
	TransactionSubType string           `json:"transactionSubType,omitempty"`
	NetworkID          string           `json:"networkID,omitempty"`
	Fee                int64            `json:"fee,omitempty"`
	SendNotification   bool             `json:"sendNotification,omitempty"`
	TicketID           string           `json:"ticketID,omitempty"`
}

// PaymentTransferResponse
type PaymentTransferResponse struct {
	Status string `json:"status,omitempty"`
}

// BlockAccountRequest ...
type BlockAccountRequest struct {
	AccountID          string   `json:"AccountID,omitempty" validate:"required,account_id"`
	IdempotencyKey     string   `json:"IdempotencyKey,omitempty" validate:"required"`
	UpdatedBy          string   `json:"UpdatedBy,omitempty"`
	TicketID           string   `json:"TicketID,omitempty"`
	IsSendNotification string   `json:"IsSendNotification,omitempty"`
	HoldCodes          []string `json:"HoldCodes,omitempty"`
}

// BlockAccountResponse ...
type BlockAccountResponse struct {
	AccountID     string `json:"AccountID,omitempty"`
	Status        string `json:"Status,omitempty"`
	FailureReason string `json:"FailureReason,omitempty"`
}

// UnblockAccountRequest ...
type UnblockAccountRequest struct {
	AccountID          string   `json:"AccountID,omitempty" validate:"required,account_id"`
	IdempotencyKey     string   `json:"IdempotencyKey,omitempty" validate:"required"`
	UpdatedBy          string   `json:"UpdatedBy,omitempty"`
	TicketID           string   `json:"TicketID,omitempty"`
	IsSendNotification string   `json:"IsSendNotification,omitempty"`
	HoldCodes          []string `json:"HoldCodes,omitempty"`
}

// UnblockAccountResponse ...
type UnblockAccountResponse struct {
	AccountID     string `json:"AccountID,omitempty"`
	Status        string `json:"Status,omitempty"`
	FailureReason string `json:"FailureReason,omitempty"`
}

type DeactivateLOCRequest struct {
	IdempotencyKey string            `json:"idempotencyKey,omitempty"`
	SafeID         string            `json:"safeID,omitempty"`
	LocAccountID   string            `json:"locAccountID,omitempty"`
	Metadata       map[string]string `json:"metadata,omitempty"`
	ReasonCode     string            `json:"reasonCode,omitempty"`
	CreatedBy      string            `json:"createdBy,omitempty"`
	TicketID       string            `json:"ticketID,omitempty"`
}

type DeactivateLOCResponse struct {
	Status string `json:"status,omitempty"`
}

// UpdateCASAAccountStatusRequest
type UpdateCASAAccountStatusRequest struct {
	AccountID        string        `json:"accountID,omitempty"`
	Status           AccountStatus `json:"status,omitempty"`
	ClosingTimestamp time.Time     `json:"closingTimestamp,omitempty"`
	IdempotencyKey   string        `json:"idempotencyKey,omitempty"`
	UpdatedBy        string        `json:"updatedBy,omitempty"`
	TicketID         string        `json:"ticketID,omitempty"`
}

// UpdateCASAAccountStatusResponse
type UpdateCASAAccountStatusResponse struct {
	AccountID string        `json:"accountID,omitempty"`
	Status    AccountStatus `json:"status,omitempty"`
	UpdatedBy string        `json:"updatedBy,omitempty"`
}

type Filter struct {
	Column string        `json:"column,omitempty"`
	Value  []interface{} `json:"value,omitempty"`
}

type Sort struct {
	Column string    `json:"column,omitempty"`
	Sort   SortOrder `json:"sort,omitempty"`
}

type TicketFilterOptions struct {
	Modules           []Options `json:"modules,omitempty"`
	Elements          []Options `json:"elements,omitempty"`
	SourceSystem      []Options `json:"sourceSystem,omitempty"`
	IntegrationStatus []Options `json:"integrationStatus,omitempty"`
	AssignTo          []Options `json:"assignTo,omitempty"`
}

type GetOptionsRequest struct {
	Type OptionType `json:"type,omitempty" validate:"required,oneof=roles elements permissions modules reasons ticketFilter"`
}

type GetOptionsResponse struct {
	Data interface{} `json:"data,omitempty"`
}

type Options struct {
	Id       int64  `json:"id,omitempty"`
	Name     string `json:"name,omitempty"`
	ModuleId int64  `json:"moduleId,omitempty"`
	Key      string `json:"key,omitempty"`
}

type GetElementPrioritiesRequest struct {
	Id int64 `json:"id,omitempty"`
}

type GetElementPrioritiesResponse struct {
	Priorities []Priority `json:"priorities,omitempty"`
}

type Priority struct {
	Id               int64  `json:"id,omitempty"`
	Name             string `json:"name,omitempty"`
	TimeToResolveSec int64  `json:"timeToResolveSec,omitempty"`
	CreatedAt        string `json:"createdAt,omitempty"`
	UpdatedAt        string `json:"updatedAt,omitempty"`
	CreatedBy        int64  `json:"createdBy,omitempty"`
	UpdatedBy        int64  `json:"updatedBy,omitempty"`
	ElementID        int64  `json:"elementID,omitempty"`
}

type GetStatusesRequest struct {
}

type GetStatusesResponse struct {
	Statuses []Status `json:"statuses,omitempty"`
}

type CreateStatusRequest struct {
	Name string `json:"name,omitempty" validate:"required"`
}

type CreateStatusResponse struct {
	Id int64 `json:"id,omitempty"`
}

type UpdateStatusRequest struct {
	Id   int64  `json:"id,omitempty" validate:"required"`
	Name string `json:"name,omitempty" validate:"required"`
}

type UpdateStatusResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetPrioritiesRequest struct {
	ElementID int64 `json:"elementID,omitempty"`
}

type GetPrioritiesResponse struct {
	Priorities []Priority `json:"priorities,omitempty"`
	Count      int64      `json:"count,omitempty"`
	Offset     int64      `json:"offset,omitempty"`
}

type CreatePriorityRequest struct {
	Name             string `json:"name,omitempty" validate:"required"`
	TimeToResolveSec int64  `json:"timeToResolveSec,omitempty" validate:"required"`
	ElementID        int64  `json:"elementID,omitempty" validate:"required"`
}

type CreatePriorityResponse struct {
	Id int64 `json:"id,omitempty"`
}

type UpdatePriorityRequest struct {
	Id               int64  `json:"id,omitempty" validate:"required"`
	Name             string `json:"name,omitempty" validate:"required"`
	TimeToResolveSec int64  `json:"timeToResolveSec,omitempty" validate:"required"`
	ElementID        int64  `json:"elementID,omitempty" validate:"required"`
}

type UpdatePriorityResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetTicketChainsRequest struct {
	ElementID int64 `json:"elementID,omitempty"`
}

type GetTicketChainsResponse struct {
	Chains []TicketChain `json:"chains,omitempty"`
}

type TicketChain struct {
	Id             int64   `json:"id,omitempty"`
	Name           string  `json:"name,omitempty"`
	CreatedAt      string  `json:"createdAt,omitempty"`
	UpdatedAt      string  `json:"updatedAt,omitempty"`
	CreatedBy      string  `json:"createdBy,omitempty"`
	UpdatedBy      string  `json:"updatedBy,omitempty"`
	ElementID      int64   `json:"elementID,omitempty"`
	PrevStatusID   int64   `json:"prevStatusID,omitempty"`
	NextStatusID   int64   `json:"nextStatusID,omitempty"`
	PermissionsIDs []int64 `json:"permissionsIDs,omitempty"`
	ActionName     string  `json:"actionName,omitempty"`
}

type Status struct {
	Id   int64  `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type CreateFeatureFlagRequest struct {
	Name        string `json:"name,omitempty" validate:"required"`
	Value       int32  `json:"value,omitempty"`
	Description string `json:"description,omitempty"`
}

type CreateFeatureFlagResponse struct {
	Status string `json:"status,omitempty"`
}

type UpdateFeatureFlagRequest struct {
	Name        string `json:"name,omitempty" validate:"required"`
	Value       int32  `json:"value,omitempty"`
	Description string `json:"description,omitempty"`
}

type UpdateFeatureFlagResponse struct {
	Status string `json:"status,omitempty"`
}

type GetFeatureFlagRequest struct {
	Name []string `json:"name,omitempty" validate:"required"`
}

type GetFeatureFlagResponse struct {
	Data []FeatureFlag `json:"data,omitempty"`
}

type FeatureFlag struct {
	Name        string `json:"name,omitempty"`
	Value       int32  `json:"value,omitempty"`
	Description string `json:"description,omitempty"`
	CreatedAt   string `json:"createdAt,omitempty"`
	UpdatedAt   string `json:"updatedAt,omitempty"`
	CreatedBy   string `json:"createdBy,omitempty"`
	UpdatedBy   string `json:"updatedBy,omitempty"`
}

type DeleteFeatureFlagRequest struct {
	Name string `json:"name,omitempty" validate:"required"`
}

type DeleteFeatureFlagResponse struct {
	Status string `json:"status,omitempty"`
}

type GetFeatureFlagListRequest struct {
	SearchKey string   `json:"searchKey,omitempty"`
	Offset    int64    `json:"offset,omitempty" validate:"offset"`
	Limit     int64    `json:"limit,omitempty" validate:"limit"`
	Filter    []Filter `json:"filter,omitempty" validate:"omitempty,ff_filter"`
	SortBy    *Sort    `json:"sortBy,omitempty" validate:"omitempty,ff_sort"`
}

type GetFeatureFlagListResponse struct {
	Count  int64         `json:"count,omitempty"`
	Offset int64         `json:"offset,omitempty"`
	Data   []FeatureFlag `json:"data,omitempty"`
}

// CreateDocumentRequest ...
type CreateDocumentRequest struct {
	Name        string `json:"name,omitempty" validate:"required,max=255"`
	Payload     string `json:"payload,omitempty"`
	TicketID    int64  `json:"ticketID,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
}

// CreateDocumentResponse ...
type CreateDocumentResponse struct {
	Id         int64  `json:"id,omitempty"`
	Url        string `json:"url,omitempty"`
	Name       string `json:"name,omitempty"`
	PresignUrl string `json:"presignUrl,omitempty"`
}

// DeleteDocumentRequest ...
type DeleteDocumentRequest struct {
	Name string `json:"name,omitempty"`
}

// DeleteDocumentResponse ...
type DeleteDocumentResponse struct {
	Id int64 `json:"id,omitempty"`
}

// GetDocumentRequest ...
type GetDocumentRequest struct {
	Name string `json:"name,omitempty"`
}

// GetDocumentResponse ...
type GetDocumentResponse struct {
	Id         int64  `json:"id,omitempty"`
	Payload    string `json:"payload,omitempty"`
	PresignUrl string `json:"presignUrl,omitempty"`
}

// GetTicketDocumentsRequest ...
type GetTicketDocumentsRequest struct {
	Id int64 `json:"id,omitempty"`
}

// GetTicketDocumentsResponse ...
type GetTicketDocumentsResponse struct {
	Documents []Document `json:"documents,omitempty"`
}

// GetTicketDocumentRequest ...
type GetTicketDocumentRequest struct {
	Id int64 `json:"id,omitempty"`
}

type CustomerSearchRequest struct {
	Identifier     string            `json:"identifier,omitempty"`
	IdentifierType IdentifierType    `json:"identifierType,omitempty" validate:"identifier_type_value"`
	Key            string            `json:"key,omitempty"`
	Payload        map[string]string `json:"payload,omitempty"`
}

type CustomerSearchResponse struct {
	Status    string                   `json:"status,omitempty"`
	Structure *CustomerSearchStructure `json:"structure,omitempty"`
	Data      map[string]interface{}   `json:"data,omitempty"`
}

type CustomerSearchStructure struct {
	Key      string      `json:"key,omitempty"`
	Type     string      `json:"type,omitempty"`
	Label    string      `json:"label,omitempty"`
	Children []Structure `json:"children,omitempty"`
}

type Structure struct {
	Key         string            `json:"key,omitempty"`
	Type        string            `json:"type,omitempty"`
	Label       string            `json:"label,omitempty"`
	Payload     map[string]string `json:"payload,omitempty"`
	HasChildren bool              `json:"hasChildren,omitempty"`
	Children    []Structure       `json:"children,omitempty"`
}

type GetCustomersRequest struct {
	Identifier     string         `json:"identifier,omitempty" validate:"required"`
	IdentifierType IdentifierType `json:"identifierType,omitempty" validate:"required"`
	Page           int64          `json:"page,omitempty" validate:"required"`
}

type GetCustomersResponse struct {
	IsLastPage bool          `json:"isLastPage,omitempty"`
	Customers  []interface{} `json:"customers,omitempty"`
}

type GetDataSegregationRoleListRequest struct {
	SearchKey string `json:"searchKey,omitempty"`
	Offset    int64  `json:"offset,omitempty" validate:"offset"`
	Limit     int64  `json:"limit,omitempty" validate:"limit"`
	SortBy    *Sort  `json:"sortBy,omitempty" validate:"omitempty,data_segregation_sort"`
}

type GetDataSegregationRoleListResponse struct {
	Count  int64                 `json:"count,omitempty"`
	Offset int64                 `json:"offset,omitempty"`
	Data   []DataSegregationRole `json:"data,omitempty"`
}

type DataSegregationRole struct {
	Id   int64  `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type GetDataSegregationRequest struct {
	RoleID    int64  `json:"roleID,omitempty" validate:"required,gt=0"`
	Offset    int64  `json:"offset,omitempty" validate:"offset"`
	Limit     int64  `json:"limit,omitempty" validate:"limit"`
	SearchKey string `json:"searchKey,omitempty"`
	ParentID  int64  `json:"parentID,omitempty"`
	SortBy    *Sort  `json:"sortBy,omitempty" validate:"omitempty,data_segregation_sort"`
}

type GetDataSegregationResponse struct {
	Offset     int64             `json:"offset,omitempty"`
	IsLastPage bool              `json:"isLastPage,omitempty"`
	Data       []DataSegregation `json:"data,omitempty"`
}

type DataSegregation struct {
	Id         int64  `json:"id,omitempty"`
	Name       string `json:"name,omitempty"`
	ParentID   int64  `json:"parentID,omitempty"`
	Status     int64  `json:"status,omitempty"`
	HasChild   bool   `json:"hasChild,omitempty"`
	ParentName string `json:"parentName,omitempty"`
}

type UpdateDataSegregationRequest struct {
	RoleID         int64   `json:"roleID,omitempty" validate:"required,gt=0"`
	SegregationIDs []int64 `json:"segregationIDs,omitempty"`
	Status         int64   `json:"status,omitempty" validate:"binary"`
	IsCheckParent  bool    `json:"isCheckParent,omitempty"`
}

type UpdateDataSegregationResponse struct {
	Status string `json:"status,omitempty"`
}

type CustomerSearchDataPointRequest struct {
	Key     string            `json:"key,omitempty" validate:"required"`
	Payload map[string]string `json:"payload,omitempty"`
}

type CustomerSearchDataPointResponse struct {
	Data interface{} `json:"data,omitempty"`
}

type GetCustomerAccountRequest struct {
	Identifier     string         `json:"identifier,omitempty" validate:"required"`
	IdentifierType IdentifierType `json:"identifierType,omitempty" validate:"required"`
	Data           []string       `json:"data,omitempty" validate:"required,oneof=customer_info customer_status accounts"`
}

type GetCustomerAccountsResponse struct {
	IsLastPage     bool          `json:"isLastPage,omitempty"`
	CustomerStatus string        `json:"customerStatus,omitempty"`
	Customers      []interface{} `json:"customers,omitempty"`
	Accounts       []interface{} `json:"accounts,omitempty"`
}

type Pagination struct {
	NextCursorID string `json:"nextCursorID,omitempty"`
	PrevCursorID string `json:"prevCursorID,omitempty"`
}

// GetEventLogRequest
type GetEventLogRequest struct {
	SafeID         string  `json:"safeID,omitempty"`
	EndDate        string  `json:"endDate,omitempty"`
	StartDate      string  `json:"startDate,omitempty"`
	PageSize       int64   `json:"pageSize,omitempty"`
	StartingBefore string  `json:"startingBefore,omitempty"`
	EndingAfter    string  `json:"endingAfter,omitempty"`
	LogType        LogType `json:"logType,omitempty"`
	PartnerID      string  `json:"partnerID,omitempty"`
}

// GetEventLogResponse
type GetEventLogResponse struct {
	Pagination *Pagination `json:"pagination,omitempty"`
	Data       interface{} `json:"data,omitempty"`
}

type GetCustomerSegmentsResponse struct {
	CustomerSegments []CustomerSegment `json:"customerSegments,omitempty"`
}

type CustomerSegment struct {
	Id        int64  `json:"id,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	UpdatedBy string `json:"updatedBy,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	Name      string `json:"name,omitempty"`
	IsActive  string `json:"isActive,omitempty"`
}

type GetTicketRequestorsResponse struct {
	TicketRequestors []TicketRequestor `json:"ticketRequestors,omitempty"`
}

type TicketRequestor struct {
	Id        int64  `json:"id,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	UpdatedBy string `json:"updatedBy,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	Name      string `json:"name,omitempty"`
	IsActive  string `json:"isActive,omitempty"`
}

// Onedash is the service that provides ticket management.
type Onedash interface {
	CreateTicket(ctx context.Context, req *CreateTicketRequest) (*CreateTicketResponse, error)
	UpdateTicket(ctx context.Context, req *UpdateTicketRequest) (*UpdateTicketResponse, error)
	GetTicketList(ctx context.Context, req *GetTicketListRequest) (*GetTicketListResponse, error)
	GetTicketExport(ctx context.Context, req *GetTicketListRequest) (*GetTicketExportResponse, error)
	GetTicketByID(ctx context.Context, req *GetTicketByIDRequest) (*GetTicketByIDResponse, error)
	CreateModule(ctx context.Context, req *CreateModuleRequest) (*CreateModuleResponse, error)
	UpdateModule(ctx context.Context, req *UpdateModuleRequest) (*UpdateModuleResponse, error)
	GetModules(ctx context.Context, req *GetModulesRequest) (*GetModulesResponse, error)
	CreateElement(ctx context.Context, req *CreateElementRequest) (*CreateElementResponse, error)
	UpdateElement(ctx context.Context, req *UpdateElementRequest) (*UpdateElementResponse, error)
	GetElements(ctx context.Context, req *GetElementsRequest) (*GetElementsResponse, error)
	GetElementByID(ctx context.Context, req *GetElementByIDRequest) (*GetElementByIDResponse, error)
	CreateTicketComment(ctx context.Context, req *CreateTicketCommentRequest) (*CreateTicketCommentResponse, error)
	GetTicketComments(ctx context.Context, req *GetTicketCommentsRequest) (*GetTicketCommentsResponse, error)
	// LogAuditTrails: API to create Log Audit Trail
	LogAuditTrails(ctx context.Context, req *LogAuditTrailRequest) (*LogAuditTrailResponse, error)
	GetLogAuditTrails(ctx context.Context, req *GetLogAuditTrailsRequest) (*GetLogAuditTrailsResponse, error)
	// UnlinkAccount: API to unlink account from partner
	UnlinkAccount(ctx context.Context, req *UnlinkAccountRequest) (*UnlinkAccountResponse, error)
	// SendNotification is API to send notification email, push or push inbox
	SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error)
	// TransferOnBehalf: API to transfer on behalf
	TransferOnBehalf(ctx context.Context, req *PaymentTransferRequest) (*PaymentTransferResponse, error)
	// BlockAccount is API to block account
	BlockAccount(ctx context.Context, req *BlockAccountRequest) (*BlockAccountResponse, error)
	// UnblockAccount is API to unblock account
	UnblockAccount(ctx context.Context, req *UnblockAccountRequest) (*UnblockAccountResponse, error)
	// DeactivateLOC: API to deactivate LOC account
	DeactivateLOC(ctx context.Context, req *DeactivateLOCRequest) (*DeactivateLOCResponse, error)
	// UpdateCASAAccountStatus: API to update the status of a casa account/bp asynchronously
	UpdateCASAAccountStatus(ctx context.Context, req *UpdateCASAAccountStatusRequest) (*UpdateCASAAccountStatusResponse, error)
	// CreateDocument is API to create document
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (*CreateDocumentResponse, error)
	// DeleteDocument is API to delete document
	DeleteDocument(ctx context.Context, req *DeleteDocumentRequest) (*DeleteDocumentResponse, error)
	// GetDocument is API to get document
	GetDocument(ctx context.Context, req *GetDocumentRequest) (*GetDocumentResponse, error)
	// GetTicketDocuments is api to get ticket documents by ticket id
	GetTicketDocuments(ctx context.Context, req *GetTicketDocumentsRequest) (*GetTicketDocumentsResponse, error)
	// GetOptions is API for get dropdown option by type
	GetOptions(ctx context.Context, req *GetOptionsRequest) (*GetOptionsResponse, error)
	// GetElementPriorities is API to get element priorities
	GetElementPriorities(ctx context.Context, req *GetElementPrioritiesRequest) (*GetElementPrioritiesResponse, error)
	// GetStatuses is API to get statuses
	GetStatuses(ctx context.Context) (*GetStatusesResponse, error)
	// CreateStatus is API to create status
	CreateStatus(ctx context.Context, req *CreateStatusRequest) (*CreateStatusResponse, error)
	// UpdateStatus is API to update status
	UpdateStatus(ctx context.Context, req *UpdateStatusRequest) (*UpdateStatusResponse, error)
	// GetPriorities is API to get priorities
	GetPriorities(ctx context.Context, req *GetPrioritiesRequest) (*GetPrioritiesResponse, error)
	// CreatePriority is API to create priority
	CreatePriority(ctx context.Context, req *CreatePriorityRequest) (*CreatePriorityResponse, error)
	// UpdatePriority is API to update priority
	UpdatePriority(ctx context.Context, req *UpdatePriorityRequest) (*UpdatePriorityResponse, error)
	// GetTicketChains is API to get ticket chains
	GetTicketChains(ctx context.Context, req *GetTicketChainsRequest) (*GetTicketChainsResponse, error)
	// UpdateTicketAssignee is API to update ticket assignee
	UpdateTicketAssignee(ctx context.Context, req *UpdateTicketAssigneeRequest) (*UpdateTicketAssigneeResponse, error)
	// CreateFeatureFlag is API to create feature flag
	CreateFeatureFlag(ctx context.Context, req *CreateFeatureFlagRequest) (*CreateFeatureFlagResponse, error)
	// UpdateFeatureFlag is API to update feature flag
	UpdateFeatureFlag(ctx context.Context, req *UpdateFeatureFlagRequest) (*UpdateFeatureFlagResponse, error)
	// GetFeatureFlag is API to get requested feature flag
	GetFeatureFlag(ctx context.Context, req *GetFeatureFlagRequest) (*GetFeatureFlagResponse, error)
	// DeleteFeatureFlag is API to soft delete feature flag
	DeleteFeatureFlag(ctx context.Context, req *DeleteFeatureFlagRequest) (*DeleteFeatureFlagResponse, error)
	// GetFeatureFlagList is API to get feature flag list
	GetFeatureFlagList(ctx context.Context, req *GetFeatureFlagListRequest) (*GetFeatureFlagListResponse, error)
	// CustomerSearch: API to search customer details based on identifier
	CustomerSearch(ctx context.Context, req *CustomerSearchRequest) (*CustomerSearchResponse, error)
	// GetCustomers: API to search multiple customer based on identifier
	GetCustomers(ctx context.Context, req *GetCustomersRequest) (*GetCustomersResponse, error)
	// GetRolesDataSegregation is get roles list API for data segregation purpose
	GetRolesDataSegregation(ctx context.Context, req *GetDataSegregationRoleListRequest) (*GetDataSegregationRoleListResponse, error)
	// GetDataSegregation is API for get data segregation detail for specific role
	GetDataSegregation(ctx context.Context, req *GetDataSegregationRequest) (*GetDataSegregationResponse, error)
	// UpdateDataSegregation is API for update data segregation
	UpdateDataSegregation(ctx context.Context, req *UpdateDataSegregationRequest) (*UpdateDataSegregationResponse, error)
	// GetCustomersDataPoints is API to get specific data points
	GetCustomersDataPoint(ctx context.Context, req *CustomerSearchDataPointRequest) (*CustomerSearchDataPointResponse, error)
	// GetCustomerSegements is API to get all customer segemnts
	GetCustomerSegements(ctx context.Context) (*GetCustomerSegmentsResponse, error)
	// GetTicketRequestors is API to get all ticket requestors
	GetTicketRequestors(ctx context.Context) (*GetTicketRequestorsResponse, error)
	// GetCustomerAccounts is API to get specific customer and list of account
	GetCustomerAccounts(ctx context.Context, req *GetCustomerAccountRequest) (*GetCustomerAccountsResponse, error)
}
