package error

import (
	"net/http"

	"gitlab.myteksi.net/dakota/servus/v2"
)

// ErrorCode ...
type ErrorCode string

// Error codes for application service
const (
	// BadRequest - 400
	BadRequest ErrorCode = "badRequest"

	// Unauthorized - 401
	Unauthorized ErrorCode = "unauthorized"

	// StatusPaymentRequired - 402
	StatusPaymentRequired ErrorCode = "StatusPaymentRequired"

	// Forbidden - 403
	Forbidden ErrorCode = "forbidden"

	// ResourceNotFound - 404
	ResourceNotFound ErrorCode = "resourceNotFound"

	// ResourceConflict - 409
	ResourceConflict ErrorCode = "resourceConflict"

	// InternalServerError - 500
	InternalServerError ErrorCode = "internalServerError"

	// FieldMissing ...
	FieldMissing ErrorCode = "fieldMissing"

	// FieldInvalid ...
	FieldInvalid ErrorCode = "fieldInvalid"

	// Idem ...
	Idem ErrorCode = "idem"
)

var errorCodeToStatusCode = map[ErrorCode]int{
	// 400
	BadRequest: http.StatusBadRequest,

	// 400
	FieldMissing: http.StatusBadRequest,

	// 401
	Unauthorized: http.StatusUnauthorized,

	// 402
	StatusPaymentRequired: http.StatusPaymentRequired,

	// 403
	Forbidden: http.StatusForbidden,

	// 404
	ResourceNotFound: http.StatusNotFound,

	// 409
	ResourceConflict: http.StatusConflict,

	// 500
	InternalServerError: http.StatusInternalServerError,

	// 500 - Idem if returned meaning there is programming error
	Idem: http.StatusInternalServerError,
}

var StatusCodeToErrorCode = map[int]ErrorCode{
	// 400
	http.StatusBadRequest: BadRequest,

	// 401
	http.StatusUnauthorized: Unauthorized,

	// 402
	http.StatusPaymentRequired: StatusPaymentRequired,

	// 403
	http.StatusForbidden: Forbidden,

	// 404
	http.StatusNotFound: ResourceNotFound,

	// 409
	http.StatusConflict: ResourceConflict,

	// 500
	http.StatusInternalServerError: InternalServerError,
}

var errorCodeToErrorMessage = map[ErrorCode]string{
	// 404
	ResourceNotFound: "Resource not found",
	// 500
	InternalServerError: "There is a problem on our end. Please try again later.",
}

// DefaultInternalServerError returns default error message for unexpected error.
var DefaultInternalServerError = servus.ServiceError{
	Code:     string(InternalServerError),
	Message:  InternalServerError.ErrorMessage(),
	HTTPCode: InternalServerError.HTTPStatusCode(),
}

// FieldMissingError returns error message for missing field.
var FieldMissingError = servus.ServiceError{
	HTTPCode: FieldMissing.HTTPStatusCode(),
	Code:     string(FieldMissing),
	Message:  "Fields missing/invalid in request",
}

// ResourceNotFoundError returns error message for missing data.
var ResourceNotFoundError = servus.ServiceError{
	HTTPCode: ResourceNotFound.HTTPStatusCode(),
	Code:     string(ResourceNotFound),
	Message:  "The request data is not present",
}

// HTTPStatusCode returns HTTP code based on code.
func (code ErrorCode) HTTPStatusCode() int {
	if statusCode, ok := errorCodeToStatusCode[code]; ok {
		return statusCode
	}
	return http.StatusBadRequest
}

// ErrorMessage returns error message based on code.
func (code ErrorCode) ErrorMessage() string {
	if errorMessage, ok := errorCodeToErrorMessage[code]; ok {
		return errorMessage
	}
	return errorCodeToErrorMessage[InternalServerError]
}
