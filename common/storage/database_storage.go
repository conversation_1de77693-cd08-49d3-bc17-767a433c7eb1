// Package storage ...
package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sync/atomic"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/data"
)

const (
	logTag = "onedash-api-dbconn"
)

// DatabaseStore ...
type DatabaseStore interface {
	GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error)
}

// DBStore ...
type DBStore struct {
	StatsD statsd.Client
}

//go:generate mockery --name DatabaseStore --inpackage --case=underscore

// NewDBStore creates a new DBStore object
func NewDBStore(statsDClient statsd.Client) *DBStore {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &DBStore{
		StatsD: statsDClient,
	}
}

// GetDatabaseHandle ...
func (db *DBStore) GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	atomic.AddInt64(&config.PendingCalls, 1)
	defer atomic.AddInt64(&config.PendingCalls, -1)
	dbs, err := getDatabase(ctx, config)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetDatabaseHandleLogTag, fmt.Sprintf("GetDatabaseHandle errors: %s", err.Error()))
		return nil, err
	}
	return dbs, nil
}

// getDatabase returns a connection the database (either real or mocked) depending on the ENV('Mode') variable.
var getDatabase = func(ctx context.Context, config *data.MysqlConfig) (db *sql.DB, err error) {
	defer panicRecovery(ctx, "commons.data.getDatabase")
	connect := func() {
		db, err = createDB(ctx, config)
	}
	config.ConnectOnce.Do(connect)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("CreateDB errors: %s", err.Error()))
		return nil, err
	}
	if db != nil {
		return db, nil
	}

	// check for existing connection
	db = getDB(config)
	if db == nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("GetDB errors: %s", err.Error()))
		return nil, errors.New("failed to connect to DB")
	}
	return db, nil
}

var createDB = func(ctx context.Context, config *data.MysqlConfig) (db *sql.DB, err error) {
	db, err = sql.Open("mysql", config.Dsn)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Failed to open database connection: %s", err.Error()))
		return
	}
	db.SetMaxIdleConns(config.MaxIdle)
	db.SetMaxOpenConns(config.MaxOpen)
	if (config.ConnMaxLifetime.Duration) > 0 {
		db.SetConnMaxLifetime(config.ConnMaxLifetime.Duration)
	}

	// store connection for later reuse
	setDB(config, db)
	return
}

func getDB(config *data.MysqlConfig) *sql.DB {
	return config.DBCache.DB
}

func setDB(config *data.MysqlConfig, db *sql.DB) {
	config.DBCache.DB = db
}

// panicRecovery recovers from a panic happened during specified function
func panicRecovery(ctx context.Context, tag string) {
	if r := recover(); r != nil {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("[%s] Recovered from panic. Error: %s", tag, r))
	}
}
