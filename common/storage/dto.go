package storage

// PaginationCursor for cursor information
type PaginationCursor struct {
	UserID    string `json:"userID"`
	CreatedAt string `json:"createdAt"`
	FirstID   uint64
	ID        int `json:"ID"`
}

// PaginationParams for holding pagination status of a request
type PaginationParams struct {
	UserID         string
	StartingBefore string
	EndingAfter    string
	StartDate      string
	EndDate        string
	PageSize       int64
}
