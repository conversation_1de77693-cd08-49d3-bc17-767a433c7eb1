package storage

import (
	"fmt"
	"strings"
)

const (
	// QueryConditionTypeWHERE ...
	QueryConditionTypeWHERE = 1
	// QueryConditionTypeLIMIT ...
	QueryConditionTypeLIMIT = 2
	// QueryConditionTypeSORT ...
	QueryConditionTypeSORT = 3
	// QueryConditionTypeOFFSET ...
	QueryConditionTypeOFFSET = 4
	// QueryConditionTypeOR ...
	QueryConditionTypeOR = 5
)

// QueryCondition ...
type QueryCondition struct {
	Type             int64  // value consist of SELECT/WHERE/LIMIT/ORDER BY
	ConstructedQuery string // ex: if type=WHERE, then X > 3. if ORDER BY then X ASC
	Args             any
	MultipleArgs     []any
}

// SortColumnMapper maps API sort fields to database columns
type SortColumnMapper func(sortBy string) string

// Sort represents a sort specification with column and order
type Sort struct {
	Column string
	Order  string
}

// QueryBuilder is a builder for query conditions
type QueryBuilder struct {
	conditions []QueryCondition
}

// NewQueryBuilder creates a new query builder with optional initial capacity
func NewQueryBuilder(capacity ...int) *QueryBuilder {
	currentCap := 10 // default capacity
	if len(capacity) > 0 && capacity[0] > 0 {
		currentCap = capacity[0]
	}

	return &QueryBuilder{
		conditions: make([]QueryCondition, 0, currentCap),
	}
}

// AddCondition adds a single condition to the builder
func (qb *QueryBuilder) AddCondition(condition QueryCondition) *QueryBuilder {
	qb.conditions = append(qb.conditions, condition)
	return qb
}

// AddConditions adds multiple conditions to the builder
func (qb *QueryBuilder) AddConditions(conditions ...QueryCondition) *QueryBuilder {
	qb.conditions = append(qb.conditions, conditions...)
	return qb
}

// Build returns the final slice of conditions
func (qb *QueryBuilder) Build() []QueryCondition {
	return qb.conditions
}

// Count returns the number of conditions
func (qb *QueryBuilder) Count() int {
	return len(qb.conditions)
}

// Clear removes all conditions
func (qb *QueryBuilder) Clear() *QueryBuilder {
	qb.conditions = qb.conditions[:0]
	return qb
}

// AddSortWithDefaults adds a sort condition with default column and sort order
func (qb *QueryBuilder) AddSortWithDefaults(column string, sortOrder string, columnMapper SortColumnMapper, defaultValueSortOrder string) *QueryBuilder {
	// Use provided values or defaults
	effectiveColumn := columnMapper(column)
	effectiveSortOrder := sortOrder

	if effectiveSortOrder == "" {
		effectiveSortOrder = defaultValueSortOrder
	}

	if effectiveSortOrder == "DESC" {
		qb.AddCondition(DescendingOrder(effectiveColumn))
	} else {
		qb.AddCondition(AscendingOrder(effectiveColumn))
	}

	return qb
}

// AddPagination adds limit and offset conditions
func (qb *QueryBuilder) AddPagination(limit, offset int64) *QueryBuilder {
	if limit > 0 {
		qb.AddCondition(Limit(int(limit)))
	}
	if offset > 0 {
		qb.AddCondition(Offset(int(offset)))
	}
	return qb
}

// EqualTo creates a condition where field is equal to value
func EqualTo(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v = ?", field),
		Args:             value,
	}
}

// NotEqualTo creates a condition where field is not equal to value
func NotEqualTo(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v != ?", field),
		Args:             value,
	}
}

// GreaterThan creates a condition where field is greater than value
func GreaterThan(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v > ?", field),
		Args:             value,
	}
}

// GreaterThanOrEqualTo creates a condition where field is greater than or equal to value
func GreaterThanOrEqualTo(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v >= ?", field),
		Args:             value,
	}
}

// LessThan creates a condition where field is less than value
func LessThan(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v < ?", field),
		Args:             value,
	}
}

// LessThanOrEqualTo creates a condition where field is less than or equal to value
func LessThanOrEqualTo(field any, value any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v <= ?", field),
		Args:             value,
	}
}

// Like creates a condition where field matches a pattern
func Like(field any, pattern any, queryType int64) QueryCondition {
	return QueryCondition{
		Type:             queryType,
		ConstructedQuery: fmt.Sprintf("%v LIKE ?", field),
		Args:             pattern,
	}
}

// In creates a condition where field is in a list of values
func In(field any, values ...any) QueryCondition {
	if len(values) == 0 {
		return QueryCondition{}
	}
	placeholders := make([]string, len(values))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v IN (%s)", field, strings.Join(placeholders, ", ")),
		MultipleArgs:     values,
	}
}

// NotIn creates a condition where field is not in a list of values
func NotIn(field any, values ...any) QueryCondition {
	placeholders := make([]string, len(values))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v NOT IN (%s)", field, strings.Join(placeholders, ", ")),
		MultipleArgs:     values,
	}
}

// And creates a condition that combines multiple conditions with AND
func And(conditions ...QueryCondition) QueryCondition {
	var clauses []string
	var args []any

	for _, condition := range conditions {
		clauses = append(clauses, condition.ConstructedQuery)
		if condition.Args != nil {
			args = append(args, condition.Args)
		}
		if len(condition.MultipleArgs) > 0 {
			args = append(args, condition.MultipleArgs...)
		}
	}

	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("(%s)", strings.Join(clauses, " AND ")),
		MultipleArgs:     args,
	}
}

// Or creates a condition that combines multiple conditions with OR
func Or(conditions ...QueryCondition) QueryCondition {
	var clauses []string
	var args []any

	for _, condition := range conditions {
		clauses = append(clauses, condition.ConstructedQuery)
		if condition.Args != nil {
			args = append(args, condition.Args)
		}
		if len(condition.MultipleArgs) > 0 {
			args = append(args, condition.MultipleArgs...)
		}
	}

	return QueryCondition{
		Type:             QueryConditionTypeOR,
		ConstructedQuery: fmt.Sprintf("(%s)", strings.Join(clauses, " OR ")),
		MultipleArgs:     args,
	}
}

// DescendingOrder ...
func DescendingOrder(column string) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeSORT,
		ConstructedQuery: fmt.Sprintf("%v DESC", column),
	}
}

// AscendingOrder ...
func AscendingOrder(column string) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeSORT,
		ConstructedQuery: fmt.Sprintf("%v ASC", column),
	}
}

// Limit ...
func Limit(limit int) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeLIMIT,
		ConstructedQuery: fmt.Sprintf("%d", limit),
	}
}

// Offset ...
func Offset(offset int) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeOFFSET,
		ConstructedQuery: fmt.Sprintf("%d", offset),
	}
}

// IsNull creates a condition where field is NULL
func IsNull(field any) QueryCondition {
	return QueryCondition{
		Type:             QueryConditionTypeWHERE,
		ConstructedQuery: fmt.Sprintf("%v IS NULL", field),
		Args:             nil,
	}
}

// BuildQuery ...
func BuildQuery(query string, conditions ...QueryCondition) (string, []any) {
	var args []any

	whereClauses, args := buildWhereClauses(conditions, args)
	orClauses, args := buildOrClauses(conditions, args)
	orderByClause := buildOrderByClause(conditions)
	limitClause := getSingleCondition(conditions, QueryConditionTypeLIMIT)
	offsetClause := getSingleCondition(conditions, QueryConditionTypeOFFSET)

	if whereClauses != "" {
		query += " WHERE " + whereClauses
	}

	if orClauses != "" {
		if whereClauses != "" {
			query += " AND " + orClauses
		} else {
			query += " WHERE " + orClauses
		}
	}

	if orderByClause != "" {
		query += " ORDER BY " + orderByClause
	}

	if limitClause != "" {
		query += " LIMIT " + limitClause
	}

	if offsetClause != "" {
		query += " OFFSET " + offsetClause
	}

	return query, args
}

func buildWhereClauses(conditions []QueryCondition, args []any) (string, []any) {
	var whereClauses []string
	for _, condition := range conditions {
		if condition.Type == QueryConditionTypeWHERE {
			whereClauses = append(whereClauses, condition.ConstructedQuery)
			args = appendArgs(args, condition)
		}
	}
	return strings.Join(whereClauses, " AND "), args
}

func buildOrClauses(conditions []QueryCondition, args []any) (string, []any) {
	var orClauses []string
	for _, condition := range conditions {
		if condition.Type == QueryConditionTypeOR {
			orClauses = append(orClauses, condition.ConstructedQuery)
			args = appendArgs(args, condition)
		}
	}
	if len(orClauses) == 0 {
		return "", args
	}
	return fmt.Sprintf("(%s)", strings.Join(orClauses, " OR ")), args
}

func buildOrderByClause(conditions []QueryCondition) string {
	var orderByClauses []string
	for _, condition := range conditions {
		if condition.Type == QueryConditionTypeSORT {
			orderByClauses = append(orderByClauses, condition.ConstructedQuery)
		}
	}
	return strings.Join(orderByClauses, ", ")
}

func BuildSortOrder(column, sortOrder, alias string) QueryCondition {
	effectiveColumn := column
	if effectiveColumn == "" {
		effectiveColumn = "id"
	}

	effectiveSortOrder := sortOrder
	// Only accept "DESC", everything else defaults to "ASC"
	if effectiveSortOrder != "DESC" {
		effectiveSortOrder = "ASC"
	}

	columnRef := effectiveColumn
	if alias != "" {
		columnRef = fmt.Sprintf("%s.%s", alias, effectiveColumn)
	}

	if effectiveSortOrder == "DESC" {
		return DescendingOrder(columnRef)
	}
	return AscendingOrder(columnRef)

}

func getSingleCondition(conditions []QueryCondition, conditionType int64) string {
	for _, condition := range conditions {
		if condition.Type == conditionType {
			return condition.ConstructedQuery
		}
	}
	return ""
}

func appendArgs(args []any, condition QueryCondition) []any {
	if condition.Args != nil {
		args = append(args, condition.Args)
	}
	if len(condition.MultipleArgs) != 0 {
		args = append(args, condition.MultipleArgs...)
	}
	return args
}
