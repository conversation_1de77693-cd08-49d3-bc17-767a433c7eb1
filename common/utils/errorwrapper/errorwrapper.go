// Package errorwrapper provides common error handling functions
package errorwrapper

import (
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// Error create common error message
func Error(code apiError.ErrorCode, msg string, errs ...servus.ErrorDetail) error {
	return servus.ServiceError{
		HTTPCode: code.HTTPStatusCode(),
		Code:     string(code),
		Message:  msg,
		Errors:   errs,
	}
}

// WrapError wraps an error with a code and message
func WrapError(err error, code apiError.ErrorCode, msg string, errs ...servus.ErrorDetail) error {
	if err == nil {
		return nil
	}

	if prevErr, ok := err.(servus.ServiceError); ok {
		errCode := code
		if code == apiError.Idem {
			errCode = apiError.ErrorCode(prevErr.Code)
		}
		errDetail := append(prevErr.Errors, servus.ErrorDetail{
			ErrorCode: prevErr.Code,
			Message:   prevErr.Message,
		})
		return servus.ServiceError{
			HTTPCode: errCode.HTTPStatusCode(),
			Code:     string(errCode),
			Message:  msg,
			Errors:   append(errDetail, errs...),
		}
	}

	return servus.ServiceError{
		HTTPCode: code.HTTPStatusCode(),
		Code:     string(code),
		Message:  err.Error(),
		Errors:   errs,
	}
}

// IsErrorCodeExist checks if the error code exists in the error object
func IsErrorCodeExist(err error, code apiError.ErrorCode) bool {
	if err == nil {
		return false
	}

	// Check if the error is a servus main error
	servusErr, ok := err.(servus.ServiceError)
	if !ok {
		return false
	}

	// check on main err struct
	if servusErr.Code == string(code) {
		return true
	}

	// Check on error details
	for _, errDetail := range servusErr.Errors {
		if errDetail.ErrorCode == string(code) {
			return true
		}
	}

	return false
}

// GetHTTPErrorResponse decodes the error object and returns appropriate http error response.
// Use this later to handle external call whenever the service is using servus
func GetHTTPErrorResponse(err error, message string) error {
	if err == nil {
		return nil
	}
	if httpError, ok := err.(*errorhandling.Error); ok {
		if code, ok := apiError.StatusCodeToErrorCode[httpError.HTTPCode]; ok {
			return Error(code, message)
		}
	}

	return apiError.DefaultInternalServerError
}
