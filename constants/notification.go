package constants

import "gitlab.super-id.net/bersama/opsce/onedash-be/api"

// ActionType is for define notification template by action
type ActionType string

// List of action that use send notification
const (
	// BlockAccountAction ...
	BlockAccountAction ActionType = "block_account"

	// UnblockAccountAction ...
	UnblockAccountAction ActionType = "unblock_account"

	// UpdateCustomerDataAction ...
	UpdateCustomerDataAction ActionType = "update_customer_data"
)

const (
	// Push template name in config
	blockAccountPushTemplate       = "push_block_account"
	unblockAccountPushTemplate     = "push_unblock_account"
	updateCustomerDataPushTemplate = "push_update_customer_data"

	// Email template name in config
	blockAccountEmailTemplate       = "email_block_account"
	unblockAccountEmailTemplate     = "email_unblock_account"
	updateCustomerDataEmailTemplate = "email_update_customer_data"

	// Push inbox template name in config
	blockAccountPushInboxTemplate       = "push_inbox_block_account"
	unblockAccountPushInboxTemplate     = "push_inbox_block_account"
	updateCustomerDataPushInboxTemplate = "push_inbox_update_customer_data"
)

// Templates has the templates for different type of notifications
type Templates struct {
	PushTemplate      string
	EmailTemplate     string
	PushInboxTemplate string
}

// NotificationTemplateMap is for mapping template by action
var NotificationTemplateMap = map[ActionType]Templates{
	BlockAccountAction: {
		PushTemplate:      blockAccountPushTemplate,
		EmailTemplate:     blockAccountEmailTemplate,
		PushInboxTemplate: blockAccountPushInboxTemplate,
	},
	UnblockAccountAction: {
		PushTemplate:      unblockAccountPushTemplate,
		EmailTemplate:     unblockAccountEmailTemplate,
		PushInboxTemplate: unblockAccountPushInboxTemplate,
	},
	UpdateCustomerDataAction: {
		PushTemplate:      updateCustomerDataPushTemplate,
		EmailTemplate:     updateCustomerDataEmailTemplate,
		PushInboxTemplate: updateCustomerDataPushInboxTemplate,
	},
}

// NotificationDescriptionMap is for mapping the audit trail description for send notification
var NotificationDescriptionMap = map[api.SendNotificationType]string{
	api.SendNotificationType_PUSH_INBOX: "Push Inbox",
	api.SendNotificationType_PUSH:       "Push",
	api.SendNotificationType_EMAIL:      "Email",
}
