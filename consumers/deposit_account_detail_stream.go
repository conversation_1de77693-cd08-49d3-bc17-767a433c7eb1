// Package consumers ...
package consumers

import (
	"context"
	"fmt"
	"runtime/debug"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/account_detail"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/stream"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

var (
	dcConsumer kafkareader.Client
)

// startConsumeDepositsAccountDetailStream creates Kafka Read and Register the method responsible to consumer
func startConsumeDepositsAccountDetailStream(conf *config.AppConfig) {
	reader, err := streams.NewStaticReader(context.Background(), constants.DepositsAccountDetailsStreamConsumerLogTag,
		convertConfig(conf.DepositsAccountDetailEventKafkaConfig, conf.DepositsAccountDetailEventKafkaConfig.DtoName), &account_detail.AccountDetail{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	dcConsumer = reader
	registerDepositsCoreHandler(constants.DepositsAccountDetailsStreamConsumerLogTag, dcConsumer.GetDataAckChan(), conf)
}

var registerDepositsCoreHandler = func(tag string, ch <-chan *kafkareader.AckEntity, conf *config.AppConfig) {
	wg.Go(tag, func() {
		consumeDepositsAccountDetailStream(context.Background(), ch, conf)
	})
}

// consumeDepositsAccountDetailStream receives event from stream and pass it to processing methods
func consumeDepositsAccountDetailStream(ctx context.Context, ch <-chan *kafkareader.AckEntity, conf *config.AppConfig) {
	streamName := conf.DepositsAccountDetailEventKafkaConfig.TopicName
	depositsAccountDetailsDTO := conf.DepositsAccountDetailEventKafkaConfig.DtoName
	defer func() {
		if errDepositsAccountDetailStream := recover(); errDepositsAccountDetailStream != nil {
			slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamConsumerLogTag, fmt.Sprintf("Panic occurred in consumeDepositsAccountDetailStream , error: %s, stacktrace %s", errDepositsAccountDetailStream, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.DepositsAccountDetailsStreamConsumerLogTag, "Executed consumeDepositsAccountDetailStream successfully")
		}
	}()
	for event := range ch {
		depositsAccountDetailsStream, ok := event.Event.(*account_detail.AccountDetail)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.DepositsAccountDetailsStreamConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(streamName, depositsAccountDetailsDTO)...)
			continue
		}
		if err := stream.HandleDepositsAccountDetailsStream(ctx, conf, *depositsAccountDetailsStream); err != nil {
			slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamConsumerLogTag, "Handling stream event failed",
				commonTags(streamName, depositsAccountDetailsDTO, tags.T("error", err))...)
		}
		if err := event.Ack(); err != nil {
			slog.FromContext(ctx).Error(constants.DepositsAccountDetailsStreamConsumerLogTag,
				"failed to ack message from deposits-account-detail", slog.Error(err))
		}
	}
}
