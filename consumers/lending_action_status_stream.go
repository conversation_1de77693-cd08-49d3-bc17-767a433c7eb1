// Package consumers provides the background process to consume the lending action status stream.
//
// nolint: dupl
package consumers

import (
	"context"
	"fmt"
	"runtime/debug"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/ops_lending_action_status_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	lendingactionstatus "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/stream/lending_action_status"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

var (
	lwConsumer kafkareader.Client
	lwTopic    string
)

const (
	// LendingActionStatusStream ...
	LendingActionStatusStream streams.StreamID = "lendingWriteOffStream"
)

// startConsumeLendingActionStatusStream creates Kafka Read and Register the method responsible to consumer
func startConsumeLendingActionStatusStream(conf *config.AppConfig) {
	reader, err := streams.NewStaticReader(context.Background(), LendingActionStatusStream,
		convertConfig(conf.LendingActionStatusKafkaConfig, conf.LendingActionStatusKafkaConfig.DtoName), &ops_lending_action_status_event.OpsLendingActionStatusEvent{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	lwConsumer = reader
	lwTopic = conf.LendingActionStatusKafkaConfig.TopicName

	registerLoanWriteOffHandler(constants.LendingActionStatusKafkaTag, lwConsumer.GetDataChan(), conf)
}

var registerLoanWriteOffHandler = func(tag string, ch <-chan *kafkareader.Entity, conf *config.AppConfig) {
	wg.Go(tag, func() {
		consumeLendingActionStatusStream(context.Background(), ch, conf)
	})
}

// consumeLendingActionStatusStream receives event from stream and pass it to processing methods
//
// nolint: dupl
func consumeLendingActionStatusStream(ctx context.Context, ch <-chan *kafkareader.Entity, conf *config.AppConfig) {
	defer func() {
		if errLendingActionStatusStream := recover(); errLendingActionStatusStream != nil {
			slog.FromContext(ctx).Warn(constants.LendingActionStatusKafkaTag, fmt.Sprintf("Panic occurred in consumeLendingActionStatusStream , error: %s, stacktrace %s", errLendingActionStatusStream, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Info(constants.LendingActionStatusKafkaTag, "Executed consumeLendingActionStatusStream successfully")
		}
	}()
	for event := range ch {
		data, ok := event.Event.(*ops_lending_action_status_event.OpsLendingActionStatusEvent)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.LendingActionStatusKafkaTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(lwTopic, conf.LendingActionStatusKafkaConfig.DtoName)...)
			continue
		}

		err := lendingactionstatus.Handle(ctx, conf, data)
		if err != nil {
			Stats(ctx, constants.LendingActionStatusKafkaTag, "ERROR", err.Error())
			slog.FromContext(ctx).Warn(constants.LendingActionStatusKafkaTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(lwTopic, conf.LendingActionStatusKafkaConfig.DtoName)...)
		}
	}
}
