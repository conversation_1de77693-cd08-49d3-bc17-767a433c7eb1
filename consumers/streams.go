// Package consumers ...
package consumers

import (
	"context"
	"fmt"
	"time"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

const (
	streamDefaultStopTimeout = 8 * time.Second
	topicTag                 = "topic"
	logTag                   = "stream.consumers"
	dtoNameTag               = "dtoName"
)

var (
	wg           = gconcurrent.NewExecutionGroup()
	statsDClient servusStatsD.Client
)

// Init initializes stream consumer
func Init(conf *config.AppConfig, client servusStatsD.Client) {
	ctx := context.Background()
	if conf.LoanCoreKafka.Enable {
		slog.FromContext(ctx).Info(logTag, "starting to consume LoanCore stream")
		startConsumeLoanCoreStream(conf)
	}
	if conf.LendingActionStatusKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting to consume Lending Action Status stream")
		startConsumeLendingActionStatusStream(conf)
	}

	if conf.DepositsAccountDetailEventKafkaConfig.Enable {
		slog.FromContext(ctx).Info(logTag, "starting to consume deposit account detail stream")
		startConsumeDepositsAccountDetailStream(conf)
	}

	statsDClient = client
}

// Stop ..
func Stop(conf *config.AppConfig) {
	if conf.LoanCoreKafka.Enable {
		stopConsumer(lcConsumer)
	}
	if conf.LendingActionStatusKafkaConfig.Enable {
		stopConsumer(lwConsumer)
	}
	if conf.DepositsAccountDetailEventKafkaConfig.Enable {
		stopConsumer(dcConsumer)
	}
	timeoutErr := wg.WaitForDone(streamDefaultStopTimeout)
	if timeoutErr != nil {
		slog.FromContext(context.Background()).Fatal(logTag, "timeout on stopping streams, it could lead to a data loss")
	}
}

// convertConfig...
func convertConfig(conf *config.KafkaConfig, dtoName string) sndconfig.KafkaConfig {
	return sndconfig.KafkaConfig{
		Brokers:         conf.Brokers,
		ClientID:        conf.ClientID,
		ClusterType:     conf.ClusterType,
		EnableTLS:       conf.EnableTLS,
		Stream:          conf.TopicName,
		OffsetType:      conf.InitOffset,
		ConsumerVersion: sndconfig.ConsumerV2,
		PackageName:     conf.PackageName,
		DtoName:         dtoName,
	}
}

func commonTags(topic, dtoName string, extraTags ...tags.Tag) []tags.Tag {
	tagList := []tags.Tag{
		tags.T(topicTag, topic),
		tags.T(dtoNameTag, dtoName),
	}
	return append(tagList, extraTags...)
}

// stopConsumer shutdown the kafkareader Client
func stopConsumer(consumer kafkareader.Client) {
	err := consumer.Shutdown()
	if err != nil {
		slog.FromContext(context.Background()).Error(logTag, fmt.Sprintf("error in stopping kafka consumer client: %v", consumer))
	}
}

// Stats for tracking success and failure when consuming stream
func Stats(ctx context.Context, logTag string, status string, statusReason string) {
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("publishing %s metric for consuming stream", status))
	statsDClient.Count1(constants.OnedashAPI, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
