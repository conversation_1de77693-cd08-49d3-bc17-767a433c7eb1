-- Deploy onedash:029-hold-resume-case to mysql

BEGIN;

-- Add new ticket status for ON_HOLD
INSERT INTO `ticket_status` (`id`, `name`, `created_at`, `created_by`)
VALUES (14, 'On Hold', NOW(), 0);

-- Add hold-related columns to tickets table
ALTER TABLE `tickets` 
    ADD COLUMN `hold_reason` VARCHAR(100) DEFAULT NULL,
    ADD COLUMN `hold_remarks` VARCHAR(100) DEFAULT NULL,
    ADD COLUMN `on_hold_at` TIMESTAMP NULL DEFAULT NULL,
    ADD COLUMN `resumed_at` TIMESTAMP NULL DEFAULT NULL,
    ADD COLUMN `total_hold_duration_sec` INT DEFAULT 0 NOT NULL,
    ADD COLUMN `original_deadline_time` TIMESTAMP NULL DEFAULT NULL;

-- Add indexes for performance
ALTER TABLE `tickets` ADD INDEX `idx_on_hold_at` (`on_hold_at`);

COMMIT;
