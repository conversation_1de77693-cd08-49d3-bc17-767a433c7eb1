-- Deploy onedash:030-hold-resume-ticket-chains to mysql

BEGIN;

-- Add ticket chain entries for HOLD_TICKET action
-- Allow holding from active statuses (New, InProgress variants)
INSERT INTO `ticket_chain` 
    (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
SELECT 
    NOW(), 0, ts.id, 14, e.id, 'HOLD_TICKET', 8
FROM 
    elements e
CROSS JOIN 
    ticket_status ts
WHERE 
    e.has_ticketing = 1 
    AND ts.id IN (1, 2, 3, 4, 5, 10, 11, 12, 13); -- Active statuses only

-- Add ticket chain entries for RESUME_TICKET action
-- Allow resuming from on-hold (14) back to the status it was held from
INSERT INTO `ticket_chain` 
    (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
SELECT 
    NOW(), 0, 14, ts.id, e.id, 'RESUME_TICKET', 16
FROM 
    elements e
CROSS JOIN 
    ticket_status ts
WHERE 
    e.has_ticketing = 1 
    AND ts.id IN (1, 2, 3, 4, 5, 10, 11, 12, 13); -- Can resume to any active status

COMMIT;
