-- Revert onedash:026-ticket-indexes from mysql

BEGIN;

-- Drop Single-Column Indexes for tickets table
ALTER TABLE `tickets` DROP INDEX t_priority_id_idx;
ALTER TABLE `tickets` DROP INDEX t_ticket_status_id_idx;
ALTER TABLE `tickets` DROP INDEX t_created_at_idx;
ALTER TABLE `tickets` DROP INDEX t_domain_id_idx;
ALTER TABLE `tickets` DROP INDEX t_customer_segment_id_idx;

-- Drop Composite Indexes for tickets table
ALTER TABLE `tickets` DROP INDEX t_created_at_id_idx;
ALTER TABLE `tickets` DROP INDEX t_element_id_created_at_idx;
ALTER TABLE `tickets` DROP INDEX t_ticket_status_id_created_at_idx;
ALTER TABLE `tickets` DROP INDEX t_case_category_subcategory_idx;
ALTER TABLE `tickets` DROP INDEX t_case_category_created_at_idx;
ALTER TABLE `tickets` DROP INDEX t_ticket_status_id_close_datetime_idx;

-- Drop Single-Column Indexes for elements table
ALTER TABLE `elements` DROP INDEX e_name_idx;

-- Drop Single-Column Indexes for customer_segment table
ALTER TABLE `customer_segment` DROP INDEX cs_name_idx;

-- Drop Single-Column Indexes for users table
ALTER TABLE `users` DROP INDEX u_name_idx;

COMMIT;