-- Verify onedash:030-hold-resume-ticket-chains on mysql

BEGIN;

-- Verify that HOLD_TICKET and RESUME_TICKET ticket chain entries exist
SELECT COUNT(*) as hold_entries FROM `ticket_chain` WHERE action_name = 'HOLD_TICKET';
SELECT COUNT(*) as resume_entries FROM `ticket_chain` WHERE action_name = 'RESUME_TICKET';

-- Verify that the entries have correct bitwise values
SELECT DISTINCT bitwise_required FROM `ticket_chain` WHERE action_name = 'HOLD_TICKET';
SELECT DISTINCT bitwise_required FROM `ticket_chain` WHERE action_name = 'RESUME_TICKET';

ROLLBACK;
