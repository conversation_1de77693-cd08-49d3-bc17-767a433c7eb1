// Package redis ...
package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	redisLib "github.com/redis/go-redis/v9"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

var (
	// RedisClient defines the redis client
	RedisClient redis.Client
)

// Init initialize the RedisClient
func Init(app *servus.Application, conf *config.AppConfig) redis.Client {
	redisClient := redis.MakeClusterClient(conf.RedisConf, redis.StatsD(app.GetStatsD()), redis.Logger(app.GetLogger()))
	RedisClient = redisClient
	return redisClient
}

// SetRedisValue sets the redis value for the given key and cache TTL in seconds provided in config
func SetRedisValue(ctx context.Context, redisKey string, redisObject interface{}, expiryTime int64, entityTag string) error {
	binaryData, _ := json.Marshal(redisObject)
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Setting redis value for key %s", redisKey))
	_, redisSetErr := RedisClient.Set(ctx, redisKey, binaryData, time.Duration(expiryTime)*time.Second)
	if redisSetErr != nil {
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Unable to set redis value for key %s: %s", redisKey, redisSetErr.Error()))
		return redisSetErr
	}
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Setting redis object completed for key : %s", redisKey))
	return nil
}

// GetRedisValue gets the redis value for the given key
func GetRedisValue(ctx context.Context, redisKey string, entityTag string) (string, error) {
	redisValue, redisErr := RedisClient.GetString(ctx, redisKey)
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Getting Redis object for key :%s", redisKey))
	if redisErr != nil {
		if redisErr == redis.ErrNoData {
			slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("key does not exist for %s :", redisKey))
			return "", nil
		}
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Error when getting data for  %s:  error : %s", redisKey, redisErr.Error()))
		return "", redisErr
	}
	return redisValue, nil
}

// DeleteRedisKey deletes the given key
func DeleteRedisKey(ctx context.Context, redisKey string, entityTag string) error {
	_, deleteErr := RedisClient.Delete(ctx, redisKey)
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Deleting Redis object for key :%s", redisKey))
	if deleteErr != nil {
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Error while deleting key %s:  error : %s", redisKey, deleteErr.Error()))
		return deleteErr
	}
	return nil
}

// GetRedisValues gets the redis value for the given keys
func GetRedisValues(ctx context.Context, redisKeys []string, hashKey string, entityTag string) ([]interface{}, error) {
	var result *redisLib.SliceCmd
	var err error

	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Getting Redis object for keys :%s", redisKeys))
	err = RedisClient.TxPipelined(ctx, func(p redis.Pipeliner) error {
		result = p.HMGet(ctx, hashKey, redisKeys...)
		return err
	})
	if err != nil {
		slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("error in transaction pipeline %s:", err.Error()))
		return nil, nil
	}

	data, err := result.Result()

	if err != nil {
		if err == redis.ErrNoData {
			slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("key does not exist for %s :", redisKeys))
			return nil, nil
		}
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Error when getting data for  %s:  error : %s", redisKeys, err.Error()))
		return nil, err
	}
	return data, nil
}

// SetHashRedisValue sets the redis value for the given key into the same hash
func SetHashRedisValue(ctx context.Context, redisObject map[string]interface{}, hashKey string, entityTag string) error {
	object := make(map[string]interface{})
	for key, values := range redisObject {
		binaryData, _ := json.Marshal(values)
		object[key] = binaryData
	}
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Setting redis value for hash key %s", hashKey))
	_, redisSetErr := RedisClient.HSet(ctx, hashKey, object)
	if redisSetErr != nil {
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Unable to set redis value for hash key %s: %s", hashKey, redisSetErr.Error()))
		return redisSetErr
	}
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Setting redis object completed for hash key : %s", hashKey))
	return nil
}

// DeleteHashRedisValue delete the redis value for the given key
func DeleteHashRedisValue(ctx context.Context, redisKey []string, hashKey string, entityTag string) error {
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Deleting redis value for hash key %s", hashKey))
	_, redisSetErr := RedisClient.HDel(ctx, hashKey, redisKey...)
	if redisSetErr != nil {
		slog.FromContext(ctx).Warn(entityTag, fmt.Sprintf("Unable to delete redis value for hash key %s: %s", hashKey, redisSetErr.Error()))
		return redisSetErr
	}
	slog.FromContext(ctx).Info(entityTag, fmt.Sprintf("Setting redis object completed for hash key : %s", hashKey))
	return nil
}
