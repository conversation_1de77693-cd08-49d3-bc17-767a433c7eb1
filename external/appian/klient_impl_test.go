package appian

import (
	"context"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"testing"

	"gitlab.myteksi.net/dakota/klient"
)

// nolint:gocognit
func Test_klientRequest_EncodeHTTPRequest(t *testing.T) {
	type fields struct {
		ctx                context.Context
		descriptor         *klient.EndpointDescriptor
		requestBody        interface{}
		requestHeaders     http.Header
		requestQueryParams map[string]string
	}
	type args struct {
		baseURL string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *http.Request
		wantErr bool
	}{
		{
			name: "basic request without body or params",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodGet,
					Path:   "/test",
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
		},
		{
			name: "request with query params",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodGet,
					Path:   "/test",
				},
				requestQueryParams: map[string]string{
					"param1": "value1",
					"param2": "value2",
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
		},
		{
			name: "request with body",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodPost,
					Path:   "/test",
				},
				requestBody: map[string]string{"key": "value"},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
		},
		{
			name: "request with custom headers",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodGet,
					Path:   "/test",
				},
				requestHeaders: http.Header{
					"X-Custom-Header": []string{"value"},
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
		},
		{
			name: "invalid URL",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodGet,
					Path:   ":\\invalid",
				},
				requestQueryParams: map[string]string{"param": "value"},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: true,
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			req := &klientRequest{
				ctx:                tt.fields.ctx,
				descriptor:         tt.fields.descriptor,
				requestBody:        tt.fields.requestBody,
				requestHeaders:     tt.fields.requestHeaders,
				requestQueryParams: tt.fields.requestQueryParams,
			}
			got, err := req.EncodeHTTPRequest(tt.args.baseURL)
			if (err != nil) != tt.wantErr {
				t.Errorf("klientRequest.EncodeHTTPRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				// Verify the generated request
				if got.Method != tt.fields.descriptor.Method {
					t.Errorf("Wrong method, got = %v, want %v", got.Method, tt.fields.descriptor.Method)
				}

				// Check if query params are correctly set
				if tt.fields.requestQueryParams != nil {
					query := got.URL.Query()
					for k, v := range tt.fields.requestQueryParams {
						if query.Get(k) != v {
							t.Errorf("Query param mismatch for %s, got = %v, want %v", k, query.Get(k), v)
						}
					}
				}

				// Check headers
				if tt.fields.requestHeaders != nil {
					for k, v := range tt.fields.requestHeaders {
						if !reflect.DeepEqual(got.Header[k], v) {
							t.Errorf("Header mismatch for %s, got = %v, want %v", k, got.Header[k], v)
						}
					}
				}

				// Check content type for requests with body
				if tt.fields.requestBody != nil {
					if got.Header.Get(contentType) != applicationJSON {
						t.Errorf("Content-Type header not set correctly for request with body")
					}
				}
			}
		})
	}
}

func Test_klientResponse_DecodeHTTPResponse(t *testing.T) {
	type fields struct {
		responseBody interface{}
	}
	type args struct {
		res *http.Response
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "server error response",
			args: args{
				res: &http.Response{
					StatusCode: http.StatusInternalServerError,
				},
			},
			wantErr: true,
		},
		{
			name: "client error response",
			args: args{
				res: &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error": "bad request"}`)),
				},
			},
			wantErr: true,
		},
		{
			name: "no content response",
			args: args{
				res: &http.Response{
					StatusCode: http.StatusNoContent,
				},
			},
			wantErr: false,
		},
		{
			name: "successful response with body",
			fields: fields{
				responseBody: &struct{ Message string }{},
			},
			args: args{
				res: &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`{"message": "success"}`)),
				},
			},
			wantErr: false,
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			resp := &klientResponse{
				responseBody: tt.fields.responseBody,
			}
			if err := resp.DecodeHTTPResponse(tt.args.res); (err != nil) != tt.wantErr {
				t.Errorf("klientResponse.DecodeHTTPResponse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_klientEncodedRequest_EncodeHTTPRequest(t *testing.T) {
	type fields struct {
		ctx         context.Context
		descriptor  *klient.EndpointDescriptor
		requestBody interface{}
	}
	type args struct {
		baseURL string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		check   func(*testing.T, *http.Request)
	}{
		{
			name: "basic encoded request without body",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodGet,
					Path:   "/test",
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
			check: func(t *testing.T, r *http.Request) {
				if r.Method != http.MethodGet {
					t.Errorf("expected GET method, got %s", r.Method)
				}
				if r.URL.String() != "http://example.com/test" {
					t.Errorf("expected URL http://example.com/test, got %s", r.URL.String())
				}
			},
		},
		{
			name: "encoded request with form data",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: http.MethodPost,
					Path:   "/auth",
				},
				requestBody: url.Values{
					"grant_type":    {"client_credentials"},
					"client_id":     {"test_id"},
					"client_secret": {"test_secret"},
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: false,
			check: func(t *testing.T, r *http.Request) {
				if r.Header.Get(contentType) != applicationURLEncoded {
					t.Errorf("expected Content-Type %s, got %s", applicationURLEncoded, r.Header.Get(contentType))
				}
				if r.Method != http.MethodPost {
					t.Errorf("expected POST method, got %s", r.Method)
				}
			},
		},
		{
			name: "invalid request",
			fields: fields{
				ctx: context.Background(),
				descriptor: &klient.EndpointDescriptor{
					Method: "INVALID",
					Path:   ":\\invalid",
				},
			},
			args: args{
				baseURL: "http://example.com",
			},
			wantErr: true,
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			req := &klientEncodedRequest{
				ctx:         tt.fields.ctx,
				descriptor:  tt.fields.descriptor,
				requestBody: tt.fields.requestBody,
			}
			got, err := req.EncodeHTTPRequest(tt.args.baseURL)
			if (err != nil) != tt.wantErr {
				t.Errorf("klientEncodedRequest.EncodeHTTPRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.check != nil {
				tt.check(t, got)
			}
		})
	}
}
