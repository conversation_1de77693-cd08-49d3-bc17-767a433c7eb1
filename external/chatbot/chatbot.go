package chatbot

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

var (
	addKnowledgeBaseDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addKnowledgeBaseDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/knowledge-bases",
	}

	updateKnowledgeBaseDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "updateKnowledgeBaseDescriptor",
		Method:  http.MethodPut,
		Path:    "/api/knowledge-bases/{id}",
	}

	deleteKnowledgeBaseDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "deleteKnowledgeBaseDescriptor",
		Method:  http.MethodDelete,
		Path:    "/api/knowledge-bases/{id}",
	}

	importKnowledgeBaseDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "importKnowledgeBaseDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/knowledge-bases/import",
	}

	addChatbotConfigsDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addChatbotConfigsDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/chatbot-configs",
	}

	addEscapeKeywordsDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addEscapeKeywordsDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/escape-keywords",
	}

	addSystemPromptDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addSystemPromptDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/system-prompt",
	}

	addCustomerWhitelistDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addCustomerWhitelistDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/customers/whitelists",
	}

	deleteCustomerWhitelistDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "deleteCustomerWhitelistDescriptor",
		Method:  http.MethodDelete,
		Path:    "/api/customers/whitelists/{id}",
	}

	importCustomerWhitelistDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "importCustomerWhitelistDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/customers/whitelists/import",
	}

	addTagDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "addTagDescriptor",
		Method:  http.MethodPost,
		Path:    "/api/knowledge-bases/tags",
	}

	updateTagDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "updateTagDescriptor",
		Method:  http.MethodPut,
		Path:    "/api/knowledge-bases/tags/{id}",
	}

	deleteTagDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "deleteTagDescriptor",
		Method:  http.MethodDelete,
		Path:    "/api/knowledge-bases/tags/{id}",
	}
)

// makeRequest is a helper function to make HTTP requests
func (c *Client) makeRequest(ctx context.Context, descriptor *klient.EndpointDescriptor, requestBody interface{}, queryParams map[string]string, isMultipart bool) (interface{}, error) {
	ctx = klient.MakeContext(ctx, descriptor)

	var responseBody interface{}
	err := c.machinery.RoundTrip(ctx, &klientRequest{
		ctx:                ctx,
		descriptor:         descriptor,
		requestBody:        requestBody,
		requestQueryParams: queryParams,
		isMultipart:        isMultipart,
	}, &klientResponse{responseBody: &responseBody})

	if err != nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("Failed to call chatbot endpoint %s: %v", descriptor.Path, err))
		return responseBody, err
	}

	return responseBody, nil
}

// Common response handling
type responseHandler struct {
	ctx     context.Context
	client  *Client
	request *klientRequest
}

func (c *Client) newResponseHandler(ctx context.Context) *responseHandler {
	return &responseHandler{
		ctx:    ctx,
		client: c,
		request: &klientRequest{
			ctx: ctx,
		},
	}
}

// handleImport is a helper function to handle file imports
func (c *Client) handleImport(ctx context.Context, descriptor *klient.EndpointDescriptor, req interface{}) (interface{}, error) {
	// Extract common fields from request
	formData := map[string]interface{}{
		"file":       reflect.ValueOf(req).Elem().FieldByName("File").Interface(),
		"created_by": reflect.ValueOf(req).Elem().FieldByName("CreatedBy").Interface(),
		"mode":       reflect.ValueOf(req).Elem().FieldByName("Mode").Interface(),
		"file_name":  reflect.ValueOf(req).Elem().FieldByName("FileName").Interface(),
	}

	// make the request
	result, err := c.makeRequest(ctx, descriptor, formData, nil, true)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (h *responseHandler) handleRequest(descriptor *klient.EndpointDescriptor, req interface{}, resp interface{}) error {
	slog.FromContext(h.ctx).Info(logTag, fmt.Sprintf("calling chatbot %s", descriptor.Name))

	result, err := h.client.makeRequest(h.ctx, descriptor, req, nil, false)
	if err != nil {
		return err
	}

	if err := utils.LoadStruct(result, resp); err != nil {
		slog.FromContext(h.ctx).Error(logTag, fmt.Sprintf("Failed to assert response type: %v", err))
		return err
	}

	slog.FromContext(h.ctx).Info(logTag, fmt.Sprintf("successfully called chatbot %s", descriptor.Name))
	return nil
}

func (h *responseHandler) handleRequestWithID(descriptor *klient.EndpointDescriptor, id int, req interface{}, resp interface{}) error {
	desc := *descriptor
	desc.Path = strings.Replace(desc.Path, "{id}", strconv.Itoa(id), 1)
	return h.handleRequest(&desc, req, resp)
}

// AddKnowledgeBaseRequest is the request struct for AddKnowledgeBase
type AddKnowledgeBaseRequest struct {
	Text      string `json:"text"`
	TagIDs    []int  `json:"tag_ids"`
	CreatedBy string `json:"created_by"`
}

// AddKnowledgeBaseResponse is the response struct for AddKnowledgeBase
type AddKnowledgeBaseResponse struct {
	ID        int     `json:"id"`
	Text      string  `json:"text"`
	CreatedAt string  `json:"created_at"`
	UpdatedAt string  `json:"updated_at"`
	CreatedBy string  `json:"created_by"`
	UpdatedBy string  `json:"updated_by"`
	ExpiredAt *string `json:"expired_at"`
	Tags      []Tag   `json:"tags"`
}

// Tag represents a tag for a knowledge base
type Tag struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// AddKnowledgeBase calls the customerportal addKnowledgeBase endpoint
// Example of simplified endpoint functions:
func (c *Client) AddKnowledgeBase(ctx context.Context, req *AddKnowledgeBaseRequest) (*AddKnowledgeBaseResponse, error) {
	var resp AddKnowledgeBaseResponse
	err := c.newResponseHandler(ctx).handleRequest(addKnowledgeBaseDescriptor, req, &resp)
	return &resp, err
}

// UpdateKnowledgeBaseRequest represents the request body for updating a knowledge base
type UpdateKnowledgeBaseRequest struct {
	ID        int    `json:"id"`
	Text      string `json:"text,omitempty"`
	TagIDs    []int  `json:"tag_ids,omitempty"`
	UpdatedBy string `json:"updated_by"`
	ExpiredAt string `json:"expired_at,omitempty"`
}

// UpdateKnowledgeBaseResponse represents the response from updating a knowledge base
type UpdateKnowledgeBaseResponse struct {
	ID        int     `json:"id"`
	Text      string  `json:"text"`
	CreatedAt string  `json:"created_at"`
	UpdatedAt string  `json:"updated_at"`
	CreatedBy string  `json:"created_by"`
	UpdatedBy string  `json:"updated_by"`
	ExpiredAt *string `json:"expired_at"`
	Tags      []Tag   `json:"tags"`
}

// UpdateKnowledgeBase updates an existing knowledge base with the given ID
func (c *Client) UpdateKnowledgeBase(ctx context.Context, req *UpdateKnowledgeBaseRequest) (*UpdateKnowledgeBaseResponse, error) {
	var resp UpdateKnowledgeBaseResponse
	err := c.newResponseHandler(ctx).handleRequestWithID(updateKnowledgeBaseDescriptor, req.ID, req, &resp)
	return &resp, err
}

// DeleteKnowledgeBaseRequest represents the request for deleting a knowledge base
type DeleteKnowledgeBaseRequest struct {
	ID int `json:"id"`
}

// DeleteKnowledgeBaseResponse represents the response from deleting a knowledge base
type DeleteKnowledgeBaseResponse struct {
	Message string `json:"message"`
}

// DeleteKnowledgeBase deletes an existing knowledge base with the given ID
func (c *Client) DeleteKnowledgeBase(ctx context.Context, req *DeleteKnowledgeBaseRequest) (*DeleteKnowledgeBaseResponse, error) {
	var resp DeleteKnowledgeBaseResponse
	err := c.newResponseHandler(ctx).handleRequestWithID(deleteKnowledgeBaseDescriptor, req.ID, nil, &resp)
	return &resp, err
}

// ImportKnowledgeBaseRequest represents the request for importing knowledge bases
type ImportKnowledgeBaseRequest struct {
	File      []byte `json:"file"` // CSV file contents
	FileName  string `json:"file_name"`
	CreatedBy string `json:"created_by"`
	Mode      string `json:"mode"` // "append" or other modes
}

// ImportKnowledgeBaseResponse represents the response from importing knowledge bases
type ImportKnowledgeBaseResponse struct {
	Message string `json:"message"`
}

// ImportKnowledgeBase imports knowledge bases from a CSV file
func (c *Client) ImportKnowledgeBase(ctx context.Context, req *ImportKnowledgeBaseRequest) (*ImportKnowledgeBaseResponse, error) {
	var resp ImportKnowledgeBaseResponse

	result, err := c.handleImport(ctx, importKnowledgeBaseDescriptor, req)
	if err != nil {
		return nil, err
	}

	if err := utils.LoadStruct(result, &resp); err != nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("Failed to assert response type: %v", err))
		return nil, err
	}

	return &resp, nil
}

// InactivityTimeouts defines the timeout configuration for chat sessions
type InactivityTimeouts struct {
	InitialMinutes *int `json:"initial_minutes,omitempty"`
	FinalMinutes   *int `json:"final_minutes,omitempty"`
}

// Configs represents the configuration settings for the chatbot
type Configs struct {
	Enabled                         *bool               `json:"enabled,omitempty"`
	ResponseDelaySeconds            *int                `json:"response_delay_seconds,omitempty"`
	MaxConversationRounds           *int                `json:"max_conversation_rounds,omitempty"`
	MaxConversationWithoutKnowledge *int                `json:"max_conversation_without_knowledge,omitempty"`
	InactivityTimeouts              *InactivityTimeouts `json:"inactivity_timeouts,omitempty"`
	ForceEndChatThreshold           *int                `json:"force_end_chat_threshold,omitempty"`
}

// AddChatbotConfigRequest represents the request for adding chatbot configs
type AddChatbotConfigRequest struct {
	Configs   Configs `json:"configs"`
	CreatedBy string  `json:"created_by"`
}

// AddChatbotConfigResponse represents the response from adding chatbot configs
type AddChatbotConfigResponse struct {
	ID              int                    `json:"id"`
	CreatedAt       string                 `json:"created_at"`
	Configs         map[string]interface{} `json:"configs"`
	PreviousConfigs map[string]interface{} `json:"previous_configs"`
	CreatedBy       string                 `json:"created_by"`
}

// AddChatbotConfig adds a new chatbot configuration
func (c *Client) AddChatbotConfig(ctx context.Context, req *AddChatbotConfigRequest) (*AddChatbotConfigResponse, error) {
	var resp AddChatbotConfigResponse
	err := c.newResponseHandler(ctx).handleRequest(addChatbotConfigsDescriptor, req, &resp)
	return &resp, err
}

// AddEscapeKeywordsRequest represents the request for adding escape keywords
type AddEscapeKeywordsRequest struct {
	Keywords         string `json:"keywords"`
	PreviousKeywords string `json:"previous_keywords,omitempty"`
	CreatedBy        string `json:"created_by"`
}

// AddEscapeKeywordsResponse represents the response from adding escape keywords
type AddEscapeKeywordsResponse struct {
	ID               int    `json:"id"`
	Keywords         string `json:"keywords"`
	PreviousKeywords string `json:"previous_keywords,omitempty"`
	CreatedBy        string `json:"created_by"`
	CreatedAt        string `json:"created_at"`
}

// AddEscapeKeywords calls the chatbot addEscapeKeywords endpoint
func (c *Client) AddEscapeKeywords(ctx context.Context, req *AddEscapeKeywordsRequest) (*AddEscapeKeywordsResponse, error) {
	var resp AddEscapeKeywordsResponse
	err := c.newResponseHandler(ctx).handleRequest(addEscapeKeywordsDescriptor, req, &resp)
	return &resp, err
}

// SystemPromptRequest represents the request for adding a system prompt
type SystemPromptRequest struct {
	PromptText         string `json:"prompt_text"`
	PreviousPromptText string `json:"previous_prompt_text"`
	CreatedBy          string `json:"created_by"`
}

// SystemPromptResponse represents the response from adding a system prompt
type SystemPromptResponse struct {
	ID                 int    `json:"id"`
	PromptText         string `json:"prompt_text"`
	PreviousPromptText string `json:"previous_prompt_text,omitempty"`
	CreatedAt          string `json:"created_at"`
	CreatedBy          string `json:"created_by"`
}

// AddSystemPrompt adds a new system prompt
func (c *Client) AddSystemPrompt(ctx context.Context, req *SystemPromptRequest) (*SystemPromptResponse, error) {
	var resp SystemPromptResponse
	err := c.newResponseHandler(ctx).handleRequest(addSystemPromptDescriptor, req, &resp)
	return &resp, err
}

// AddCustomerWhitelistRequest represents the request for adding a customer to whitelist
type AddCustomerWhitelistRequest struct {
	SafeID    string `json:"safe_id"`
	CreatedBy string `json:"created_by"`
}

// AddCustomerWhitelistResponse represents the response from adding a customer to whitelist
type AddCustomerWhitelistResponse struct {
	Message string `json:"message"`
}

// AddCustomerWhitelist adds a customer to the whitelist
func (c *Client) AddCustomerWhitelist(ctx context.Context, req *AddCustomerWhitelistRequest) (*AddCustomerWhitelistResponse, error) {
	var resp AddCustomerWhitelistResponse
	err := c.newResponseHandler(ctx).handleRequest(addCustomerWhitelistDescriptor, req, &resp)
	return &resp, err
}

// DeleteCustomerWhitelistRequest represents the request for deleting a customer from whitelist
type DeleteCustomerWhitelistRequest struct {
	ID int `json:"id"`
}

// DeleteCustomerWhitelistResponse represents the response from deleting a customer from whitelist
type DeleteCustomerWhitelistResponse struct {
	Message string `json:"message"`
}

// DeleteCustomerWhitelist removes a customer from the whitelist
func (c *Client) DeleteCustomerWhitelist(ctx context.Context, req *DeleteCustomerWhitelistRequest) (*DeleteCustomerWhitelistResponse, error) {
	var resp DeleteCustomerWhitelistResponse
	err := c.newResponseHandler(ctx).handleRequestWithID(deleteCustomerWhitelistDescriptor, req.ID, nil, &resp)
	return &resp, err
}

// ImportCustomerWhitelistRequest represents the request for importing customer whitelists
type ImportCustomerWhitelistRequest struct {
	File      []byte `json:"file"` // CSV file contents
	CreatedBy string `json:"created_by"`
	Mode      string `json:"mode"` // "append" or other modes
	FileName  string `json:"file_name"`
}

// ImportCustomerWhitelistResponse represents the response from importing customer whitelists
type ImportCustomerWhitelistResponse struct {
	Message string `json:"message"`
}

// ImportCustomerWhitelist imports customer whitelists from a CSV file
func (c *Client) ImportCustomerWhitelist(ctx context.Context, req *ImportCustomerWhitelistRequest) (*ImportCustomerWhitelistResponse, error) {
	var resp ImportCustomerWhitelistResponse

	result, err := c.handleImport(ctx, importCustomerWhitelistDescriptor, req)
	if err != nil {
		return nil, err
	}

	if err := utils.LoadStruct(result, &resp); err != nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("Failed to assert response type: %v", err))
		return nil, err
	}

	return &resp, nil
}

// AddTagRequest represents the request for adding a new tag
type AddTagRequest struct {
	Name      string `json:"name"`
	CreatedBy string `json:"created_by"`
}

// AddTagResponse represents the response from adding a knowledge base tag
type AddTagResponse struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	CreatedAt string `json:"created_at"`
	CreatedBy string `json:"created_by"`
}

// AddTag adds a new tag
func (c *Client) AddTag(ctx context.Context, req *AddTagRequest) (*AddTagResponse, error) {
	var resp AddTagResponse
	err := c.newResponseHandler(ctx).handleRequest(addTagDescriptor, req, &resp)
	return &resp, err
}

// UpdateTagRequest represents the request for updating a tag
type UpdateTagRequest struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	UpdatedBy string `json:"updated_by"`
}

// UpdateTagResponse represents the response from updating a tag
type UpdateTagResponse struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
	CreatedBy string `json:"created_by"`
	UpdatedBy string `json:"updated_by"`
}

// UpdateTag updates an existing tag
func (c *Client) UpdateTag(ctx context.Context, req *UpdateTagRequest) (*UpdateTagResponse, error) {
	var resp UpdateTagResponse
	err := c.newResponseHandler(ctx).handleRequestWithID(updateTagDescriptor, req.ID, req, &resp)
	return &resp, err
}

// DeleteTagRequest represents the request for deleting a tag
type DeleteTagRequest struct {
	ID int `json:"id"`
}

// DeleteTagResponse represents the response from deleting a tag
type DeleteTagResponse struct {
	Message string `json:"message"`
}

// DeleteTag deletes an existing tag
func (c *Client) DeleteTag(ctx context.Context, req *DeleteTagRequest) (*DeleteTagResponse, error) {
	var resp DeleteTagResponse
	err := c.newResponseHandler(ctx).handleRequestWithID(deleteTagDescriptor, req.ID, nil, &resp)
	return &resp, err
}
