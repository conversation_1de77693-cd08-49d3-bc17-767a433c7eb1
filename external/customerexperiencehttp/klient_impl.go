package customerexperiencehttp

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"

	_go "github.com/json-iterator/go"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
)

const (
	contentType     = "Content-Type"
	applicationJSON = "application/json"
)

type klientRequest struct {
	ctx                context.Context
	descriptor         *klient.EndpointDescriptor
	requestBody        interface{}
	requestHeaders     http.Header
	requestQueryParams map[string]string
}

// EncodeHTTPRequest implements klient.Request
func (req *klientRequest) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	fullPath := fmt.Sprintf("%s%s", baseURL, req.descriptor.Path)

	if req.requestQueryParams != nil {
		fullURL, err := url.Parse(fullPath)
		if err != nil {
			slog.FromContext(req.ctx).Warn(logTag, "fail to parse url",
				slog.Error(err), tags.T("desc", req.descriptor.Name), tags.T("reqQueryParams", req.requestQueryParams))
			return nil, err
		}
		params := url.Values{}
		for k, v := range req.requestQueryParams {
			params.Add(k, v)
		}
		fullURL.RawQuery = params.Encode()
		fullPath = fullURL.String()
	}

	var body io.Reader
	if req.requestBody != nil {
		jsonBytes, err := _go.Marshal(req.requestBody)
		if err != nil {
			slog.FromContext(req.ctx).Warn(logTag, "fail to marshal request body",
				slog.Error(err), tags.T("desc", req.descriptor.Name), tags.T("reqBody", req.requestBody))
			return nil, err
		}
		body = bytes.NewBuffer(jsonBytes)
	}
	httpReq, err := http.NewRequest(req.descriptor.Method, fullPath, body)
	if err != nil {
		slog.FromContext(req.ctx).Warn(logTag, "fail to create new HTTP request",
			slog.Error(err), tags.T("desc", req.descriptor.Name))
		return nil, err
	}

	if req.requestHeaders != nil {
		httpReq.Header = req.requestHeaders
	}

	if body != nil {
		httpReq.Header.Add(contentType, applicationJSON)
	}
	slog.FromContext(req.ctx).Debug(logTag, fmt.Sprintf("fullPath: %v, method: %v, body: %v, header: %v", fullPath, req.descriptor.Method, body, httpReq.Header))
	return httpReq, nil
}

type klientResponse struct {
	responseBody interface{}
}

// DecodeHTTPResponse implements klient.Response
func (resp *klientResponse) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= http.StatusInternalServerError {
		statusCode := strconv.Itoa(res.StatusCode)
		return fmt.Errorf("%s_%s", constants.ErrServerGeneric, statusCode)
	}

	if res.StatusCode >= http.StatusBadRequest {
		return errorhandling.UnmarshalError(res)
	}

	if res.StatusCode == http.StatusNoContent {
		return nil
	}

	return _go.NewDecoder(res.Body).Decode(resp.responseBody)
}
