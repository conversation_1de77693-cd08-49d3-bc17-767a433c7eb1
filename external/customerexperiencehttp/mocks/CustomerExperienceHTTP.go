// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	customerexperiencehttp "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
)

// CustomerExperienceHTTP is an autogenerated mock type for the CustomerExperienceHTTP type
type CustomerExperienceHTTP struct {
	mock.Mock
}

// GetVideoCallOps provides a mock function with given fields: ctx, applicationID, userID
func (_m *CustomerExperienceHTTP) GetVideoCallOps(ctx context.Context, applicationID string, userID string) (*customerexperiencehttp.GetVideoCallOpsResponse, error) {
	ret := _m.Called(ctx, applicationID, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetVideoCallOps")
	}

	var r0 *customerexperiencehttp.GetVideoCallOpsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*customerexperiencehttp.GetVideoCallOpsResponse, error)); ok {
		return rf(ctx, applicationID, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *customerexperiencehttp.GetVideoCallOpsResponse); ok {
		r0 = rf(ctx, applicationID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*customerexperiencehttp.GetVideoCallOpsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, applicationID, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCustomerExperienceHTTP creates a new instance of CustomerExperienceHTTP. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCustomerExperienceHTTP(t interface {
	mock.TestingT
	Cleanup(func())
}) *CustomerExperienceHTTP {
	mock := &CustomerExperienceHTTP{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
