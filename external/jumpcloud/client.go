package jumpcloud

import (
	"context"
	"net/http"
)

// Config ...
type Config struct {
	BaseURL string `json:"baseURL"`
}

// UserInfo ...
type UserInfo struct {
	Email     string `json:"email"`
	FirstName string `json:"first-name"`
	LastName  string `json:"last-name"`
}

// JumpCloud ...
type JumpCloud interface {
	VerifyEmail(ctx context.Context, token string, logTag string) (UserInfo, error)
}

// Client ...
type Client struct {
	Client  http.Client
	BaseURL string
}

var (
	// JCClient ...
	JCClient JumpCloud = &Client{}
)

// Init ...
func Init(config *Config) {
	JCClient = &Client{
		Client:  http.Client{},
		BaseURL: config.BaseURL,
	}
}
