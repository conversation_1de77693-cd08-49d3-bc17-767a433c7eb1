// Package jumpcloud is for handle sso capability with jumpcloud
package jumpcloud

import (
	"context"
	"encoding/json"
	"net/http"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"
)

// VerifyEmail ...
func (c *Client) VerifyEmail(ctx context.Context, token string, logTag string) (UserInfo, error) {
	var data UserInfo
	request, err := http.NewRequest("GET", c.BaseURL, nil)
	if err != nil {
		return UserInfo{}, err
	}

	request.Header.Set("Authorization", "BEARER "+token)
	response, err := c.Client.Do(request)
	if err != nil {
		return UserInfo{}, err
	}
	defer func() {
		err = response.Body.Close()
		if err != nil {
			slogwrapper.FromContext(ctx).Warn(logTag, "failed to close response body")
		}
	}()

	if response.StatusCode != 200 {
		return UserInfo{}, errorwrapper.Error(apiError.Idem, "Couldn't login using jumpcloud")
	}
	err = json.NewDecoder(response.Body).Decode(&data)
	if err != nil {
		return UserInfo{}, err
	}
	return data, nil
}
