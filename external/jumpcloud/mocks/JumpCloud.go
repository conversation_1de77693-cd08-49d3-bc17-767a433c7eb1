// Code generated by mockery v2.44.1. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	jumpcloud "gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
)

// JumpCloud is an autogenerated mock type for the JumpCloud type
type JumpCloud struct {
	mock.Mock
}

// VerifyEmail provides a mock function with given fields: ctx, token, logTag
func (_m *JumpCloud) VerifyEmail(ctx context.Context, token string, logTag string) (jumpcloud.UserInfo, error) {
	ret := _m.Called(ctx, token, logTag)

	if len(ret) == 0 {
		panic("no return value specified for VerifyEmail")
	}

	var r0 jumpcloud.UserInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (jumpcloud.UserInfo, error)); ok {
		return rf(ctx, token, logTag)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) jumpcloud.UserInfo); ok {
		r0 = rf(ctx, token, logTag)
	} else {
		r0 = ret.Get(0).(jumpcloud.UserInfo)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, token, logTag)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewJumpCloud creates a new instance of JumpCloud. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewJumpCloud(t interface {
	mock.TestingT
	Cleanup(func())
}) *JumpCloud {
	mock := &JumpCloud{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
