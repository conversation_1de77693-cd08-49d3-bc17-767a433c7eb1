package ops

import (
	"context"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/initialize"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

const (
	logTag  = "opsClient"
	service = "ops-service"
)

// OpsHTTP defines methods to interact with the ops service.
type OpsHTTP interface {
	ExecuteFile(ctx context.Context, body *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error)
}

// OpsClient implements OpsHTTP using HTTP requests.
type OpsClient struct {
	machinery klient.RoundTripper
}

// NewOpsHTTPClient initializes a new OpsClient.
func NewOpsHTTPClient(baseURL string, options ...klient.Option) (*OpsClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &OpsClient{
		machinery: roundTripper,
	}, nil
}
