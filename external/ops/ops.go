// Package customerexperiencehttp provides functionalities to call customer-experience services
package ops

import (
	"context"
	"net/http"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

var (
	executeFileDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "execute-file",
		Method:  http.MethodPost,
		Path:    "/api/v2/ops-service/execute-file",
	}
)

// ExecuteFile fetches video call operations based on the given application ID and user ID.
func (c *OpsClient) ExecuteFile(ctx context.Context, body *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	slog.FromContext(ctx).Info(logTag, "calling ExecuteFile")
	ctx = klient.MakeContext(ctx, executeFileDescriptor)

	req := &klientRequest{
		ctx:         ctx,
		descriptor:  executeFileDescriptor,
		requestBody: &body,
	}
	var data *api.ExecuteFileResponse

	err := c.machinery.RoundTrip(ctx, req, &klientResponse{responseBody: &data})

	kResp := &api.ExecuteFileResponse{}

	if err != nil {
		slog.FromContext(ctx).Info(logTag, "failed to call ExecuteFile", slog.Error(err))
		return kResp, err
	}

	return kResp, nil
}
