module gitlab.super-id.net/bersama/opsce/onedash-be

replace (
	github.com/coreos/go-systemd => github.com/coreos/go-systemd/v22 v22.0.0
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.2
	github.com/golang-jwt/jwt/v5 => github.com/golang-jwt/jwt/v5 v5.2.2
	github.com/miekg/dns => github.com/miekg/dns v1.1.25
	github.com/satori/go.uuid => github.com/satori/go.uuid v1.2.0
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api => ./module/audittrail/api
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common => ./common
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api => ./module/permissionmanagement/api
	gitlab.myteksi.net/dakota/common/aws => gitlab.com/gx-regional/dakota/common.git/aws v1.4.3
	gitlab.myteksi.net/dakota/common/context => gitlab.com/gx-regional/dakota/common.git/context v0.17.0
	gitlab.myteksi.net/dakota/common/env-injection => gitlab.com/gx-regional/dakota/common.git/env-injection v1.0.0
	gitlab.myteksi.net/dakota/common/redis => gitlab.com/gx-regional/dakota/common.git/redis v0.19.0
	gitlab.myteksi.net/dakota/common/secrets-injection => gitlab.com/gx-regional/dakota/common.git/secrets-injection v1.0.3
	gitlab.myteksi.net/dakota/common/servicename => gitlab.com/gx-regional/dakota/common.git/servicename v1.9.1
	gitlab.myteksi.net/dakota/common/testauto => gitlab.com/gx-regional/dakota/common.git/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing => gitlab.com/gx-regional/dakota/common.git/tracing v1.10.0
	gitlab.myteksi.net/dakota/gaia => gitlab.com/gx-regional/dakota/gaia.git v0.1.1
	gitlab.myteksi.net/dakota/klient => gitlab.com/gx-regional/dakota/klient.git v1.21.1
	gitlab.myteksi.net/dakota/lending/loan-app/api => gitlab.com/gx-regional/dakota/lending.git/loan-app/api v1.88.0
	gitlab.myteksi.net/dakota/lending/loan-core/api => gitlab.com/gx-regional/dakota/lending.git/loan-core/api v1.158.0
	gitlab.myteksi.net/dakota/lending/loan-exp/api => gitlab.com/gx-regional/dakota/lending.git/loan-exp/api v1.125.0
	gitlab.myteksi.net/dakota/payment-ops-trf/api => gitlab.com/gx-regional/dakota/payment-ops-trf.git/api v1.19.0
	gitlab.myteksi.net/dakota/payment/pay-authz/api => gitlab.com/gx-regional/dakota/payment.git/pay-authz/api v0.0.0-**************-b7e43e7b435e
	gitlab.myteksi.net/dakota/schemas => gitlab.com/gx-regional/dakota/schemas.git v1.16.140-0.**************-d24c68f65440
	gitlab.myteksi.net/dakota/servus/v2 => gitlab.com/gx-regional/dakota/servus.git/v2 v2.55.0
	gitlab.myteksi.net/dakota/transaction-limit/api => gitlab.com/gx-regional/dakota/transaction-limit.git/api v1.20.0
	gitlab.myteksi.net/dbmy/core-banking/account-service/api => gitlab.com/gx-regional/dbmy/core-banking.git/account-service/api v1.70.0-dbmy
	gitlab.super-id.net/bersama/corex/customer-journal/api => gitlab.super-id.net/bersama/corex/customer-journal.git/api v1.7.0
	gitlab.super-id.net/bersama/corex/customer-journey-experience/api => gitlab.super-id.net/bersama/corex/customer-journey-experience.git/api v1.0.0
	gitlab.super-id.net/bersama/deposit/transaction-history/api => gitlab.super-id.net/bersama/deposit/transaction-history.git/api v1.20.2
	gitlab.super-id.net/bersama/fintrust/aml-service/api => gitlab.super-id.net/bersama/fintrust/aml-service.git/api v0.17.0
	gitlab.super-id.net/bersama/onboarding/application-service/api => gitlab.super-id.net/bersama/onboarding/application-service.git/api v1.36.1-migration-3.1
	gitlab.super-id.net/bersama/onboarding/customer-experience/api => gitlab.super-id.net/bersama/onboarding/customer-experience.git/api v1.45.8
	gitlab.super-id.net/bersama/onboarding/customer-master/api/v2 => gitlab.super-id.net/bersama/onboarding/customer-master.git/api/v2 v2.31.27-migration-3
	gitlab.super-id.net/bersama/opsce/onedash-be/api => ./api
	golang.org/x/crypto => golang.org/x/crypto v0.39.0
	golang.org/x/text => golang.org/x/text v0.21.0
	gopkg.in/yaml.v2 => gopkg.in/yaml.v2 v2.4.0
)

go 1.23.0

require (
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/aws/aws-sdk-go v1.55.5
	github.com/dustin/go-humanize v1.0.1
	github.com/go-co-op/gocron v1.37.0
	github.com/go-playground/validator/v10 v10.17.0
	github.com/golang-jwt/jwt/v5 v5.2.2
	github.com/google/uuid v1.4.0
	github.com/json-iterator/go v1.1.12
	github.com/myteksi/hystrix-go v1.1.3
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.18.1
	github.com/stretchr/testify v1.8.4
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api v0.0.0-**************-************
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common v0.0.0-**************-************
	gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api v0.0.0-**************-************
	gitlab.myteksi.net/dakota/common/aws v1.4.3
	gitlab.myteksi.net/dakota/common/context v0.17.0
	gitlab.myteksi.net/dakota/common/redis v0.19.0
	gitlab.myteksi.net/dakota/common/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing v1.10.0
	gitlab.myteksi.net/dakota/klient v1.21.1
	gitlab.myteksi.net/dakota/lending/loan-app/api v1.88.0
	gitlab.myteksi.net/dakota/lending/loan-core/api v1.158.0
	gitlab.myteksi.net/dakota/lending/loan-exp/api v1.125.0
	gitlab.myteksi.net/dakota/payment-ops-trf/api v1.19.0
	gitlab.myteksi.net/dakota/payment/pay-authz/api v0.0.0-**************-b7e43e7b435e
	gitlab.myteksi.net/dakota/schemas v1.16.140-0.**************-d24c68f65440
	gitlab.myteksi.net/dakota/servus/v2 v2.55.0
	gitlab.myteksi.net/dakota/transaction-limit/api v1.20.0
	gitlab.myteksi.net/gophers/go/commons/data v1.0.11
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.7 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.18 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.0.10
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.7
	gitlab.myteksi.net/snd/streamsdk v0.6.18
	gitlab.super-id.net/bersama/common/validations v1.0.2-migration
	gitlab.super-id.net/bersama/core-banking/account-service/api v1.70.0-migration
	gitlab.super-id.net/bersama/core-banking/product-master/api v1.34.0
	gitlab.super-id.net/bersama/deposit/transaction-history/api v0.0.0-**************-************
	gitlab.super-id.net/bersama/onboarding/customer-experience/api v1.45.1
	gitlab.super-id.net/bersama/onboarding/customer-master/api/v2 v2.31.27-migration-3
	gitlab.super-id.net/bersama/opsce/onedash-be/api v0.0.0-**************-************
	golang.org/x/crypto v0.39.0
)

require github.com/redis/go-redis/v9 v9.7.0

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.0.0-**************-6491aa3bf583 // indirect
	github.com/DataDog/datadog-go v4.8.3+incompatible // indirect
	github.com/DataDog/datadog-go/v5 v5.0.2 // indirect
	github.com/DataDog/sketches-go v1.2.1 // indirect
	github.com/Microsoft/go-winio v0.5.2 // indirect
	github.com/StackExchange/wmi v1.2.1 // indirect
	github.com/alecholmes/xfccparser v0.1.0 // indirect
	github.com/alecthomas/participle v0.4.1 // indirect
	github.com/cactus/go-statsd-client/v4 v4.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/flosch/pongo2 v0.0.0-20200913210552-0d938eb266f3 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/garyburd/redigo v1.6.3 // indirect
	github.com/go-ole/go-ole v1.2.5 // indirect
	github.com/go-openapi/errors v0.19.4 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-redis/redis v6.15.9+incompatible // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/gofrs/uuid v4.0.0+incompatible // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/glog v1.2.5 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20170512040955-6c130eed1e29 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.7 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/klauspost/compress v1.17.7 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/lightstep/lightstep-tracer-common/golang/gogo v0.0.0-20210210170715-a8dfcb80d3a7 // indirect
	github.com/lightstep/lightstep-tracer-go v0.25.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/schema v0.0.0-20150408103446-b22097c66c3d // indirect
	github.com/nxadm/tail v1.4.11 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.15 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/reterVision/go-kinesis v0.0.0-20150928061512-c0f0783318c3 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rs/cors v1.11.1 // indirect
	github.com/shirou/gopsutil/v3 v3.21.2 // indirect
	github.com/showa-93/go-mask v0.6.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.1.1 // indirect
	github.com/stretchr/objx v0.5.0 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/tklauser/go-sysconf v0.3.4 // indirect
	github.com/tklauser/numcpus v0.2.1 // indirect
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	gitlab.myteksi.net/dakota/common/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/common/servicename v1.9.1
	gitlab.myteksi.net/dakota/gaia v0.1.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/algo/cmap v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/deprecation v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/public/misc/systems v1.0.14 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/aws/grabkinesis v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/conf v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/encoding/grabjson v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/ldflags v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/internal/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.8 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/network/iputil v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredis v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredismigrate v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/server/swaggergen v1.0.4 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/time/grabtime v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tracer v1.0.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/validate v1.1.4 // indirect
	gitlab.myteksi.net/gophers/go/spartan/lechuck v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/gredis3 v1.0.8 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.13 // indirect
	gitlab.myteksi.net/snd/sarama v1.34.0 // indirect
	gitlab.myteksi.net/spartan/hystrix-go/v2 v2.0.1 // indirect
	gitlab.super-id.net/bersama/corex/customer-journal/api v0.0.0-**************-************
	gitlab.super-id.net/bersama/corex/customer-journey-experience/api v0.0.0-**************-************
	gitlab.super-id.net/bersama/fintrust/aml-service/api v0.0.0-**************-************
	gitlab.super-id.net/bersama/onboarding/application-service/api v1.36.1-migration-3.1 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.17.0 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/grpc v1.58.3 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/DataDog/dd-trace-go.v1 v1.36.2 // indirect
	gopkg.in/redsync.v1 v1.0.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/tylerb/graceful.v1 v1.2.15 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
