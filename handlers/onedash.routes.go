// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package handlers

import (
	context "context"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

// RegisterRoutes registers handlers with the Servus library.
func (o *OnedashService) RegisterRoutes(app *v2.Application) {
	app.POST(
		"/api/v1/ticket",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateTicket(ctx, req.(*api.CreateTicketRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateTicketRequest{}),
		v2.WithResponse(&api.CreateTicketResponse{}),
	)
	app.PUT(
		"/api/v1/ticket/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateTicket(ctx, req.(*api.UpdateTicketRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateTicketRequest{}),
		v2.WithResponse(&api.UpdateTicketResponse{}),
	)
	app.GET(
		"/api/v1/ticket",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketList(ctx, req.(*api.GetTicketListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketListRequest{}),
		v2.WithResponse(&api.GetTicketListResponse{}),
	)
	app.POST(
		"/api/v1/export/ticket",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketExport(ctx, req.(*api.GetTicketListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketListRequest{}),
		v2.WithResponse(&api.GetTicketExportResponse{}),
	)
	app.GET(
		"/api/v1/ticket/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketByID(ctx, req.(*api.GetTicketByIDRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketByIDRequest{}),
		v2.WithResponse(&api.GetTicketByIDResponse{}),
	)
	app.POST(
		"/api/v1/modules",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateModule(ctx, req.(*api.CreateModuleRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateModuleRequest{}),
		v2.WithResponse(&api.CreateModuleResponse{}),
	)
	app.PUT(
		"/api/v1/modules/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateModule(ctx, req.(*api.UpdateModuleRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateModuleRequest{}),
		v2.WithResponse(&api.UpdateModuleResponse{}),
	)
	app.GET(
		"/api/v1/modules",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetModules(ctx, req.(*api.GetModulesRequest))
			return res, err
		},
		v2.WithRequest(&api.GetModulesRequest{}),
		v2.WithResponse(&api.GetModulesResponse{}),
	)
	app.POST(
		"/api/v1/elements",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateElement(ctx, req.(*api.CreateElementRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateElementRequest{}),
		v2.WithResponse(&api.CreateElementResponse{}),
	)
	app.PUT(
		"/api/v1/elements/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateElement(ctx, req.(*api.UpdateElementRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateElementRequest{}),
		v2.WithResponse(&api.UpdateElementResponse{}),
	)
	app.GET(
		"/api/v1/elements",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetElements(ctx, req.(*api.GetElementsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetElementsRequest{}),
		v2.WithResponse(&api.GetElementsResponse{}),
	)
	app.GET(
		"/api/v1/elements/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetElementByID(ctx, req.(*api.GetElementByIDRequest))
			return res, err
		},
		v2.WithRequest(&api.GetElementByIDRequest{}),
		v2.WithResponse(&api.GetElementByIDResponse{}),
	)
	app.POST(
		"/api/v1/ticket/:id/comments",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateTicketComment(ctx, req.(*api.CreateTicketCommentRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateTicketCommentRequest{}),
		v2.WithResponse(&api.CreateTicketCommentResponse{}),
	)
	app.GET(
		"/api/v1/ticket/:id/comments",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketComments(ctx, req.(*api.GetTicketCommentsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketCommentsRequest{}),
		v2.WithResponse(&api.GetTicketCommentsResponse{}),
	)
	app.POST(
		"/api/v1/log-audit-trails",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.LogAuditTrails(ctx, req.(*api.LogAuditTrailRequest))
			return res, err
		},
		v2.WithRequest(&api.LogAuditTrailRequest{}),
		v2.WithResponse(&api.LogAuditTrailResponse{}),
		v2.WithDescription("LogAuditTrails: API to create Log Audit Trail"),
	)
	app.POST(
		"/api/v1/get-log-audit-trails",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetLogAuditTrails(ctx, req.(*api.GetLogAuditTrailsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetLogAuditTrailsRequest{}),
		v2.WithResponse(&api.GetLogAuditTrailsResponse{}),
	)
	app.POST(
		"/api/v1/unlink-account",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UnlinkAccount(ctx, req.(*api.UnlinkAccountRequest))
			return res, err
		},
		v2.WithRequest(&api.UnlinkAccountRequest{}),
		v2.WithResponse(&api.UnlinkAccountResponse{}),
		v2.WithDescription("UnlinkAccount: API to unlink account from partner"),
	)
	app.POST(
		"/api/v1/send-notification",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.SendNotification(ctx, req.(*api.SendNotificationRequest))
			return res, err
		},
		v2.WithRequest(&api.SendNotificationRequest{}),
		v2.WithResponse(&api.SendNotificationResponse{}),
		v2.WithDescription("SendNotification is API to send notification email, push or push inbox"),
	)
	app.POST(
		"/api/v1/transfer-on-behalf",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.TransferOnBehalf(ctx, req.(*api.PaymentTransferRequest))
			return res, err
		},
		v2.WithRequest(&api.PaymentTransferRequest{}),
		v2.WithResponse(&api.PaymentTransferResponse{}),
		v2.WithDescription("TransferOnBehalf: API to transfer on behalf"),
	)
	app.POST(
		"/api/v1/block-account",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.BlockAccount(ctx, req.(*api.BlockAccountRequest))
			return res, err
		},
		v2.WithRequest(&api.BlockAccountRequest{}),
		v2.WithResponse(&api.BlockAccountResponse{}),
		v2.WithDescription("BlockAccount is API to block account"),
	)
	app.POST(
		"/api/v1/unblock-account",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UnblockAccount(ctx, req.(*api.UnblockAccountRequest))
			return res, err
		},
		v2.WithRequest(&api.UnblockAccountRequest{}),
		v2.WithResponse(&api.UnblockAccountResponse{}),
		v2.WithDescription("UnblockAccount is API to unblock account"),
	)
	app.POST(
		"/api/v1/deactivate-loc",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.DeactivateLOC(ctx, req.(*api.DeactivateLOCRequest))
			return res, err
		},
		v2.WithRequest(&api.DeactivateLOCRequest{}),
		v2.WithResponse(&api.DeactivateLOCResponse{}),
		v2.WithDescription("DeactivateLOC: API to deactivate LOC account"),
	)
	app.POST(
		"/api/v1/update-casa-account/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateCASAAccountStatus(ctx, req.(*api.UpdateCASAAccountStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateCASAAccountStatusRequest{}),
		v2.WithResponse(&api.UpdateCASAAccountStatusResponse{}),
		v2.WithDescription("UpdateCASAAccountStatus: API to update the status of a casa account/bp asynchronously"),
	)
	app.POST(
		"/api/v1/documents",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateDocument(ctx, req.(*api.CreateDocumentRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateDocumentRequest{}),
		v2.WithResponse(&api.CreateDocumentResponse{}),
		v2.WithDescription("CreateDocument is API to create document"),
	)
	app.DELETE(
		"/api/v1/documents/:name",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.DeleteDocument(ctx, req.(*api.DeleteDocumentRequest))
			return res, err
		},
		v2.WithRequest(&api.DeleteDocumentRequest{}),
		v2.WithResponse(&api.DeleteDocumentResponse{}),
		v2.WithDescription("DeleteDocument is API to delete document"),
	)
	app.GET(
		"/api/v1/documents/:name",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetDocument(ctx, req.(*api.GetDocumentRequest))
			return res, err
		},
		v2.WithRequest(&api.GetDocumentRequest{}),
		v2.WithResponse(&api.GetDocumentResponse{}),
		v2.WithDescription("GetDocument is API to get document"),
	)
	app.GET(
		"/api/v1/ticket/:id/documents",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketDocuments(ctx, req.(*api.GetTicketDocumentsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketDocumentsRequest{}),
		v2.WithResponse(&api.GetTicketDocumentsResponse{}),
		v2.WithDescription("GetTicketDocuments is api to get ticket documents by ticket id"),
	)
	app.GET(
		"/api/v1/options/:type",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetOptions(ctx, req.(*api.GetOptionsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetOptionsRequest{}),
		v2.WithResponse(&api.GetOptionsResponse{}),
		v2.WithDescription("GetOptions is API for get dropdown option by type"),
	)
	app.GET(
		"/api/v1/elements/:id/priorities",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetElementPriorities(ctx, req.(*api.GetElementPrioritiesRequest))
			return res, err
		},
		v2.WithRequest(&api.GetElementPrioritiesRequest{}),
		v2.WithResponse(&api.GetElementPrioritiesResponse{}),
		v2.WithDescription("GetElementPriorities is API to get element priorities"),
	)
	app.GET(
		"/api/v1/statuses",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetStatuses(ctx)
			return res, err
		},
		v2.WithResponse(&api.GetStatusesResponse{}),
		v2.WithDescription("GetStatuses is API to get statuses"),
	)
	app.POST(
		"/api/v1/statuses",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateStatus(ctx, req.(*api.CreateStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateStatusRequest{}),
		v2.WithResponse(&api.CreateStatusResponse{}),
		v2.WithDescription("CreateStatus is API to create status"),
	)
	app.PUT(
		"/api/v1/statuses/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateStatus(ctx, req.(*api.UpdateStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateStatusRequest{}),
		v2.WithResponse(&api.UpdateStatusResponse{}),
		v2.WithDescription("UpdateStatus is API to update status"),
	)
	app.GET(
		"/api/v1/priorities",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetPriorities(ctx, req.(*api.GetPrioritiesRequest))
			return res, err
		},
		v2.WithRequest(&api.GetPrioritiesRequest{}),
		v2.WithResponse(&api.GetPrioritiesResponse{}),
		v2.WithDescription("GetPriorities is API to get priorities"),
	)
	app.POST(
		"/api/v1/priorities",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreatePriority(ctx, req.(*api.CreatePriorityRequest))
			return res, err
		},
		v2.WithRequest(&api.CreatePriorityRequest{}),
		v2.WithResponse(&api.CreatePriorityResponse{}),
		v2.WithDescription("CreatePriority is API to create priority"),
	)
	app.PUT(
		"/api/v1/priorities/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdatePriority(ctx, req.(*api.UpdatePriorityRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdatePriorityRequest{}),
		v2.WithResponse(&api.UpdatePriorityResponse{}),
		v2.WithDescription("UpdatePriority is API to update priority"),
	)
	app.GET(
		"/api/v1/ticket-chains",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketChains(ctx, req.(*api.GetTicketChainsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetTicketChainsRequest{}),
		v2.WithResponse(&api.GetTicketChainsResponse{}),
		v2.WithDescription("GetTicketChains is API to get ticket chains"),
	)
	app.PUT(
		"/api/v1/ticket/:id/assignee",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateTicketAssignee(ctx, req.(*api.UpdateTicketAssigneeRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateTicketAssigneeRequest{}),
		v2.WithResponse(&api.UpdateTicketAssigneeResponse{}),
		v2.WithDescription("UpdateTicketAssignee is API to update ticket assignee"),
	)
	app.POST(
		"/api/v1/feature-flag",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CreateFeatureFlag(ctx, req.(*api.CreateFeatureFlagRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateFeatureFlagRequest{}),
		v2.WithResponse(&api.CreateFeatureFlagResponse{}),
		v2.WithDescription("CreateFeatureFlag is API to create feature flag"),
	)
	app.PUT(
		"/api/v1/feature-flag",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateFeatureFlag(ctx, req.(*api.UpdateFeatureFlagRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateFeatureFlagRequest{}),
		v2.WithResponse(&api.UpdateFeatureFlagResponse{}),
		v2.WithDescription("UpdateFeatureFlag is API to update feature flag"),
	)
	app.GET(
		"/api/v1/feature-flag",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetFeatureFlag(ctx, req.(*api.GetFeatureFlagRequest))
			return res, err
		},
		v2.WithRequest(&api.GetFeatureFlagRequest{}),
		v2.WithResponse(&api.GetFeatureFlagResponse{}),
		v2.WithDescription("GetFeatureFlag is API to get requested feature flag"),
	)
	app.DELETE(
		"/api/v1/feature-flag",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.DeleteFeatureFlag(ctx, req.(*api.DeleteFeatureFlagRequest))
			return res, err
		},
		v2.WithRequest(&api.DeleteFeatureFlagRequest{}),
		v2.WithResponse(&api.DeleteFeatureFlagResponse{}),
		v2.WithDescription("DeleteFeatureFlag is API to soft delete feature flag"),
	)
	app.POST(
		"/api/v1/feature-flag/list",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetFeatureFlagList(ctx, req.(*api.GetFeatureFlagListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetFeatureFlagListRequest{}),
		v2.WithResponse(&api.GetFeatureFlagListResponse{}),
		v2.WithDescription("GetFeatureFlagList is API to get feature flag list"),
	)
	app.POST(
		"/api/v1/customer/search",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.CustomerSearch(ctx, req.(*api.CustomerSearchRequest))
			return res, err
		},
		v2.WithRequest(&api.CustomerSearchRequest{}),
		v2.WithResponse(&api.CustomerSearchResponse{}),
		v2.WithDescription("CustomerSearch: API to search customer details based on identifier"),
	)
	app.GET(
		"/api/v1/customers/search",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetCustomers(ctx, req.(*api.GetCustomersRequest))
			return res, err
		},
		v2.WithRequest(&api.GetCustomersRequest{}),
		v2.WithResponse(&api.GetCustomersResponse{}),
		v2.WithDescription("GetCustomers: API to search multiple customer based on identifier"),
	)
	app.POST(
		"/api/v1/data-segregation/roles",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetRolesDataSegregation(ctx, req.(*api.GetDataSegregationRoleListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetDataSegregationRoleListRequest{}),
		v2.WithResponse(&api.GetDataSegregationRoleListResponse{}),
		v2.WithDescription("GetRolesDataSegregation is get roles list API for data segregation purpose"),
	)
	app.POST(
		"/api/v1/data-segregation",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetDataSegregation(ctx, req.(*api.GetDataSegregationRequest))
			return res, err
		},
		v2.WithRequest(&api.GetDataSegregationRequest{}),
		v2.WithResponse(&api.GetDataSegregationResponse{}),
		v2.WithDescription("GetDataSegregation is API for get data segregation detail for specific role"),
	)
	app.PUT(
		"/api/v1/data-segregation",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.UpdateDataSegregation(ctx, req.(*api.UpdateDataSegregationRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateDataSegregationRequest{}),
		v2.WithResponse(&api.UpdateDataSegregationResponse{}),
		v2.WithDescription("UpdateDataSegregation is API for update data segregation"),
	)
	app.POST(
		"/api/v1/customers/search/data-point",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetCustomersDataPoint(ctx, req.(*api.CustomerSearchDataPointRequest))
			return res, err
		},
		v2.WithRequest(&api.CustomerSearchDataPointRequest{}),
		v2.WithResponse(&api.CustomerSearchDataPointResponse{}),
		v2.WithDescription("GetCustomersDataPoints is API to get specific data points"),
	)
	app.GET(
		"/api/v1/customer-segments",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetCustomerSegements(ctx)
			return res, err
		},
		v2.WithResponse(&api.GetCustomerSegmentsResponse{}),
		v2.WithDescription("GetCustomerSegements is API to get all customer segemnts"),
	)
	app.GET(
		"/api/v1/ticket-requestors",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetTicketRequestors(ctx)
			return res, err
		},
		v2.WithResponse(&api.GetTicketRequestorsResponse{}),
		v2.WithDescription("GetTicketRequestors is API to get all ticket requestors"),
	)
	app.POST(
		"/api/v1/customers/search/accounts",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := o.GetCustomerAccounts(ctx, req.(*api.GetCustomerAccountRequest))
			return res, err
		},
		v2.WithRequest(&api.GetCustomerAccountRequest{}),
		v2.WithResponse(&api.GetCustomerAccountsResponse{}),
		v2.WithDescription("GetCustomerAccounts is API to get specific customer and list of account"),
	)
}
