package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// CreateDocument is API to create document
func (o *OnedashService) CreateDocument(ctx context.Context, req *api.CreateDocumentRequest) (*api.CreateDocumentResponse, error) {
	res, err := logic.Process.CreateDocument(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "handler: failed to create document")
	}
	return res, nil
}
