package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// CreateElement creates an element
func (o *OnedashService) CreateElement(ctx context.Context, req *api.CreateElementRequest) (*api.CreateElementResponse, error) {
	res, err := logic.Process.CreateElement(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create element")
	}
	return res, nil
}
