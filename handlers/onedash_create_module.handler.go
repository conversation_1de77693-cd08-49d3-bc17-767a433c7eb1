//nolint:golint,dupl
package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// CreateModule creates a module
func (o *OnedashService) CreateModule(ctx context.Context, req *api.CreateModuleRequest) (*api.CreateModuleResponse, error) {
	res, err := logic.Process.CreateModule(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create module")
	}
	return res, nil
}
