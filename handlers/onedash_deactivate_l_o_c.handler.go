package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// DeactivateLOC is API to deactivate LOC account
func (o *OnedashService) DeactivateLOC(ctx context.Context, req *api.DeactivateLOCRequest) (*api.DeactivateLOCResponse, error) {
	err := validations.ValidateDeactivateLOCRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	message, errMarshal := json.Marshal(req)
	if errMarshal != nil {
		slog.FromContext(ctx).Warn(constants.DeactivateLOCLogTag,
			fmt.Sprintf("failed to marshal sqs message: %v", errMarshal.Error()), utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	if o.QueueOpsDeactivateLOC == nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag,
			"DeactivateLOCSQSClient is nil might be failed to connect to sqs", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	err = insertQueueDeactivateLOC(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag,
			fmt.Sprintf("Error writing db queue for deactivate loc req: %v", err.Error()), utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	err = o.QueueOpsDeactivateLOC.SendMessage(ctx, string(message), nil)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag,
			fmt.Sprintf("fail to send message for safeID %v to sqs: %v", req.SafeID, err.Error()), utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	slog.FromContext(ctx).Info(constants.DeactivateLOCLogTag,
		fmt.Sprintf("proceed deactivate LOC for safeID %v", req.SafeID), utils.GetTraceID(ctx))

	return &api.DeactivateLOCResponse{Status: "PROCESSING"}, nil
}

func insertQueueDeactivateLOC(ctx context.Context, req *api.DeactivateLOCRequest) error {
	queueDto := &storage.QueueDTO{
		IdentifierType: constants.AccountID,
		Identifier:     req.LocAccountID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1, // FIXME: Hardcoded
			Valid: true,
		},
		Status:    int32(0),
		EventName: constants.DeactivateLOC,
		TicketID:  req.TicketID,
		Metadata:  make(map[string]interface{}),
	}

	_, err := logic.Process.InsertQueueLog(ctx, queueDto)
	return err
}
