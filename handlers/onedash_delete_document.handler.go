package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// DeleteDocument is API to delete document
func (o *OnedashService) DeleteDocument(ctx context.Context, req *api.DeleteDocumentRequest) (*api.DeleteDocumentResponse, error) {
	res, err := logic.Process.DeleteDocument(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "handler: failed to delete document")
	}

	return res, nil
}
