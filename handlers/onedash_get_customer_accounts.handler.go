package handlers

import (
	context "context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetCustomerAccounts is API to get specific customer and list of account
func (o *OnedashService) GetCustomerAccounts(ctx context.Context, req *api.GetCustomerAccountRequest) (*api.GetCustomerAccountsResponse, error) {
	resp, err := logic.Process.GetCustomerAccounts(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchAccountLogTag, fmt.Sprintf("error proceed get customers %v", err), utils.GetTraceID(ctx))
		return &api.GetCustomerAccountsResponse{}, err
	}
	return resp, nil
}
