package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

func (o *OnedashService) GetCustomerSegements(ctx context.Context) (*api.GetCustomerSegmentsResponse, error) {
	res, err := logic.Process.GetCustomerSegmentList(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error processing get customer segement list")
	}
	return res, nil
}
