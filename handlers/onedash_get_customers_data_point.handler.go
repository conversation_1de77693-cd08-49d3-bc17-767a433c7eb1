package handlers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"

	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

// GetCustomersDataPoint is API to get specific data points
func (o *OnedashService) GetCustomersDataPoint(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	resp, err := logic.Process.CustomerSearchDataPoint(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchDataPointLogTag, fmt.Sprintf("error proceed get customer data point %v", err), utils.GetTraceID(ctx))
		return &api.CustomerSearchDataPointResponse{}, err
	}
	return resp, nil
}
