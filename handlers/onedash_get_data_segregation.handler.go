// Package handlers
//
// nolint: dupl
package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetDataSegregation is API for get data segregation detail for specific role
func (o *OnedashService) GetDataSegregation(ctx context.Context, req *api.GetDataSegregationRequest) (*api.GetDataSegregationResponse, error) {
	// check permission
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.DataSegregation, constants.BitwiseValueGeneralRead)
	if err != nil {
		return &api.GetDataSegregationResponse{}, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return &api.GetDataSegregationResponse{}, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	resp, err := logic.Process.GetDataSegregation(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetDataSegregationLogTag, fmt.Sprintf("error proceed get data segregation %v", err), utils.GetTraceID(ctx))
		return &api.GetDataSegregationResponse{}, err
	}

	return resp, nil
}
