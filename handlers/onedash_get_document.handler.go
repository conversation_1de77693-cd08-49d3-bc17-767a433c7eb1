package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetDocument is API to get document
func (o *OnedashService) GetDocument(ctx context.Context, req *api.GetDocumentRequest) (*api.GetDocumentResponse, error) {
	result, err := logic.Process.GetDocument(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get document")
	}

	return result, nil
}
