package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetElementByID ...
func (o *OnedashService) GetElementByID(ctx context.Context, req *api.GetElementByIDRequest) (*api.GetElementByIDResponse, error) {
	result, err := logic.Process.GetElementByID(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element by ID")
	}

	return result, nil
}
