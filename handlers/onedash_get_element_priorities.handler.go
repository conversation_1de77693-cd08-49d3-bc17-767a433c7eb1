package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetElementPriorities is API to get element priorities
func (o *OnedashService) GetElementPriorities(ctx context.Context, req *api.GetElementPrioritiesRequest) (*api.GetElementPrioritiesResponse, error) {
	// Get database handle
	result, err := logic.Process.GetElementPriorities(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element priorities")
	}
	return result, nil
}
