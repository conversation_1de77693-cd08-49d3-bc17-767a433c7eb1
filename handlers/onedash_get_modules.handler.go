package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetModules ...
func (o *OnedashService) GetModules(ctx context.Context, req *api.GetModulesRequest) (*api.GetModulesResponse, error) {
	res, err := logic.Process.GetModules(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get modules")
	}
	return res, nil
}
