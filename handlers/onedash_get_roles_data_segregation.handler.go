// Package handlers
//
// nolint: dupl
package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetRolesDataSegregation is get roles list API for data segregation purpose
func (o *OnedashService) GetRolesDataSegregation(ctx context.Context, req *api.GetDataSegregationRoleListRequest) (*api.GetDataSegregationRoleListResponse, error) {
	// check permission
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.DataSegregation, constants.BitwiseValueGeneralRead)
	if err != nil {
		return &api.GetDataSegregationRoleListResponse{}, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return &api.GetDataSegregationRoleListResponse{}, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	resp, err := logic.Process.GetDataSegregationRoles(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetDataSegregationRolesLogTag, fmt.Sprintf("error proceed get list role %v", err), utils.GetTraceID(ctx))
		return &api.GetDataSegregationRoleListResponse{}, err
	}

	return resp, nil
}
