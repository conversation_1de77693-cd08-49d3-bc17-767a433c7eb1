package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetStatuses is API to get statuses
func (o *OnedashService) GetStatuses(ctx context.Context) (*api.GetStatusesResponse, error) {
	// Get database handle
	result, err := logic.Process.GetStatuses(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get statuses")
	}

	return result, nil
}
