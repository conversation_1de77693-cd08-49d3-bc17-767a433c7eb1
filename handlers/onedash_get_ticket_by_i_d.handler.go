//nolint:golint,dupl,funlen
package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetTicketByID gets ticket by id
func (o *OnedashService) GetTicketByID(ctx context.Context, req *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error) {
	res, err := logic.Process.GetTicketByID(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "error processing get ticket by id")
	}
	return res, nil
}
