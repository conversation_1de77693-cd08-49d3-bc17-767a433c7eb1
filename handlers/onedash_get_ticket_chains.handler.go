package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetTicketChains is API to get ticket chains
func (o *OnedashService) GetTicketChains(ctx context.Context, req *api.GetTicketChainsRequest) (*api.GetTicketChainsResponse, error) {
	res, err := logic.Process.GetTicketChains(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chains")
	}

	return res, nil
}
