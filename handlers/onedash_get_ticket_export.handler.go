package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// ExportTicket Export a ticket
func (o *OnedashService) GetTicketExport(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketExportResponse, error) {
	res, err := logic.Process.ExportTicket(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error("handler.ExportTicket", fmt.Sprintf("error processing export ticket: %v", err.Error()))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to Export ticket")
	}

	return res, nil
}
