package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetTicketList gets ticket list
func (o *OnedashService) GetTicketList(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketListResponse, error) {
	res, err := logic.Process.GetTicketList(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "error processing get ticket list")
	}
	return res, nil
}
