package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

func (o *OnedashService) GetTicketRequestors(ctx context.Context) (*api.GetTicketRequestorsResponse, error) {
	res, err := logic.Process.GetTicketRequestorList(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error processing get ticket requestors list")
	}
	return res, nil
}
