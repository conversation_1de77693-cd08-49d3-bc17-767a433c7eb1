//nolint:golint,dupl
package handlers

import (
	"context"
	"encoding/json"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// UnblockAccount is API to unblock account
func (o *OnedashService) UnblockAccount(ctx context.Context, req *api.UnblockAccountRequest) (*api.UnblockAccountResponse, error) {
	if req == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	message, errMarshal := json.Marshal(req)
	if errMarshal != nil {
		slog.FromContext(ctx).Warn(constants.UnblockAccountLogTag,
			fmt.Sprintf("failed to marshal sqs message: %v", errMarshal.Error()), utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	if o.QueueOpsUnblockAccount == nil {
		slog.FromContext(ctx).Error(constants.UnblockAccountLogTag,
			"UnblockAccountSqsClient is nil might be failed to connect to sqs", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	err = o.QueueOpsUnblockAccount.SendMessage(ctx, string(message), nil)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UnblockAccountLogTag,
			fmt.Sprintf("fail to send message to sqs: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	slog.FromContext(ctx).Info(constants.UnblockAccountLogTag,
		fmt.Sprintf("proceed unblock account for %v", req.AccountID), utils.GetTraceID(ctx))

	return &api.UnblockAccountResponse{Status: "SUCCESS"}, nil
}
