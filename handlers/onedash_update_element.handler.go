package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdateElement updates an element
func (o *OnedashService) UpdateElement(ctx context.Context, req *api.UpdateElementRequest) (*api.UpdateElementResponse, error) {
	res, err := logic.Process.UpdateElement(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update element")
	}
	return res, nil
}
