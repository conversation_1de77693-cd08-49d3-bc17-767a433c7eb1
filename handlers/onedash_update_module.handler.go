//nolint:golint,dupl,funlen
package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdateModule updates a module
func (o *OnedashService) UpdateModule(ctx context.Context, req *api.UpdateModuleRequest) (*api.UpdateModuleResponse, error) {
	res, err := logic.Process.UpdateModule(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update module")
	}
	return res, nil
}
