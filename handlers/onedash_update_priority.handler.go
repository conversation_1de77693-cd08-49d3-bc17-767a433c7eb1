package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdatePriority is API to update priority
func (o *OnedashService) UpdatePriority(ctx context.Context, req *api.UpdatePriorityRequest) (*api.UpdatePriorityResponse, error) {
	res, err := logic.Process.UpdatePriority(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update priority")
	}
	return res, nil
}
