package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdateStatus is API to update status
func (o *OnedashService) UpdateStatus(ctx context.Context, req *api.UpdateStatusRequest) (*api.UpdateStatusResponse, error) {
	res, err := logic.Process.UpdateStatus(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update status")
	}
	return res, nil
}
