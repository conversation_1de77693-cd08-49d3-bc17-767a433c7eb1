//nolint:golint,dupl,funlen
package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdateTicket updates a ticket
func (o *OnedashService) UpdateTicket(ctx context.Context, req *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error) {
	// if action is update ticket assignee
	if req.Action == constants.ActionAssignSelf || req.Action == constants.ActionDeassignSelf {
		res, err := logic.Process.UpdateTicketAssignee(ctx, &api.UpdateTicketAssigneeRequest{
			Id:           req.Id,
			TargetUserID: req.TargetUserID,
		})
		if err != nil {
			slog.FromContext(ctx).Error("handler.UpdateTicket", fmt.Sprintf("error processing update ticket assignee: %v", err.Error()))
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket assignee")
		}
		return &api.UpdateTicketResponse{Id: res.Id}, nil
	} else if req.IsUpdateData {
		res, err := logic.Process.UpdateTicket(ctx, req)
		if err != nil {
			slog.FromContext(ctx).Error("handler.UpdateTicket", fmt.Sprintf("error processing update ticket: %v", err.Error()))
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket")
		}
		return res, nil
	}

	res, err := logic.Process.UpdateTicketStatus(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error("handler.UpdateTicket", fmt.Sprintf("error processing update ticket status: %v", err.Error()))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket status")
	}
	return res, nil
}
