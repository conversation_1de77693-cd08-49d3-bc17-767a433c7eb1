package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// UpdateTicketAssignee updates a ticket assignee
func (o *OnedashService) UpdateTicketAssignee(ctx context.Context, req *api.UpdateTicketAssigneeRequest) (*api.UpdateTicketAssigneeResponse, error) {
	// update non-capture include: approve/reject ticket, assign ticket
	res, err := logic.Process.UpdateTicketAssignee(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error("handler.UpdateTicket", fmt.Sprintf("error processing update ticket non capture: %v", err.Error()))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket non capture")
	}

	return res, nil
}
