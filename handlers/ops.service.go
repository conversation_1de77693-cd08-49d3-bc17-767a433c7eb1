package handlers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/ops_lending_action_event"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// OpsService serves as the context for handlers.
type OpsService struct {
	AppConfig                   *config.AppConfig  `inject:"config"`
	RedisClient                 redis.Client       `inject:"client.redisClient"`
	Statsd                      statsd.Client      `inject:"statsD"`
	OpsLendingActionKafkaWriter kafkawriter.Client `inject:"kafkawriter.OpsLendingAction"`
	AppianClient                appian.Appian      `inject:"client.appian"`
	S3Client                    s3client.S3        `inject:"client.s3"`
}

// RegisterOpsLendingActionKafkaWriter ...
func RegisterOpsLendingActionKafkaWriter(appCfg *config.AppConfig, app *servus.Application) {
	kafkaWriter, err := streams.NewStaticWriter(context.Background(), "lendingActionKafkaConfig",
		sndconfig.KafkaConfig{
			Brokers:     appCfg.LendingActionKafkaConfig.Brokers,
			ClientID:    appCfg.LendingActionKafkaConfig.ClientID,
			ClusterType: appCfg.LendingActionKafkaConfig.ClusterType,
			EnableTLS:   appCfg.LendingActionKafkaConfig.EnableTLS,
			PackageName: appCfg.LendingActionKafkaConfig.PackageName,
			DtoName:     appCfg.LendingActionKafkaConfig.DtoName,
			OffsetType:  appCfg.LendingActionKafkaConfig.InitOffset,
			Stream:      appCfg.LendingActionKafkaConfig.TopicName,
		},
		&ops_lending_action_event.OpsLendingActionEvent{},
	)
	if err != nil {
		panic(fmt.Sprintf("fail to create kafka writer, err=%s", err))
	}

	app.MustRegister("kafkawriter.OpsLendingAction", kafkaWriter)
}
