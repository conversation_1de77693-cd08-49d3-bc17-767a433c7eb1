package handlers

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/ops_lending_action_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// ExecuteFile is API to process file.
func (o *OpsService) ExecuteFile(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	switch req.Type {
	case fileprocessor.WriteOffFileProcessorUsecase:
		return o.executeWriteOffFile(ctx, req)
	}

	return nil, errorwrapper.Error(apiError.BadRequest, "Invalid usecase type")
}

func (o *OpsService) executeWriteOffFile(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	interactor := fileprocessor.NewBulkWriteOffFileProcessor().
		WithCache(o.RedisClient).
		WithTimeoutInSec(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.Timeout).
		WithRecordTTL(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.RecordTTL)

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, o.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get database handle")
	}

	// get list of staged files
	listRows := interactor.GetStagedList(ctx, req.FileName)
	if len(listRows) == 0 {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, "No records found")
	}

	// check if execution is locked
	err = interactor.BeginExecution(ctx, req.FileName)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get lock status")
	}

	for _, cif := range listRows {
		referenceID := uuid.New().String() // generate a new UUID for the reference ID
		err = o.OpsLendingActionKafkaWriter.Save(&ops_lending_action_event.OpsLendingActionEvent{
			Identifier:  constants.CifNumberIdentifierType,
			Value:       cif,
			EventType:   constants.InsuranceClaim,
			ReferenceID: referenceID,
		})
		if err != nil {
			slog.FromContext(ctx).Error(fileprocessor.TagBulkFileProcessor, "failed to save to kafka:"+err.Error())
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to save to kafka")
		}
		slog.FromContext(ctx).Info(fileprocessor.TagBulkFileProcessor, fmt.Sprintf("Published to Kafka: %s, ReferenceID: %s", cif, referenceID))

		// create a dump row in the database
		_, err := storage.CreateBulkFileProcessorDumpRow(ctx, db, &storage.BulkFileProccessorDumpDTO{
			Identifier:     cif,
			IdentifierType: constants.CifNumberIdentifierType,
			Type:           fileprocessor.WriteOffFileProcessorUsecase,
			Status:         fileprocessor.StatusRequestCreated,
			Data:           nil, // No data to store in this case
			ReferenceID:    sql.NullString{String: referenceID, Valid: true},
			FileName:       sql.NullString{String: req.FileName, Valid: true},
		})
		if err != nil {
			slog.FromContext(ctx).Error(fileprocessor.TagBulkFileProcessor, "failed to create bulk file processor dump row: "+err.Error())
		}
	}

	return &api.ExecuteFileResponse{
		Status:  fileprocessor.StatusPending,
		Message: "File is being processed",
	}, nil
}
