package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/ops_lending_action_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

func wrapErrV2(action string, err error) error {
	return errorwrapper.WrapError(err, apiError.Idem, fmt.Sprintf("failed to %s", action))
}

// ExecuteFile is API to process file.
func (o *OpsService) ExecuteFileV2(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	var interactor *fileprocessor.WriteOffFileProcessor
	switch req.Type {
	case fileprocessor.WriteOffFileProcessorUsecaseV2:
		interactor = fileprocessor.NewBulkWriteOffFileProcessorV2().
			WithCache(o.RedisClient).
			WithTimeoutInSec(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.Timeout).
			WithRecordTTL(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.RecordTTL)
	case fileprocessor.WaiveOffFileProcessorUsecaseV2:
		interactor = fileprocessor.NewBulkWaiveOffFileProcessorV2().
			WithCache(o.RedisClient).
			WithTimeoutInSec(o.AppConfig.FileProcessorConfig.WaiveOffFileProcessorConfig.Timeout).
			WithRecordTTL(o.AppConfig.FileProcessorConfig.WaiveOffFileProcessorConfig.RecordTTL)
	}

	return o.executeWriteOffFileV2(ctx, req, interactor)
}

func (o *OpsService) executeWriteOffFileV2(ctx context.Context, req *api.ExecuteFileRequest, interactor *fileprocessor.WriteOffFileProcessor) (*api.ExecuteFileResponse, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, o.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, wrapErrV2("get database handle", err)
	}

	s3Path := fmt.Sprintf("onedash-documents/%s", req.FileName)
	obj, err := o.S3Client.GetObjectWithContext(ctx, o.AppConfig.S3Config.BucketName, s3Path)
	if err != nil {
		return nil, wrapErrV2("get CSV from S3", err)
	}

	rows, err := interactor.LoadFromCSV(ctx, obj)
	if err != nil {
		return nil, wrapErrV2("load CSV", err)
	}

	cacheRows := make(map[string]interface{}, len(rows))
	successRows := make([]string, 0, len(rows))

	for _, row := range rows {
		meta := fileprocessor.WriteOffDTO{
			CIFNumber:     row.CIFNumber,
			OverallStatus: fileprocessor.StatusPending,
		}
		metaBytes, _ := json.Marshal(meta)
		cacheRows[row.CIFNumber] = metaBytes
		successRows = append(successRows, row.CIFNumber)
	}

	if err := interactor.Stage(ctx, req.FileName, cacheRows, successRows, len(successRows)); err != nil {
		return nil, wrapErrV2("stage validation", err)
	}

	if err := interactor.BeginExecution(ctx, req.FileName); err != nil {
		return nil, wrapErrV2("acquire execution lock", err)
	}

	for _, row := range rows {
		if err := o.publishAndStoreEventV2(ctx, db, req.FileName, row.CIFNumber); err != nil {
			return nil, err
		}
	}

	return &api.ExecuteFileResponse{
		Status:  fileprocessor.StatusPending,
		Message: "File is being processed",
	}, nil
}

func (o *OpsService) publishAndStoreEventV2(ctx context.Context, db *sql.DB, fileName, cif string) error {
	refID := uuid.New().String()

	// Publish to Kafka
	err := o.OpsLendingActionKafkaWriter.Save(&ops_lending_action_event.OpsLendingActionEvent{
		Identifier:  constants.CifNumberIdentifierType,
		Value:       cif,
		EventType:   constants.InsuranceClaim,
		ReferenceID: refID,
	})
	if err != nil {
		slog.FromContext(ctx).Error(fileprocessor.TagBulkFileProcessor, "Kafka publish failed: "+err.Error())
		return wrapErrV2("publish to Kafka", err)
	}
	slog.FromContext(ctx).Info(fileprocessor.TagBulkFileProcessor, fmt.Sprintf("Published to Kafka: %s, RefID: %s", cif, refID))

	// Create dump row
	_, err = storage.CreateBulkFileProcessorDumpRow(ctx, db, &storage.BulkFileProccessorDumpDTO{
		Identifier:     cif,
		IdentifierType: constants.CifNumberIdentifierType,
		Type:           fileprocessor.WriteOffFileProcessorUsecase,
		Status:         fileprocessor.StatusRequestCreated,
		Data:           nil,
		ReferenceID:    sql.NullString{String: refID, Valid: true},
		FileName:       sql.NullString{String: fileName, Valid: true},
	})
	if err != nil {
		slog.FromContext(ctx).Error(fileprocessor.TagBulkFileProcessor, "Create dump row failed: "+err.Error())
	}

	return nil
}
