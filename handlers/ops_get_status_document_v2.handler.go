package handlers

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// Get Status Validation Document
func (o *OpsService) GetStatusDocumentV2(ctx context.Context, req *api.GetStatusRequest) (*api.GetStatusResponse, error) {
	resp := &api.GetStatusResponse{}
	respResult := &api.GetStatusResponse_Result{}

	// init db
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, o.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("getStatusDocument", "failed to get database handle:"+err.Error())
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// get document by name
	document, err := storage.GetDocumentByName(ctx, db, req.FileName)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get document")
	}

	if !document.ValidationStatus.Valid {
		slog.FromContext(ctx).Warn("getStatusDocument", "invalid validation status:"+document.ValidationStatus.String)
		return nil, errorwrapper.WrapError(apiError.ResourceNotFoundError, apiError.ResourceNotFound, "invalid validation status")
	}

	resp.FileName = document.Name
	resp.Count = document.Count.Int64
	resp.Status = document.ValidationStatus.String

	if document.ValidationStatus.String == "SUCCESS" {
		// get success file
		ext := filepath.Ext(document.Name)
		name := strings.TrimSuffix(document.Name, ext)

		successFile, err := storage.GetDocumentByName(ctx, db, fmt.Sprintf("%s-validation-success.csv", name))
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get document")
		}

		respResult.SuccessFile = &api.FilePayload{
			Id:       successFile.ID,
			FileName: successFile.Name,
			Count:    successFile.Count.Int64,
		}

		// get failed file
		failedFile, err := storage.GetDocumentByName(ctx, db, fmt.Sprintf("%s-validation-failed.csv", name))
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get document")
		}

		respResult.FailedFile = &api.FilePayload{
			Id:       failedFile.ID,
			FileName: failedFile.Name,
			Count:    failedFile.Count.Int64,
		}

		resp.Result = respResult
	}

	return resp, nil
}
