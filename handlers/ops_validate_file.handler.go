package handlers

import (
	"context"
	"encoding/json"
	"fmt"

	"encoding/base64"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	generatevalidationresultfile "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background/generate_validation_result_file"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// ValidateFile is API to validate file.
func (o *OpsService) ValidateFile(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	switch req.Type {
	case fileprocessor.WriteOffFileProcessorUsecase:
		return o.validateWriteOffFile(ctx, req)
	}

	return nil, errorwrapper.Error(apiError.BadRequest, "Invalid usecase type")
}

// validateWriteOffFile is the handler for validating write off file.
//
// nolint: funlen
func (o *OpsService) validateWriteOffFile(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	interactor := fileprocessor.NewBulkWriteOffFileProcessor().
		WithCache(o.RedisClient).
		WithTimeoutInSec(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.Timeout).
		WithRecordTTL(o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.RecordTTL)
	success := fileprocessor.NewBulkWriteOffFileProcessor() // file generator only
	failed := fileprocessor.NewBulkWriteOffFileProcessor()  // file generator only

	if interactor.IsLocked(ctx, req.FileName) {
		return &api.ValidateFileResponse{
			FileName: req.FileName,
			Status:   fileprocessor.StatusInvalid,
			Message:  "File processor is being locked",
		}, errorwrapper.Error(apiError.BadRequest, "File processor is being locked, there is another process running")
	}

	decodedData, err := base64.StdEncoding.DecodeString(req.Payload)
	if err != nil {
		return &api.ValidateFileResponse{
			FileName: req.FileName,
			Status:   fileprocessor.StatusInvalid,
		}, errorwrapper.Error(apiError.BadRequest, "Failed to extract CSV")
	}

	// read the file
	records, err := interactor.LoadFromCSV(ctx, decodedData)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to load from csv")
	}
	if len(records) > o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.MaxRecords {
		return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("Bulk write off is limited to %d records", o.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.MaxRecords))
	}

	cacheRows := map[string]interface{}{}
	listRows := []string{}
	successCount := 0
	failedCount := 0

	for _, r := range records {
		if _, ok := cacheRows[r.CIFNumber]; ok {
			slog.FromContext(ctx).Warn("tagBulkWriteOff", fmt.Sprintf("Duplicate record found for CIF: %s", r.CIFNumber))
			continue
		}

		meta := fileprocessor.WriteOffDTO{
			CIFNumber:     r.CIFNumber,
			OverallStatus: fileprocessor.StatusPending, // initial status
		}

		// first gate of validation
		_, err = validations.IsValid(r)
		if err != nil {
			meta.OverallStatus = fileprocessor.StatusFailed
			meta.FailureReason = "Invalid CIF Format"

			failed.Records = append(failed.Records, meta)
			failedCount++
		} else {
			b, _ := json.Marshal(meta)
			cacheRows[r.CIFNumber] = b
			listRows = append(listRows, r.CIFNumber)
			success.Records = append(success.Records, meta)
			successCount++ // don't count duplicates
		}
	}

	// stage all the passing validation records
	err = interactor.Stage(ctx, req.FileName, cacheRows, listRows, successCount)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to stage validation")
	}

	successFile, errSuccess := success.GenerateValidationResultFile(ctx, req.FileName, "success")
	if errSuccess != nil {
		slog.FromContext(ctx).Error("tagBulkWriteOff", "failed to generate success file, fileName: "+req.FileName)
		return nil, errorwrapper.WrapError(errSuccess, apiError.Idem, "failed to generate success file")
	}
	failedFile, errFailed := failed.GenerateValidationResultFile(ctx, req.FileName, "failed")
	if errFailed != nil {
		slog.FromContext(ctx).Error("tagBulkWriteOff", "failed to generate failed file, fileName: "+req.FileName)
		return nil, errorwrapper.WrapError(errFailed, apiError.Idem, "failed to generate failed file")
	}

	// make background call to compile the validation result and send to appian
	background.RunInBackground(
		fileprocessor.TagBulkFileProcessor,
		generatevalidationresultfile.New(req.FileName, successFile, failedFile, o.AppConfig, o.AppianClient),
	)

	return &api.ValidateFileResponse{
		FileName: req.FileName,
		Status:   fileprocessor.StatusPending,
	}, nil
}
