package handlers

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	generatevalidationresultfileV2 "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background/generate_validation_result_fileV2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// ValidateFile is API to validate file.
func (o *OpsService) ValidateFileV2(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	return o.validateFileV2(ctx, req)
}

// validateFile is the handler for validating write off file.
//
// nolint: funlen
func (o *OpsService) validateFileV2(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, o.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("validateFileV2", "failed to get database handle:"+err.Error())
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get database handle")
	}

	document, err := storage.GetDocumentByName(ctx, db, req.FileName)
	if err != nil {
		slog.FromContext(ctx).Error("validateFileV2", "failed to get document:"+err.Error())
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get document")
	}

	switch req.Type {
	case "WRITE_OFF":
		req.Type = fileprocessor.WriteOffFileProcessorUsecaseV2
	case "WAIVE_OFF":
		req.Type = fileprocessor.WaiveOffFileProcessorUsecaseV2
	default:
		return nil, errorwrapper.WrapError(api.DefaultInternalServerError, apiError.Idem, "invalid type")
	}

	// update document data
	document.ValidationStatus = sql.NullString{String: "IN_PROGRESS", Valid: true}
	err = storage.UpdateDocumentsForValidation(ctx, db, document)
	if err != nil {
		slog.FromContext(ctx).Error("validateFileV2", "failed to update parent document:"+err.Error())
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to process document")
	}

	background.RunInBackground(
		fileprocessor.TagBulkFileProcessor,
		generatevalidationresultfileV2.New(document, o.AppConfig, o.S3Client, o.RedisClient, req.Type),
	)

	return &api.ValidateFileResponse{
		FileName: req.FileName,
		Status:   "IN_PROGRESS",
	}, nil
}
