// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: audittrail.proto
package api

import (
	context "context"
)

type SortOrder string

const (
	SortOrder_ASC  SortOrder = "ASC"
	SortOrder_DESC SortOrder = "DESC"
)

type AuditTrailActivityType string

const (
	AuditTrailActivityType_CUSTOMER_SEARCH  AuditTrailActivityType = "CUSTOMER_SEARCH"
	AuditTrailActivityType_DATA_SEGREGATION AuditTrailActivityType = "DATA_SEGREGATION"
	AuditTrailActivityType_MODULE_CONFIG    AuditTrailActivityType = "MODULE_CONFIG"
	AuditTrailActivityType_FEATURE_FLAG     AuditTrailActivityType = "FEATURE_FLAG"
)

type Sort struct {
	Column string    `json:"column,omitempty"`
	Sort   SortOrder `json:"sort,omitempty"`
}

type GetAuditTrailsRequest struct {
	Identifier     *string `json:"identifier,omitempty"`
	IdentifierType *string `json:"identifierType,omitempty"`
	Limit          *int64  `json:"limit,omitempty"`
	StartingBefore *string `json:"startingBefore,omitempty"`
	EndingAfter    *string `json:"endingAfter,omitempty"`
	ActivityType   *string `json:"activityType,omitempty"`
	SortBy         *Sort   `json:"sortBy,omitempty" validate:"omitempty,audit_trails_sort"`
	WithPagination *bool   `json:"withPagination,omitempty"`
}

type GetAuditTrailsResponse struct {
	AuditTrails []AuditTrails     `json:"auditTrails,omitempty"`
	Links       map[string]string `json:"links,omitempty"`
}

type AuditTrails struct {
	Id             int64       `json:"id,omitempty"`
	CreatedAt      string      `json:"createdAt,omitempty"`
	CreatedBy      int64       `json:"createdBy,omitempty"`
	Identifier     string      `json:"identifier,omitempty"`
	IdentifierType string      `json:"identifierType,omitempty"`
	Title          string      `json:"title,omitempty"`
	Description    string      `json:"description,omitempty"`
	ActivityType   string      `json:"activityType,omitempty"`
	ReferenceID    string      `json:"referenceID,omitempty"`
	ExtraParams    interface{} `json:"extraParams,omitempty"`
	UserName       string      `json:"userName,omitempty"`
}

type AuditTrailRequest struct {
	Identifier     string                 `json:"identifier,omitempty" validate:"required"`
	IdentifierType string                 `json:"identifierType,omitempty" validate:"required"`
	Title          *string                `json:"title,omitempty"`
	Description    *string                `json:"description,omitempty"`
	ActivityType   string                 `json:"activityType,omitempty" validate:"required"`
	ReferenceID    *string                `json:"referenceID,omitempty"`
	ExtraParams    map[string]interface{} `json:"extraParams,omitempty"`
	CreatedBy      int64                  `json:"createdBy,omitempty" validate:"required"`
}

type AuditTrailResponse struct {
	Id int64 `json:"id,omitempty"`
}

type AuditTrail interface {
	// GetAuditTrails is API to get audit trails
	GetAuditTrails(ctx context.Context, req *GetAuditTrailsRequest) (*GetAuditTrailsResponse, error)
	// CreateAuditTrails is API to create Audit Trail
	CreateAuditTrails(ctx context.Context, req *AuditTrailRequest) (*AuditTrailResponse, error)
}
