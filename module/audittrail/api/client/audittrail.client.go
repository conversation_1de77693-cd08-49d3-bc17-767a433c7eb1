// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: audittrail.proto
package client

import (
	bytes "bytes"
	context "context"
	_go "github.com/json-iterator/go"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api"
	http "net/http"
)

// AuditTrailClient makes calls to AuditTrail service.
type AuditTrailClient struct {
	machinery klient.RoundTripper
}

// MakeAuditTrailClient instantiates a new AuditTrailClient.
// Deprecated: Use NewAuditTrailClient instead
func MakeAuditTrailClient(initializer klient.Initializer) (*AuditTrailClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &AuditTrailClient{
		machinery: roundTripper,
	}, nil
}

// NewAuditTrailClient instantiates a new AuditTrailClient.
func NewAuditTrailClient(baseURL string, options ...klient.Option) (*AuditTrailClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &AuditTrailClient{
		machinery: roundTripper,
	}, nil
}

// [Deprecated] GetAuditTrails is API to get audit trails
func (a *AuditTrailClient) DeprecatedGetAuditTrails(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error) {
	reqShell := (*GetAuditTrailsRequestShell)(req)
	resShell := &GetAuditTrailsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deprecatedGetAuditTrailsDescriptor)
	err := a.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetAuditTrailsResponse)(resShell), err
}

// GetAuditTrailsWithGet is API to get audit trails using GET method
func (a *AuditTrailClient) GetAuditTrail(ctx context.Context, req *api.GetAuditTrailsRequest) (*api.GetAuditTrailsResponse, error) {
	reqShell := (*GetAuditTrailsRequestShell)(req)
	resShell := &GetAuditTrailsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getAuditTrailsDescriptor)
	err := a.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetAuditTrailsResponse)(resShell), err
}

// CreateAuditTrails is API to create Audit Trail
func (a *AuditTrailClient) CreateAuditTrails(ctx context.Context, req *api.AuditTrailRequest) (*api.AuditTrailResponse, error) {
	reqShell := (*CreateAuditTrailsRequestShell)(req)
	resShell := &CreateAuditTrailsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createAuditTrailsDescriptor)
	err := a.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AuditTrailResponse)(resShell), err
}

// GetAuditTrailsRequestShell is a wrapper to make the object a klient.Request
type GetAuditTrailsRequestShell api.GetAuditTrailsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAuditTrailsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/get-audit-trails"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetAuditTrailsRequestShell) DeprecatedEncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/get-audit-trails"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetAuditTrailsResponseShell is a wrapper to make the object a klient.Request
type GetAuditTrailsResponseShell api.GetAuditTrailsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetAuditTrailsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateAuditTrailsRequestShell is a wrapper to make the object a klient.Request
type CreateAuditTrailsRequestShell api.AuditTrailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateAuditTrailsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/audit-trails"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateAuditTrailsResponseShell is a wrapper to make the object a klient.Request
type CreateAuditTrailsResponseShell api.AuditTrailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateAuditTrailsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}


var deprecatedGetAuditTrailsDescriptor = klient.EndpointDescriptor{
	Name:        "[Deprecated] GetAuditTrails",
	Description: "[Deprecated] GetAuditTrails is API to get audit trails",
	Method:      "POST",
	Path:        "/api/v1/get-audit-trails",
}

var createAuditTrailsDescriptor = klient.EndpointDescriptor{
	Name:        "CreateAuditTrails",
	Description: "CreateAuditTrails is API to create Audit Trail",
	Method:      "POST",
	Path:        "/api/v1/audit-trails",
}

var getAuditTrailsDescriptor = klient.EndpointDescriptor{
	Name:        "GetAuditTrailsWithGet",
	Description: "GetAuditTrails is API to get audit trails",
	Method:      "GET",
	Path:        "/api/v1/get-audit-trails",
}
