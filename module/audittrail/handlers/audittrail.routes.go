// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: audittrail.proto
package handlers

import (
	context "context"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api"
)

// RegisterRoutes registers handlers with the Servus library.
func (a *AuditTrailService) RegisterRoutes(app *v2.Application) {
	// GET version of the same endpoint for backward compatibility
	app.GET(
		"/api/v1/get-audit-trails",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := a.GetAuditTrails(ctx, req.(*api.GetAuditTrailsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetAuditTrailsRequest{}),
		v2.WithResponse(&api.GetAuditTrailsResponse{}),
		v2.WithDescription("GetAuditTrails is API to get audit trails"),
	)
	app.POST(
		"/api/v1/audit-trails",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := a.CreateAuditTrails(ctx, req.(*api.AuditTrailRequest))
			return res, err
		},
		v2.WithRequest(&api.AuditTrailRequest{}),
		v2.WithResponse(&api.AuditTrailResponse{}),
		v2.WithDescription("CreateAuditTrails is API to create Audit Trail"),
	)

	app.POST(
		"/api/v1/get-audit-trails",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := a.GetAuditTrails(ctx, req.(*api.GetAuditTrailsRequest))
			return res, err
		},
		v2.WithRequest(&api.GetAuditTrailsRequest{}),
		v2.WithResponse(&api.GetAuditTrailsResponse{}),
		v2.WithDescription("[Deprecated] GetAuditTrails is API to get audit trails"),
	)
}
