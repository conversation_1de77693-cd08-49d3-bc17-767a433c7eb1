package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// CreateAuditTrails is API to create Audit Trail
func (a *AuditTrailService) CreateAuditTrails(ctx context.Context, req *api.AuditTrailRequest) (*api.AuditTrailResponse, error) {
	resp, err := auditTrailLogic.AuditTrailProcess.CreateAuditTrail(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreateAuditTrailTag, fmt.Sprintf("error create audit_trail %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.AuditTrailResponse{}, err
	}
	return resp, nil
}
