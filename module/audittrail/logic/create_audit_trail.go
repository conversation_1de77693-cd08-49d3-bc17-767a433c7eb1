package logic

import (
	"context"
	"database/sql"
	"log"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
)

// CreateAuditTrail creates an audit_trail
func (p *AuditTrailImpl) CreateAuditTrail(ctx context.Context, req *api.AuditTrailRequest) (*api.AuditTrailResponse, error) {
	// validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		log.Println(err)
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		log.Println(err)
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Create audit trail
	auditTrailDTO := &storage.AuditTrailDTO{
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy:      req.CreatedBy,
		Identifier:     req.Identifier,
		IdentifierType: req.IdentifierType,
		ActivityType:   req.ActivityType,
	}
	if req.Title != nil {
		auditTrailDTO.Title = sql.NullString{
			String: *req.Title,
			Valid:  true,
		}
	}
	if req.Description != nil {
		auditTrailDTO.Description = sql.NullString{
			String: *req.Description,
			Valid:  true,
		}
	}
	if req.ReferenceID != nil {
		auditTrailDTO.ReferenceID = sql.NullString{
			String: *req.ReferenceID,
			Valid:  true,
		}
	}
	if req.ExtraParams != nil {
		auditTrailDTO.ExtraParams = (*storage.DataJSON)(&req.ExtraParams)
	}

	auditTrailID, err := storage.CreateAuditTrail(ctx, db, auditTrailDTO)
	if err != nil {
		log.Println(err)
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create audit_trail")
	}

	return &api.AuditTrailResponse{
		Id: auditTrailID,
	}, nil
}
