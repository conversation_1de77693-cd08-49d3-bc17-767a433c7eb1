package logic

import (
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

type AuditTrailImpl struct {
	AppConfig *config.AppConfig `inject:"config"`
}

var (
	// Process is the interactor for all logic
	AuditTrailProcess IAuditTrail
)

// InitLogic ...
func InitLogic(app *servus.Application, conf *config.AppConfig) {
	AuditTrailProcess = NewAuditTrailProcess(conf)
	app.MustRegister("process.audittrail", AuditTrailProcess)
}

// NewAuditTrailProcess ...
func NewAuditTrailProcess(cfg *config.AppConfig) *AuditTrailImpl {
	return &AuditTrailImpl{AppConfig: cfg}
}
