package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"log"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateAuditTrails inserts a new audit trail record into the database.
// nolint:dupl
func CreateAuditTrails(ctx context.Context, db *sql.DB, p *AuditTrailsDTO) (id int64, err error) {
	// Marshal ExtraParams to JSON if it's not nil
	var extraParamsJSON []byte
	if p.ExtraParams != nil {
		extraParamsJSON, err = json.Marshal(p.ExtraParams)
		if err != nil {
			return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
	}

	var query = `INSERT INTO audit_trails (identifier, identifier_type, title, description, activity_type, reference_id, created_by, created_at, extra_params) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := db.ExecContext(ctx, query, p.Identifier, p.IdentifierType, p.Title, p.Description, p.ActivityType, p.ReferenceID, p.CreatedBy, p.CreatedAt, extraParamsJSON)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// GetAuditTrails retrieves a list of audit trail records based on the provided query conditions.
func GetAuditTrails(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition) ([]*AuditTrailsDTO, error) {
	var query = `
		SELECT
		    at.id, at.identifier, at.identifier_type, at.title, at.description,
		    at.activity_type, at.reference_id, at.created_by, at.created_at, at.extra_params,
		    u.name AS user_name
		FROM audit_trails at
		JOIN users u ON at.created_by = u.id
	`
	query, args := commonStorage.BuildQuery(query, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		closeErr := rows.Close()
		if closeErr != nil && err == nil {
			err = errorwrapper.Error(apiError.InternalServerError, closeErr.Error())
		}
	}()

	var res []*AuditTrailsDTO
	for rows.Next() {
		var p AuditTrailsDTO
		err = rows.Scan(&p.ID, &p.Identifier, &p.IdentifierType, &p.Title, &p.Description,
			&p.ActivityType, &p.ReferenceID, &p.CreatedBy, &p.CreatedAt, &p.ExtraParams, &p.UserName)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, errorwrapper.Error(apiError.ResourceNotFound, "No audit trails found")
			}
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		res = append(res, &p)
	}

	return res, nil
}

// CreateAuditTrail ...
func CreateAuditTrail(ctx context.Context, db *sql.DB, p *AuditTrailDTO) (id int64, err error) {
	query := p.QueryInsert()
	result, err := db.ExecContext(ctx, query, p.Identifier, p.IdentifierType, p.Title, p.Description, p.ActivityType, p.ReferenceID, p.ExtraParams, p.CreatedBy, p.CreatedAt)
	if err != nil {
		log.Println(err)
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err = result.LastInsertId()
	if err != nil {
		log.Println(err)
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}
