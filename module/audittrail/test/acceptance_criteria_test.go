package test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
)

// AuditTrailAcceptanceCriteriaTestSuite defines the test suite for audit trail acceptance criteria
type AuditTrailAcceptanceCriteriaTestSuite struct {
	suite.Suite
}

// TestAuditLogEventDetailsTableFormat tests the audit log event details table format
func (suite *AuditTrailAcceptanceCriteriaTestSuite) TestAuditLogEventDetailsTableFormat() {
	// Create a sample audit trail with all required fields
	now := time.Now()
	auditTrail := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: now, Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Case Created",
		Description:    "Case was created by User",
		ActivityType:   constants.Ticket,
		ReferenceID:    "789",
		ExtraParams: map[string]interface{}{
			"event_type": "CASE_CREATED",
			"user_name":  "John Doe",
		},
	}

	// Verify Event Name: Correct mapping to database fields
	assert.Equal(suite.T(), constants.Ticket, auditTrail.ActivityType, "Event Name should be mapped to ActivityType field")

	// Verify Event Description: Proper population from description field
	assert.Equal(suite.T(), "Case was created by User", auditTrail.Description, "Event Description should be populated from Description field")

	// Verify Actioned User: Accurate user attribution using created_by field
	assert.Equal(suite.T(), int64(123), auditTrail.CreatedBy.Int64, "Actioned User should be mapped to CreatedBy field")
	assert.True(suite.T(), auditTrail.CreatedBy.Valid, "CreatedBy field should be valid")

	// Verify Actioned Timestamp: Correct date/time format
	assert.True(suite.T(), auditTrail.CreatedAt.Valid, "CreatedAt field should be valid")

	// Verify ExtraParams contains event_type
	extraParams, ok := auditTrail.ExtraParams.(map[string]interface{})
	assert.True(suite.T(), ok, "ExtraParams should be a map")
	eventType, ok := extraParams["event_type"]
	assert.True(suite.T(), ok, "ExtraParams should contain event_type")
	assert.Equal(suite.T(), "CASE_CREATED", eventType, "event_type should be CASE_CREATED")
}

// TestStandardEventsImplementation tests the standard events implementation
func (suite *AuditTrailAcceptanceCriteriaTestSuite) TestStandardEventsImplementation() {
	// Test Case Created event
	caseCreatedAudit := createAuditTrailWithEventType("CASE_CREATED")
	extraParams, _ := caseCreatedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "CASE_CREATED", extraParams["event_type"], "Standard event CASE_CREATED should be implemented")

	// Test Task Assigned event
	taskAssignedAudit := createAuditTrailWithEventType("TASK_ASSIGNED")
	extraParams, _ = taskAssignedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "TASK_ASSIGNED", extraParams["event_type"], "Standard event TASK_ASSIGNED should be implemented")
	assert.Contains(suite.T(), extraParams, "assignee_name", "TASK_ASSIGNED event should contain assignee_name")

	// Test Task Completed event
	taskCompletedAudit := createAuditTrailWithEventType("TASK_COMPLETED")
	extraParams, _ = taskCompletedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "TASK_COMPLETED", extraParams["event_type"], "Standard event TASK_COMPLETED should be implemented")

	// Test Case Completed event
	caseCompletedAudit := createAuditTrailWithEventType("CASE_COMPLETED")
	extraParams, _ = caseCompletedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "CASE_COMPLETED", extraParams["event_type"], "Standard event CASE_COMPLETED should be implemented")

	// Test Case Rejected event
	caseRejectedAudit := createAuditTrailWithEventType("CASE_REJECTED")
	extraParams, _ = caseRejectedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "CASE_REJECTED", extraParams["event_type"], "Standard event CASE_REJECTED should be implemented")

	// Test System Task Failed event
	systemTaskFailedAudit := createAuditTrailWithEventType("SYSTEM_TASK_FAILED")
	extraParams, _ = systemTaskFailedAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "SYSTEM_TASK_FAILED", extraParams["event_type"], "Standard event SYSTEM_TASK_FAILED should be implemented")
	assert.Contains(suite.T(), extraParams, "error_details", "SYSTEM_TASK_FAILED event should contain error_details")
}

// TestFrameworkExtensibility tests the framework extensibility
func (suite *AuditTrailAcceptanceCriteriaTestSuite) TestFrameworkExtensibility() {
	// Test custom event type
	customEventAudit := createAuditTrailWithEventType("CUSTOM_EVENT")
	extraParams, _ := customEventAudit.ExtraParams.(map[string]interface{})
	assert.Equal(suite.T(), "CUSTOM_EVENT", extraParams["event_type"], "Framework should support custom event types")

	// Test cross case types
	crossCaseAudit := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: "CUSTOM_CASE_TYPE", // Custom case type
		Title:          "Custom Case Event",
		Description:    "Custom case event occurred",
		ActivityType:   "CUSTOM_ACTIVITY", // Custom activity type
		ReferenceID:    "789",
		ExtraParams: map[string]interface{}{
			"event_type": "CUSTOM_EVENT",
			"custom_data": map[string]interface{}{
				"key1": "value1",
				"key2": "value2",
			},
		},
	}

	// Verify custom case type and activity type
	assert.Equal(suite.T(), "CUSTOM_CASE_TYPE", crossCaseAudit.IdentifierType, "Framework should support custom case types")
	assert.Equal(suite.T(), "CUSTOM_ACTIVITY", crossCaseAudit.ActivityType, "Framework should support custom activity types")

	// Verify custom event data
	extraParams, _ = crossCaseAudit.ExtraParams.(map[string]interface{})
	customData, ok := extraParams["custom_data"].(map[string]interface{})
	assert.True(suite.T(), ok, "Framework should support custom event data")
	assert.Equal(suite.T(), "value1", customData["key1"], "Custom data should be preserved")
	assert.Equal(suite.T(), "value2", customData["key2"], "Custom data should be preserved")
}

// TestChronologicalOrdering tests the chronological ordering
func (suite *AuditTrailAcceptanceCriteriaTestSuite) TestChronologicalOrdering() {
	// Create audit trails with different timestamps
	now := time.Now()
	auditTrail1 := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: now.Add(-2 * time.Hour), Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "First Event",
	}

	auditTrail2 := &storage.AuditTrailsDTO{
		ID:             2,
		CreatedAt:      sql.NullTime{Time: now.Add(-1 * time.Hour), Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Second Event",
	}

	auditTrail3 := &storage.AuditTrailsDTO{
		ID:             3,
		CreatedAt:      sql.NullTime{Time: now, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Third Event",
	}

	// Create a slice of audit trails in chronological order
	auditTrails := []*storage.AuditTrailsDTO{auditTrail1, auditTrail2, auditTrail3}

	// Verify chronological ordering
	assert.True(suite.T(), auditTrails[0].CreatedAt.Time.Before(auditTrails[1].CreatedAt.Time), "Audit trails should be in chronological order")
	assert.True(suite.T(), auditTrails[1].CreatedAt.Time.Before(auditTrails[2].CreatedAt.Time), "Audit trails should be in chronological order")
}

// TestDataSnapshots tests the data snapshots
func (suite *AuditTrailAcceptanceCriteriaTestSuite) TestDataSnapshots() {
	// Create an audit trail with a data snapshot
	auditTrail := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Case Status Changed",
		Description:    "Case status was changed",
		ActivityType:   constants.Ticket,
		ReferenceID:    "789",
		ExtraParams: map[string]interface{}{
			"event_type":     "CASE_STATUS_CHANGED",
			"ticket_id":      int64(456),
			"element_id":     int64(789),
			"priority_id":    int64(1),
			"prev_status_id": int64(2),
			"next_status_id": int64(3),
			"source":         "test source",
			"deadline_time":  time.Now().Add(24 * time.Hour),
			"assignee_id":    int64(123),
			"data":           map[string]interface{}{"key": "value"},
		},
	}

	// Verify data snapshot contains all relevant case/task details
	extraParams, _ := auditTrail.ExtraParams.(map[string]interface{})
	assert.Contains(suite.T(), extraParams, "ticket_id", "Data snapshot should contain ticket_id")
	assert.Contains(suite.T(), extraParams, "element_id", "Data snapshot should contain element_id")
	assert.Contains(suite.T(), extraParams, "priority_id", "Data snapshot should contain priority_id")
	assert.Contains(suite.T(), extraParams, "prev_status_id", "Data snapshot should contain prev_status_id")
	assert.Contains(suite.T(), extraParams, "next_status_id", "Data snapshot should contain next_status_id")
	assert.Contains(suite.T(), extraParams, "source", "Data snapshot should contain source")
	assert.Contains(suite.T(), extraParams, "deadline_time", "Data snapshot should contain deadline_time")
	assert.Contains(suite.T(), extraParams, "assignee_id", "Data snapshot should contain assignee_id")
	assert.Contains(suite.T(), extraParams, "data", "Data snapshot should contain data")
}

// Helper function to create an audit trail with a specific event type
func createAuditTrailWithEventType(eventType string) *storage.AuditTrailsDTO {
	extraParams := map[string]interface{}{
		"event_type": eventType,
	}

	// Add event-specific data
	switch eventType {
	case "TASK_ASSIGNED":
		extraParams["assignee_name"] = "John Doe"
		extraParams["assignee_id"] = int64(123)
	case "SYSTEM_TASK_FAILED":
		extraParams["error_details"] = "Connection timeout"
		extraParams["error_code"] = "TIMEOUT"
	}

	return &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          eventType,
		Description:    "Event description",
		ActivityType:   constants.Ticket,
		ReferenceID:    "789",
		ExtraParams:    extraParams,
	}
}

// TestAuditTrailAcceptanceCriteriaSuite runs the test suite
func TestAuditTrailAcceptanceCriteriaSuite(t *testing.T) {
	suite.Run(t, new(AuditTrailAcceptanceCriteriaTestSuite))
}
