package test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
)

// AuditTrailFrameworkTestSuite defines the test suite for audit trail framework
type AuditTrailFrameworkTestSuite struct {
	suite.Suite
}

// TestFrameworkGenericImplementation tests the generic implementation of the framework
func (suite *AuditTrailFrameworkTestSuite) TestFrameworkGenericImplementation() {
	// Create a standard audit trail
	standardAudit := createAuditTrailWithEventType("CASE_CREATED")

	// Create a custom audit trail with a non-standard event type
	customAudit := createAuditTrailWithEventType("CUSTOM_EVENT_TYPE")

	// Verify both audit trails have the same structure
	assert.Equal(suite.T(), standardAudit.Identifier, customAudit.Identifier)
	assert.Equal(suite.T(), standardAudit.IdentifierType, customAudit.IdentifierType)
	assert.Equal(suite.T(), standardAudit.ActivityType, customAudit.ActivityType)

	// Verify the event types are different
	standardExtraParams, _ := standardAudit.ExtraParams.(map[string]interface{})
	customExtraParams, _ := customAudit.ExtraParams.(map[string]interface{})

	assert.Equal(suite.T(), "CASE_CREATED", standardExtraParams["event_type"])
	assert.Equal(suite.T(), "CUSTOM_EVENT_TYPE", customExtraParams["event_type"])
}

// TestFrameworkCrossCaseTypes tests the framework's ability to work across different case types
func (suite *AuditTrailFrameworkTestSuite) TestFrameworkCrossCaseTypes() {
	// Create audit trails with different case types
	ticketAudit := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Ticket Event",
		Description:    "Ticket event occurred",
		ActivityType:   constants.Ticket,
		ExtraParams: map[string]interface{}{
			"event_type": "CASE_CREATED",
		},
	}

	userAudit := &storage.AuditTrailsDTO{
		ID:             2,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "789",
		IdentifierType: constants.UserID,
		Title:          "User Event",
		Description:    "User event occurred",
		ActivityType:   constants.User,
		ExtraParams: map[string]interface{}{
			"event_type": "USER_CREATED",
		},
	}

	roleAudit := &storage.AuditTrailsDTO{
		ID:             3,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "101",
		IdentifierType: constants.RoleID,
		Title:          "Role Event",
		Description:    "Role event occurred",
		ActivityType:   constants.Role,
		ExtraParams: map[string]interface{}{
			"event_type": "ROLE_CREATED",
		},
	}

	// Verify different case types have the same structure
	assert.Equal(suite.T(), ticketAudit.CreatedBy, userAudit.CreatedBy)
	assert.Equal(suite.T(), userAudit.CreatedBy, roleAudit.CreatedBy)

	// Verify different identifier types
	assert.Equal(suite.T(), constants.TicketID, ticketAudit.IdentifierType)
	assert.Equal(suite.T(), constants.UserID, userAudit.IdentifierType)
	assert.Equal(suite.T(), constants.RoleID, roleAudit.IdentifierType)

	// Verify different activity types
	assert.Equal(suite.T(), constants.Ticket, ticketAudit.ActivityType)
	assert.Equal(suite.T(), constants.User, userAudit.ActivityType)
	assert.Equal(suite.T(), constants.Role, roleAudit.ActivityType)
}

// TestFrameworkConfiguration tests the framework's configuration capabilities
func (suite *AuditTrailFrameworkTestSuite) TestFrameworkConfiguration() {
	// Create a standard audit trail
	standardAudit := createAuditTrailWithEventType("CASE_CREATED")

	// Create a custom audit trail with additional configuration
	customConfig := map[string]interface{}{
		"event_type": "CUSTOM_EVENT_TYPE",
		"config": map[string]interface{}{
			"retention_days": 90,
			"priority":       "high",
			"notify_users":   []string{"user1", "user2"},
			"custom_field1":  "custom value 1",
			"custom_field2":  123,
		},
	}

	customAudit := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "456",
		IdentifierType: constants.TicketID,
		Title:          "Custom Event",
		Description:    "Custom event with configuration",
		ActivityType:   constants.Ticket,
		ExtraParams:    customConfig,
	}

	// Verify the custom audit trail has the same structure as the standard one
	assert.Equal(suite.T(), standardAudit.Identifier, customAudit.Identifier)
	assert.Equal(suite.T(), standardAudit.IdentifierType, customAudit.IdentifierType)
	assert.Equal(suite.T(), standardAudit.ActivityType, customAudit.ActivityType)

	// Verify the custom configuration
	customExtraParams, _ := customAudit.ExtraParams.(map[string]interface{})
	config, ok := customExtraParams["config"].(map[string]interface{})
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), int(90), config["retention_days"])
	assert.Equal(suite.T(), "high", config["priority"])

	// Verify array values
	notifyUsers, ok := config["notify_users"].([]string)
	assert.True(suite.T(), ok)
	assert.Contains(suite.T(), notifyUsers, "user1")
	assert.Contains(suite.T(), notifyUsers, "user2")
}

// TestReadOnlyAccess tests that audit logs are read-only
func (suite *AuditTrailFrameworkTestSuite) TestReadOnlyAccess() {
	// This test is conceptual since we can't directly test database constraints in a unit test
	// In a real implementation, we would test that:
	// 1. There are no UPDATE or DELETE endpoints for audit trails
	// 2. Database constraints prevent modification of audit records
	// 3. All audit trail API endpoints are GET only

	// For this test, we'll verify that our DTO doesn't have methods for updating
	auditTrail := createAuditTrailWithEventType("CASE_CREATED")

	// Verify the audit trail has an ID and creation timestamp
	assert.NotZero(suite.T(), auditTrail.ID)
	assert.True(suite.T(), auditTrail.CreatedAt.Valid)

	// In a real database, we would ensure:
	// - No UPDATE triggers exist for the audit_trails table
	// - No DELETE triggers exist for the audit_trails table
	// - Appropriate database permissions prevent UPDATE/DELETE operations
}

// TestDataSnapshots tests the data snapshot capabilities
func (suite *AuditTrailFrameworkTestSuite) TestDataSnapshots() {
	// Create an audit trail with a complete data snapshot
	now := time.Now()

	dataSnapshot := map[string]interface{}{
		"ticket_id":      int64(123),
		"element_id":     int64(456),
		"priority_id":    int64(1),
		"prev_status_id": int64(2),
		"next_status_id": int64(3),
		"source":         "test source",
		"deadline_time":  now,
		"assignee_id":    int64(789),
		"data": map[string]interface{}{
			"field1": "value1",
			"field2": 123,
			"field3": true,
			"nested": map[string]interface{}{
				"subfield1": "subvalue1",
				"subfield2": 456,
			},
		},
		"event_type": "CASE_STATUS_CHANGED",
	}

	auditTrail := &storage.AuditTrailsDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: now, Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 123, Valid: true},
		Identifier:     "123",
		IdentifierType: constants.TicketID,
		Title:          "Case Status Changed",
		Description:    "Case status was changed",
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}

	// Verify the data snapshot contains all required fields
	extraParams, ok := auditTrail.ExtraParams.(map[string]interface{})
	assert.True(suite.T(), ok)

	// Verify basic fields
	assert.Equal(suite.T(), int64(123), extraParams["ticket_id"])
	assert.Equal(suite.T(), int64(456), extraParams["element_id"])
	assert.Equal(suite.T(), int64(1), extraParams["priority_id"])
	assert.Equal(suite.T(), "test source", extraParams["source"])

	// Verify nested data
	data, ok := extraParams["data"].(map[string]interface{})
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), "value1", data["field1"])
	assert.Equal(suite.T(), 123, data["field2"])
	assert.Equal(suite.T(), true, data["field3"])

	// Verify deeply nested data
	nested, ok := data["nested"].(map[string]interface{})
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), "subvalue1", nested["subfield1"])
	assert.Equal(suite.T(), 456, nested["subfield2"])
}

// TestAuditTrailFrameworkSuite runs the test suite
func TestAuditTrailFrameworkSuite(t *testing.T) {
	suite.Run(t, new(AuditTrailFrameworkTestSuite))
}
