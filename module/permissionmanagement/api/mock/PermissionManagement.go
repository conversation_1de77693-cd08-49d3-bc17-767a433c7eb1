// Code generated by mockery v2.44.1. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"

	mock "github.com/stretchr/testify/mock"
)

// PermissionManagement is an autogenerated mock type for the PermissionManagement type
type PermissionManagement struct {
	mock.Mock
}

// Authenticate provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) Authenticate(ctx context.Context, req *api.AuthenticateRequest) (*api.AuthenticateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Authenticate")
	}

	var r0 *api.AuthenticateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AuthenticateRequest) (*api.AuthenticateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AuthenticateRequest) *api.AuthenticateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AuthenticateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AuthenticateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePermission provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) CreatePermission(ctx context.Context, req *api.CreatePermissionRequest) (*api.CreatePermissionResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreatePermission")
	}

	var r0 *api.CreatePermissionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePermissionRequest) (*api.CreatePermissionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePermissionRequest) *api.CreatePermissionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreatePermissionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreatePermissionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRole provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) CreateRole(ctx context.Context, req *api.CreateRoleRequest) (*api.CreateRoleResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateRole")
	}

	var r0 *api.CreateRoleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateRoleRequest) (*api.CreateRoleResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateRoleRequest) *api.CreateRoleResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateRoleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateRoleRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateUser provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) CreateUser(ctx context.Context, req *api.CreateUserRequest) (*api.CreateUserResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *api.CreateUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateUserRequest) (*api.CreateUserResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateUserRequest) *api.CreateUserResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateUserRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetListRole provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) GetListRole(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetListRole")
	}

	var r0 *api.GetListRoleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetListRoleRequest) (*api.GetListRoleResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetListRoleRequest) *api.GetListRoleResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetListRoleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetListRoleRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPermissionList provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) GetPermissionList(ctx context.Context, req *api.GetPermissionListRequest) (*api.GetPermissionListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPermissionList")
	}

	var r0 *api.GetPermissionListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPermissionListRequest) (*api.GetPermissionListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPermissionListRequest) *api.GetPermissionListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPermissionListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPermissionListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRoleDetail provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) GetRoleDetail(ctx context.Context, req *api.GetRoleDetailRequest) (*api.GetRoleDetailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetRoleDetail")
	}

	var r0 *api.GetRoleDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetRoleDetailRequest) (*api.GetRoleDetailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetRoleDetailRequest) *api.GetRoleDetailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetRoleDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetRoleDetailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserPermissions provides a mock function with given fields: ctx
func (_m *PermissionManagement) GetUserPermissions(ctx context.Context) (*api.GetUserPermissionsResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPermissions")
	}

	var r0 *api.GetUserPermissionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetUserPermissionsResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetUserPermissionsResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetUserPermissionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUsers provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) GetUsers(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetUsers")
	}

	var r0 *api.GetUsersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetUsersRequest) (*api.GetUsersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetUsersRequest) *api.GetUsersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetUsersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetUsersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Login provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) Login(ctx context.Context, req *api.LoginRequest) (*api.LoginResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Login")
	}

	var r0 *api.LoginResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.LoginRequest) (*api.LoginResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.LoginRequest) *api.LoginResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.LoginResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.LoginRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Logout provides a mock function with given fields: ctx
func (_m *PermissionManagement) Logout(ctx context.Context) (*api.LogoutResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Logout")
	}

	var r0 *api.LogoutResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.LogoutResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.LogoutResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.LogoutResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefreshLogin provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) RefreshLogin(ctx context.Context, req *api.RefreshLoginRequest) (*api.RefreshLoginResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RefreshLogin")
	}

	var r0 *api.RefreshLoginResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.RefreshLoginRequest) (*api.RefreshLoginResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.RefreshLoginRequest) *api.RefreshLoginResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.RefreshLoginResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.RefreshLoginRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ResetPassword provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) ResetPassword(ctx context.Context, req *api.ResetPasswordRequest) (*api.ResetPasswordResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ResetPassword")
	}

	var r0 *api.ResetPasswordResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ResetPasswordRequest) (*api.ResetPasswordResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ResetPasswordRequest) *api.ResetPasswordResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ResetPasswordResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ResetPasswordRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdatePermission provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdatePermission(ctx context.Context, req *api.UpdatePermissionRequest) (*api.UpdatePermissionResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePermission")
	}

	var r0 *api.UpdatePermissionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePermissionRequest) (*api.UpdatePermissionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePermissionRequest) *api.UpdatePermissionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdatePermissionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdatePermissionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdatePermissionStatus provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdatePermissionStatus(ctx context.Context, req *api.UpdatePermissionStatusRequest) (*api.UpdatePermissionStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePermissionStatus")
	}

	var r0 *api.UpdatePermissionStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePermissionStatusRequest) (*api.UpdatePermissionStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdatePermissionStatusRequest) *api.UpdatePermissionStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdatePermissionStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdatePermissionStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateRole provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdateRole(ctx context.Context, req *api.UpdateRoleRequest) (*api.UpdateRoleResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRole")
	}

	var r0 *api.UpdateRoleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateRoleRequest) (*api.UpdateRoleResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateRoleRequest) *api.UpdateRoleResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateRoleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateRoleRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateRoleStatus provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdateRoleStatus(ctx context.Context, req *api.UpdateRoleStatusRequest) (*api.UpdateRoleStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRoleStatus")
	}

	var r0 *api.UpdateRoleStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateRoleStatusRequest) (*api.UpdateRoleStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateRoleStatusRequest) *api.UpdateRoleStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateRoleStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateRoleStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateUser provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdateUser(ctx context.Context, req *api.UpdateUserRequest) (*api.UpdateUserResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *api.UpdateUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateUserRequest) (*api.UpdateUserResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateUserRequest) *api.UpdateUserResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateUserRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateUserStatus provides a mock function with given fields: ctx, req
func (_m *PermissionManagement) UpdateUserStatus(ctx context.Context, req *api.UpdateUserStatusRequest) (*api.UpdateUserStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserStatus")
	}

	var r0 *api.UpdateUserStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateUserStatusRequest) (*api.UpdateUserStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateUserStatusRequest) *api.UpdateUserStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateUserStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateUserStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPermissionManagement creates a new instance of PermissionManagement. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPermissionManagement(t interface {
	mock.TestingT
	Cleanup(func())
}) *PermissionManagement {
	mock := &PermissionManagement{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
