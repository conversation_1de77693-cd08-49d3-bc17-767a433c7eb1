// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: permissionmanagement.proto
package api

import (
	context "context"
)

type LoginType string

const (
	LoginType_NATIVE    LoginType = "NATIVE"
	LoginType_JUMPCLOUD LoginType = "JUMPCLOUD"
	LoginType_SERVICE   LoginType = "SERVICE"
)

type SortOrder string

const (
	SortOrder_ASC  SortOrder = "ASC"
	SortOrder_DESC SortOrder = "DESC"
)

type CreateUserRequest struct {
	Name     string  `json:"name,omitempty" validate:"required"`
	Email    string  `json:"email,omitempty" validate:"required,email"`
	Status   int64   `json:"status,omitempty" validate:"binary"`
	RoleIDs  []int64 `json:"roleIDs,omitempty"`
	Password string  `json:"password,omitempty"`
}

type CreateUserResponse struct {
	Status string `json:"status,omitempty"`
}

type UpdateUserRequest struct {
	Name    string  `json:"name,omitempty" validate:"required"`
	Email   string  `json:"email,omitempty" validate:"required,email"`
	Status  int64   `json:"status,omitempty" validate:"binary"`
	RoleIDs []int64 `json:"roleIDs,omitempty"`
	UserID  string  `json:"userID,omitempty"`
}

type UpdateUserResponse struct {
	Status string `json:"status,omitempty"`
}

type LoginRequest struct {
	Email        string    `json:"email,omitempty" validate:"omitempty,email,email_domain"`
	Password     string    `json:"password,omitempty"`
	Type         LoginType `json:"type,omitempty" validate:"required,oneof=NATIVE JUMPCLOUD SERVICE"`
	Token        string    `json:"token,omitempty"`
	ClientID     string    `json:"clientID,omitempty"`
	ClientSecret string    `json:"clientSecret,omitempty"`
}

type LoginResponse struct {
	Token        string `json:"token,omitempty"`
	RefreshToken string `json:"refreshToken,omitempty"`
}

type RefreshLoginRequest struct {
	RefreshToken string `json:"refreshToken,omitempty" validate:"required"`
}

type RefreshLoginResponse struct {
	Status string `json:"status,omitempty"`
	Token  string `json:"token,omitempty"`
}

type LogoutRequest struct {
}

type LogoutResponse struct {
	Status string `json:"status,omitempty"`
}

type ResetPasswordRequest struct {
	Password        string `json:"password,omitempty" validate:"required"`
	ConfirmPassword string `json:"confirmPassword,omitempty" validate:"required"`
}

type ResetPasswordResponse struct {
	Status string `json:"status,omitempty"`
}

type GetUsersRequest struct {
	SearchKey string   `json:"searchKey,omitempty"`
	Offset    int64    `json:"offset,omitempty" validate:"offset"`
	Limit     int64    `json:"limit,omitempty" validate:"limit"`
	Filter    []Filter `json:"filter,omitempty" validate:"omitempty,users_filter"`
	SortBy    *Sort    `json:"sortBy,omitempty" validate:"omitempty,users_sort"`
}

type GetUsersResponse struct {
	Count  int64  `json:"count,omitempty"`
	Offset int64  `json:"offset,omitempty"`
	Data   []User `json:"data,omitempty"`
}

type User struct {
	Id        int64    `json:"id,omitempty"`
	Name      string   `json:"name,omitempty"`
	Email     string   `json:"email,omitempty"`
	CreatedAt string   `json:"createdAt,omitempty"`
	UpdatedAt string   `json:"updatedAt,omitempty"`
	CreatedBy string   `json:"createdBy,omitempty"`
	UpdatedBy string   `json:"updatedBy,omitempty"`
	Status    int64    `json:"status,omitempty"`
	Roles     []string `json:"roles,omitempty"`
	UserID    string   `json:"userID,omitempty"`
}

type CreateRoleRequest struct {
	Name               string                      `json:"name,omitempty" validate:"required"`
	Status             int64                       `json:"status,omitempty" validate:"binary"`
	ElementPermissions []ElementPermissionsRequest `json:"elementPermissions,omitempty"`
}

type ElementPermissionsRequest struct {
	ElementID      int64   `json:"elementID,omitempty"`
	PermissionsIDs []int64 `json:"permissionsIDs,omitempty"`
}

type CreateRoleResponse struct {
	Id        int64  `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Status    int64  `json:"status,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
}

type UpdateRoleRequest struct {
	Id                 int64                       `json:"id,omitempty"`
	Name               string                      `json:"name,omitempty" validate:"required"`
	Status             int64                       `json:"status,omitempty" validate:"binary"`
	ElementPermissions []ElementPermissionsRequest `json:"elementPermissions,omitempty"`
}

type UpdateRoleResponse struct {
	Id        int64  `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Status    int64  `json:"status,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	UpdatedBy string `json:"updatedBy,omitempty"`
}

type CreatePermissionRequest struct {
	Name        string `json:"name,omitempty" validate:"required"`
	Description string `json:"description,omitempty"`
	Bitwise     int64  `json:"bitwise,omitempty" validate:"required,permission_bitwise"`
	ModuleID    int64  `json:"moduleID,omitempty" validate:"required"`
	Status      int32  `json:"status,omitempty"`
}

type CreatePermissionResponse struct {
	Id     int64  `json:"id,omitempty"`
	Status string `json:"status,omitempty"`
}

type UpdatePermissionRequest struct {
	Id          int64  `json:"id,omitempty"`
	Name        string `json:"name,omitempty" validate:"required"`
	Description string `json:"description,omitempty"`
	Bitwise     int64  `json:"bitwise,omitempty" validate:"required,permission_bitwise"`
	ModuleID    int64  `json:"moduleID,omitempty" validate:"required"`
	Status      int32  `json:"status,omitempty"`
}

type UpdatePermissionResponse struct {
	Id     int64  `json:"id,omitempty"`
	Status string `json:"status,omitempty"`
}

type GetListRoleRequest struct {
	SearchKey string   `json:"searchKey,omitempty"`
	Offset    int64    `json:"offset,omitempty" validate:"offset"`
	Limit     int64    `json:"limit,omitempty" validate:"limit"`
	Filter    []Filter `json:"filter,omitempty" validate:"omitempty,roles_filter"`
	SortBy    *Sort    `json:"sortBy,omitempty" validate:"omitempty,roles_sort_by"`
}

type Filter struct {
	Column string        `json:"column,omitempty"`
	Value  []interface{} `json:"value,omitempty"`
}

type Sort struct {
	Column string    `json:"column,omitempty"`
	Sort   SortOrder `json:"sort,omitempty"`
}

type GetListRoleResponse struct {
	Count  int64  `json:"count,omitempty"`
	Offset int64  `json:"offset,omitempty"`
	Data   []Role `json:"data,omitempty"`
}

type Role struct {
	Id        int64  `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Status    int64  `json:"status,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
	UpdatedBy string `json:"updatedBy,omitempty"`
}

type GetUserPermissionsRequest struct {
}

type GetUserPermissionsResponse struct {
	UserId      string           `json:"userId,omitempty"`
	Email       string           `json:"email,omitempty"`
	Name        string           `json:"name,omitempty"`
	Modules     []string         `json:"modules,omitempty"`
	Roles       []string         `json:"roles,omitempty"`
	Permissions []UserPermission `json:"permissions,omitempty"`
}

type UserPermission struct {
	ElementCode string `json:"elementCode,omitempty"`
	Bitwise     int64  `json:"bitwise,omitempty"`
}

type UpdateUserStatusRequest struct {
	UserID string `json:"userID,omitempty" validate:"required"`
	Status int64  `json:"status,omitempty"`
}

type UpdateUserStatusResponse struct {
	Status string `json:"status,omitempty"`
}

type UpdateRoleStatusRequest struct {
	Id     int64 `json:"id,omitempty" validate:"required"`
	Status int64 `json:"status,omitempty" validate:"binary"`
}

type UpdateRoleStatusResponse struct {
	Id     int64  `json:"id,omitempty"`
	Status string `json:"status,omitempty"`
}

type GetRoleDetailRequest struct {
	Id int64 `json:"id,omitempty" validate:"required"`
}

type GetRoleDetailResponse struct {
	Id        int64              `json:"id,omitempty"`
	Name      string             `json:"name,omitempty"`
	Status    int64              `json:"status,omitempty"`
	CreatedAt string             `json:"createdAt,omitempty"`
	CreatedBy string             `json:"createdBy,omitempty"`
	UpdatedAt string             `json:"updatedAt,omitempty"`
	UpdatedBy string             `json:"updatedBy,omitempty"`
	Modules   []RoleDetailModule `json:"modules,omitempty"`
}

type RoleDetailModule struct {
	ModuleID int64               `json:"moduleID,omitempty"`
	Name     string              `json:"name,omitempty"`
	Elements []RoleDetailElement `json:"elements,omitempty"`
}

type RoleDetailElement struct {
	ElementID   int64                  `json:"elementID,omitempty"`
	Name        string                 `json:"name,omitempty"`
	Permissions []RoleDetailPermission `json:"permissions,omitempty"`
}

type RoleDetailPermission struct {
	PermissionID int64  `json:"permissionID,omitempty"`
	Name         string `json:"name,omitempty"`
}

type GetPermissionListRequest struct {
	SearchKey string   `json:"searchKey,omitempty"`
	Offset    int64    `json:"offset,omitempty" validate:"offset"`
	Limit     int64    `json:"limit,omitempty" validate:"limit"`
	Filter    []Filter `json:"filter,omitempty" validate:"omitempty,permissions_filter"`
	SortBy    *Sort    `json:"sortBy,omitempty" validate:"omitempty,permissions_sort_by"`
}

type GetPermissionListResponse struct {
	Count  int64        `json:"count,omitempty"`
	Offset int64        `json:"offset,omitempty"`
	Data   []Permission `json:"data,omitempty"`
}

type Permission struct {
	Id          int64  `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	CreatedAt   string `json:"createdAt,omitempty"`
	UpdatedAt   string `json:"updatedAt,omitempty"`
	CreatedBy   string `json:"createdBy,omitempty"`
	UpdatedBy   string `json:"updatedBy,omitempty"`
	Status      int64  `json:"status,omitempty"`
	ModuleID    int64  `json:"moduleID,omitempty"`
	ModuleName  string `json:"moduleName,omitempty"`
	Bitwise     int64  `json:"bitwise,omitempty"`
}

type UpdatePermissionStatusRequest struct {
	Id     int64 `json:"id,omitempty" validate:"required"`
	Status int32 `json:"status,omitempty" validate:"binary"`
}

type UpdatePermissionStatusResponse struct {
	Status string `json:"status,omitempty"`
}

// AuthenticateRequest ...
type AuthenticateRequest struct {
	Token           string `json:"token,omitempty" validate:"required"`
	ElementCode     string `json:"elementCode,omitempty" validate:"required"`
	BitwiseRequired int64  `json:"bitwiseRequired,omitempty"`
}

// AuthenticateResponse ...
type AuthenticateResponse struct {
	UserID      string       `json:"userID,omitempty"`
	Permissions []Permission `json:"permissions,omitempty"`
}

type PermissionManagement interface {
	CreateUser(ctx context.Context, req *CreateUserRequest) (*CreateUserResponse, error)
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	RefreshLogin(ctx context.Context, req *RefreshLoginRequest) (*RefreshLoginResponse, error)
	Logout(ctx context.Context) (*LogoutResponse, error)
	ResetPassword(ctx context.Context, req *ResetPasswordRequest) (*ResetPasswordResponse, error)
	GetUsers(ctx context.Context, req *GetUsersRequest) (*GetUsersResponse, error)
	GetListRole(ctx context.Context, req *GetListRoleRequest) (*GetListRoleResponse, error)
	CreateRole(ctx context.Context, req *CreateRoleRequest) (*CreateRoleResponse, error)
	UpdateRole(ctx context.Context, req *UpdateRoleRequest) (*UpdateRoleResponse, error)
	CreatePermission(ctx context.Context, req *CreatePermissionRequest) (*CreatePermissionResponse, error)
	UpdatePermission(ctx context.Context, req *UpdatePermissionRequest) (*UpdatePermissionResponse, error)
	// GetUserPermissions is API to get user detail and permission
	GetUserPermissions(ctx context.Context) (*GetUserPermissionsResponse, error)
	// UpdateUserStatus: API to update user status
	UpdateUserStatus(ctx context.Context, req *UpdateUserStatusRequest) (*UpdateUserStatusResponse, error)
	// UpdateUser: API to update user
	UpdateUser(ctx context.Context, req *UpdateUserRequest) (*UpdateUserResponse, error)
	// UpdateRoleStatus is API for deactivate and reactivate role
	UpdateRoleStatus(ctx context.Context, req *UpdateRoleStatusRequest) (*UpdateRoleStatusResponse, error)
	// GetRoleDetail is API for get role details by id
	GetRoleDetail(ctx context.Context, req *GetRoleDetailRequest) (*GetRoleDetailResponse, error)
	// GetPermissionList is API for get permission list in admin config
	GetPermissionList(ctx context.Context, req *GetPermissionListRequest) (*GetPermissionListResponse, error)
	// UpdatePermissionStatus is API for update permission in admin config
	UpdatePermissionStatus(ctx context.Context, req *UpdatePermissionStatusRequest) (*UpdatePermissionStatusResponse, error)
	// Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe
	Authenticate(ctx context.Context, req *AuthenticateRequest) (*AuthenticateResponse, error)
}
