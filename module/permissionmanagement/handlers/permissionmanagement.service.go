package handlers

import (
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// PermissionManagementService serves as the context for handlers.
type PermissionManagementService struct {
	AppConfig                   *config.AppConfig               `inject:"config"`
	PermissionManagementProcess *logic.PermissionManagementImpl `inject:"process.permissionManagement"`
}
