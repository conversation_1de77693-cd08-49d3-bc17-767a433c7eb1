package handlers

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
)

// Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe
func (p *PermissionManagementService) Authenticate(ctx context.Context, req *api.AuthenticateRequest) (*api.AuthenticateResponse, error) {
	res, err := p.PermissionManagementProcess.AuthenticateInternalRequestByElementCode(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}
	return res, nil
}
