package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) CreatePermission(ctx context.Context, req *api.CreatePermissionRequest) (*api.CreatePermissionResponse, error) {
	resp, err := p.PermissionManagementProcess.CreatePermission(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error proceed create permission %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.CreatePermissionResponse{}, err
	}
	return resp, nil
}
