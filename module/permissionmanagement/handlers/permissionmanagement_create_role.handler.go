package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) CreateRole(ctx context.Context, req *api.CreateRoleRequest) (*api.CreateRoleResponse, error) {
	resp, err := p.PermissionManagementProcess.CreateRole(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreateRoleLogTag, fmt.Sprintf("error proceed create role %v", err), utils.GetTraceID(ctx))
		return &api.CreateRoleResponse{}, err
	}
	return resp, nil
}
