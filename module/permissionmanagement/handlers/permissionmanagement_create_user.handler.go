package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) CreateUser(ctx context.Context, req *api.CreateUserRequest) (*api.CreateUserResponse, error) {
	resp, err := p.PermissionManagementProcess.CreateUser(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreateUserLogTag, fmt.Sprintf("error create user %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.CreateUserResponse{}, err
	}
	return resp, nil
}
