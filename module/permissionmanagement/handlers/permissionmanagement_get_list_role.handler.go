package handlers

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) GetListRole(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	// check permission
	_, hasPerm, err := logic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.RoleManagement, constants.BitwiseValueGeneralRead)
	if err != nil {
		return &api.GetListRoleResponse{}, err
	}
	if !hasPerm {
		return &api.GetListRoleResponse{}, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	resp, err := p.PermissionManagementProcess.GetListRoles(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetListRoleLogTag, fmt.Sprintf("error proceed get list role %v", err), utils.GetTraceID(ctx))
		return &api.GetListRoleResponse{}, err
	}
	return resp, nil
}
