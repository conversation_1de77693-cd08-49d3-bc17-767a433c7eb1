package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetPermissionList is API for get permission list in admin config
func (p *PermissionManagementService) GetPermissionList(ctx context.Context, req *api.GetPermissionListRequest) (*api.GetPermissionListResponse, error) {
	resp, err := p.PermissionManagementProcess.GetPermissionList(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetPermissionListLogTag, fmt.Sprintf("error proceed get permissions %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.GetPermissionListResponse{}, err
	}
	return resp, nil
}
