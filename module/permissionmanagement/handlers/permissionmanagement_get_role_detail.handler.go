package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetRoleDetail is API for get role details by id
func (p *PermissionManagementService) GetRoleDetail(ctx context.Context, req *api.GetRoleDetailRequest) (*api.GetRoleDetailResponse, error) {
	resp, err := p.PermissionManagementProcess.GetRoleDetail(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetRoleDetailLogTag, fmt.Sprintf("error get role detail with err: %v", err), utils.GetTraceID(ctx))
		return &api.GetRoleDetailResponse{}, err
	}
	return resp, nil
}
