package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetUserPermissions is API to get user detail and permission
func (p *PermissionManagementService) GetUserPermissions(ctx context.Context) (*api.GetUserPermissionsResponse, error) {
	resp, err := p.PermissionManagementProcess.GetUserPermissions(ctx)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetUserPermissionsLogTag, fmt.Sprintf("error proceed get user permissions: %v", err.Error()), utils.GetTraceID(ctx))
		return &api.GetUserPermissionsResponse{}, err
	}

	return resp, nil
}
