package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) GetUsers(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	resp, err := p.PermissionManagementProcess.GetUserList(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetUsersLogTag, fmt.Sprintf("error proceed get users %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.GetUsersResponse{}, err
	}
	return resp, nil
}
