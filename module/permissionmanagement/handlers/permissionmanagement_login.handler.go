package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// Login logs in a user
func (p *PermissionManagementService) Login(ctx context.Context, req *api.LoginRequest) (*api.LoginResponse, error) {
	resp, err := p.PermissionManagementProcess.Login(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.LoginLogTag, fmt.Sprintf("error login %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.LoginResponse{}, err
	}
	return resp, nil
}
