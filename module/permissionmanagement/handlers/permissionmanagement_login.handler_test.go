package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

func TestPermissionManagementService_Login(t *testing.T) {
	o := &PermissionManagementService{
		AppConfig: &config.AppConfig{
			DefaultAppConfig: servus.DefaultAppConfig{
				Data: &servus.DataConfig{
					MySQL: &data.MysqlConfig{},
				},
			},
		},
	}
	t.Run("missing type", func(t *testing.T) {
		validations.InitValidator("ID")
		res, err := o.Login(context.Background(), &api.LoginRequest{
			Email:    "<EMAIL>",
			Password: "password",
		})
		assert.Nil(t, res)
		assert.Equal(t, servus.ServiceError{
			HTTPCode: error.BadRequest.HTTPStatusCode(),
			Code:     string(error.BadRequest),
			Message:  "failed to validate request Key: 'LoginRequest.Type' Error:Field validation for 'Type' failed on the 'required' tag",
		}, err)
	})
	t.Run("missing email", func(t *testing.T) {
		validations.InitValidator("ID")
		res, err := o.Login(context.Background(), &api.LoginRequest{
			Email:    "",
			Password: "password",
			Type:     api.LoginType_NATIVE,
		})
		assert.Nil(t, res)
		assert.Equal(t, servus.ServiceError{
			HTTPCode: error.BadRequest.HTTPStatusCode(),
			Code:     string(error.BadRequest),
			Message:  "failed to validate request Key: 'LoginRequest.Email' Error:Field validation for 'Email' failed on the 'required' tag",
		}, err)
	})
}
