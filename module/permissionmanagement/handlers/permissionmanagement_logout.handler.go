package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// Logout ...
func (p *PermissionManagementService) Logout(ctx context.Context) (*api.LogoutResponse, error) {
	resp, err := p.PermissionManagementProcess.Logout(ctx)
	if err != nil {
		slog.FromContext(ctx).Error(constants.LogoutLogTag, fmt.Sprintf("error logout %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.LogoutResponse{}, err
	}
	return resp, nil
}
