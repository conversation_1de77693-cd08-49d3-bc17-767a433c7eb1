package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// RefreshLogin will re-login a user
func (p *PermissionManagementService) RefreshLogin(ctx context.Context, req *api.RefreshLoginRequest) (*api.RefreshLoginResponse, error) {
	resp, err := p.PermissionManagementProcess.RefreshLogin(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.RefreshLoginLogTag, fmt.Sprintf("error refresh login %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.RefreshLoginResponse{}, err
	}
	return resp, nil
}
