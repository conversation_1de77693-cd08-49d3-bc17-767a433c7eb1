package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// ResetPassword is used to update the user password
func (p *PermissionManagementService) ResetPassword(ctx context.Context, req *api.ResetPasswordRequest) (*api.ResetPasswordResponse, error) {
	resp, err := p.PermissionManagementProcess.ResetPassword(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.ResetPasswordLogTag, fmt.Sprintf("error reset password %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.ResetPasswordResponse{}, err
	}
	return resp, nil
}
