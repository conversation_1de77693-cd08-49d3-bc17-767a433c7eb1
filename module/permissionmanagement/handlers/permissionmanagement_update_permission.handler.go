package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) UpdatePermission(ctx context.Context, req *api.UpdatePermissionRequest) (*api.UpdatePermissionResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdatePermission(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdatePermissionLogTag, fmt.Sprintf("error proceed update permission %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.UpdatePermissionResponse{}, err
	}
	return resp, nil
}
