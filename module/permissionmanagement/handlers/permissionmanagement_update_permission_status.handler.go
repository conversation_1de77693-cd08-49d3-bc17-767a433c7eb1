package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdatePermissionStatus is API for update permission in admin config
func (p *PermissionManagementService) UpdatePermissionStatus(ctx context.Context, req *api.UpdatePermissionStatusRequest) (*api.UpdatePermissionStatusResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdatePermissionStatus(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdatePermissionLogTag, fmt.Sprintf("error proceed update permission %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.UpdatePermissionStatusResponse{}, err
	}
	return resp, nil
}
