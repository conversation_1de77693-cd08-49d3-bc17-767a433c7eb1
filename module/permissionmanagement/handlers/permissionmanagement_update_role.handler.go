package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *PermissionManagementService) UpdateRole(ctx context.Context, req *api.UpdateRoleRequest) (*api.UpdateRoleResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdateRole(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdateRoleLogTag, fmt.Sprintf("error proceed update role with err: %v", err), utils.GetTraceID(ctx))
		return &api.UpdateRoleResponse{}, err
	}
	return resp, nil
}
