package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateRoleStatus is API for deactivate and reactivate role
func (p *PermissionManagementService) UpdateRoleStatus(ctx context.Context, req *api.UpdateRoleStatusRequest) (*api.UpdateRoleStatusResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdateRoleStatus(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CreateRoleLogTag, fmt.Sprintf("error proceed update status role with err: %v", err), utils.GetTraceID(ctx))
		return &api.UpdateRoleStatusResponse{}, err
	}
	return resp, nil
}
