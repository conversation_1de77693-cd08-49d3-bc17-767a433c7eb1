package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateUser API to update user
func (p *PermissionManagementService) UpdateUser(ctx context.Context, req *api.UpdateUserRequest) (*api.UpdateUserResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdateUser(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdateUserLogTag, fmt.Sprintf("error update user %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.UpdateUserResponse{}, err
	}
	return resp, nil
}
