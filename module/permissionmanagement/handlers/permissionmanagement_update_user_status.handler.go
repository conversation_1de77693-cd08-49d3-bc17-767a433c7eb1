package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateUserStatus API to update user status
func (p *PermissionManagementService) UpdateUserStatus(ctx context.Context, req *api.UpdateUserStatusRequest) (*api.UpdateUserStatusResponse, error) {
	resp, err := p.PermissionManagementProcess.UpdateUserStatus(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdateUserStatusLogTag, fmt.Sprintf("error update user status %v", err), slog.Error(err), utils.GetTraceID(ctx))
		return &api.UpdateUserStatusResponse{}, err
	}
	return resp, nil
}
