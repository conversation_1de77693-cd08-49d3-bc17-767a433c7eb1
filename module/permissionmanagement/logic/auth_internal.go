package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	coreStorage "gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

// AuthenticateInternalRequestByElementCode is used to authenticate internal request by element code
func (p *PermissionManagementImpl) AuthenticateInternalRequestByElementCode(ctx context.Context, req *api.AuthenticateRequest) (*api.AuthenticateResponse, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(req.Token, p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to parse jwt token")
	}
	userIDStr := claims["id"].(string)

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get the user info
	user, err := storage.GetUserByUserID(ctx, db, userIDStr)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user by id")
	}

	// get user bitwise available on element code
	var bitwiseAvailable int64
	perms, err := p.GetUserInfoFromRedis(ctx, userIDStr, constants.AuthenticateInternalLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user info from redis")
	}
	if perms != nil {
		bitwiseAvailable = perms.Permissions[req.ElementCode]
	} else {
		bitwiseAvailable, err = storage.GetUserBitwiseValueForElementCode(ctx, db, user.ID, req.ElementCode)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user bitwise value for element code")
		}
	}
	// get user permissions if bitwise required is not given
	if req.BitwiseRequired > 0 {
		hasPerm, _, err := storage.IsUserHasPermissionForElementCode(ctx, db, user.ID, req.ElementCode, req.BitwiseRequired)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to check user permission for element")
		} else if !hasPerm {
			return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
		}
	} else {
		element, err := coreStorage.GetElementByCode(ctx, db, req.ElementCode)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element by code")
		}
		res, err := storage.GetPermissionsByModuleAndBitwise(ctx, db, bitwiseAvailable, element.ModuleID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get permissions by module and bitwise")
		}

		return &api.AuthenticateResponse{
			UserID:      user.UserID,
			Permissions: makePermissionsListFromDTO(res),
		}, nil
	}

	return &api.AuthenticateResponse{
		UserID: user.UserID,
	}, nil
}

func makePermissionsListFromDTO(permissions []*storage.PermissionDTO) []api.Permission {
	var res []api.Permission
	for _, p := range permissions {
		res = append(res, api.Permission{
			Id:          p.ID,
			Name:        p.Name,
			Description: p.Description,
			CreatedAt:   utils.DateAsString(p.CreatedAt.Time),
			UpdatedAt:   utils.DateAsString(p.UpdatedAt.Time),
			Bitwise:     p.BitwiseValue,
			ModuleID:    p.ModuleID,
			Status:      int64(p.Status),
		})
	}
	return res
}
