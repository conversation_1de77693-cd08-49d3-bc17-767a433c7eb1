package logic

import (
	"context"
	"encoding/json"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

// ResetPermissionByUserID ...
func (p *PermissionManagementImpl) ResetPermissionByUserID(ctx context.Context, userID string, logTag string) (*storage.UserPermissionsDetail, error) {
	var user *storage.UserPermissionsDetail

	// get by redis
	err := redis.DeleteRedisKey(ctx, constants.UserIDRedisKey+userID, constants.UpdateStatusRoleLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to expiry the user")
	}

	// Get the user by user id
	user, err = p.GetUserPermissionFromDB(ctx, userID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user permission")
	}

	//Set user to redis
	err = redis.SetRedisValue(ctx, constants.UserIDRedisKey+user.UserID, user, 43200, logTag)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// AuthenticateRequestByElementCode ...
func (p *PermissionManagementImpl) AuthenticateRequestByElementCode(ctx context.Context, code constants.ElementCodes, bitwiseValueRequired int64) (*storage.UserDTO, bool, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, false, err
	}

	userID := claims["id"].(string)
	// Get user info from redis
	redisUser, err := p.GetUserInfoFromRedis(ctx, userID, "logic.authenticateRequestByElementCode")
	if err != nil {
		return nil, false, err
	}
	if redisUser != nil {
		tmpUser := &storage.UserDTO{
			ID:     redisUser.ID,
			UserID: redisUser.UserID,
			Name:   redisUser.Name,
			Email:  redisUser.Email,
		}

		if value, ok := redisUser.Permissions[string(code)]; ok {
			return tmpUser, value&bitwiseValueRequired == bitwiseValueRequired, nil
		}
		return nil, false, nil
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, false, err
	}

	// Get the user by id on claim
	user, err := storage.GetUserByUserID(ctx, db, userID)
	if err != nil {
		return nil, false, errorwrapper.WrapError(err, apiError.Idem, "failed to get user by id")
	}

	hasPerm, _, err := storage.IsUserHasPermissionForElementCode(ctx, db, user.ID, string(code), bitwiseValueRequired)
	if err != nil {
		return nil, false, errorwrapper.WrapError(err, apiError.Idem, "failed to check user permission for element")
	} else if !hasPerm {
		return nil, false, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	return user, true, nil
}

// GetUserInfoFromRedis ...
func (p *PermissionManagementImpl) GetUserInfoFromRedis(ctx context.Context, userID string, logTag string) (*storage.UserPermissionsDetail, error) {
	var user *storage.UserPermissionsDetail
	//get from redis
	userRedis, err := redis.GetRedisValue(ctx, constants.UserIDRedisKey+userID, logTag)
	if err != nil {
		return nil, err
	}

	if userRedis != "" {
		unmarshalErr := json.Unmarshal([]byte(userRedis), &user)
		if unmarshalErr != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error unmarshaling redis value for user %s with error: %s", userID, unmarshalErr.Error()), utils.GetTraceID(ctx))
		} else {
			return user, nil
		}
	}

	return nil, nil
}

func (p *PermissionManagementImpl) AuthenticateRequest(ctx context.Context) (*storage.UserDTO, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, err
	}

	userID := claims["id"].(string)
	// Get user info from redis
	redisUser, err := p.GetUserInfoFromRedis(ctx, userID, "logic.AuthenticateRequest")
	if err != nil {
		return nil, err
	}
	if redisUser != nil {
		return &storage.UserDTO{
			ID:     redisUser.ID,
			UserID: redisUser.UserID,
			Name:   redisUser.Name,
			Email:  redisUser.Email,
		}, nil
	}
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// Get the user by id on claim
	user, err := storage.GetUserByUserID(ctx, db, userID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user by id")
	}

	return user, nil
}

func (p *PermissionManagementImpl) AuthenticateRequestForElement(ctx context.Context, elementID int64, bitwiseValueRequired int64) (*storage.UserDTO, int64, error) {
	user, err := p.AuthenticateRequest(ctx)
	if err != nil {
		return nil, 0, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, 0, err
	}
	// Validate whether user is authorized to element
	hasPerm, bitwiseValue, err := storage.IsUserHasPermissionForElementID(ctx, db, user.ID, elementID, bitwiseValueRequired)
	if err != nil {
		return nil, 0, errorwrapper.WrapError(err, apiError.Idem, "failed to check user permission for element")
	} else if !hasPerm {
		return nil, bitwiseValue, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	return user, bitwiseValue, nil
}
