package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// CreatePermission ...
//
// nolint: dupl
func (p *PermissionManagementImpl) CreatePermission(ctx context.Context, req *api.CreatePermissionRequest) (*api.CreatePermissionResponse, error) {
	// check permission
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	perm, err := createNewPermission(ctx, req, db, user.ID)
	if err != nil {
		return nil, err
	}

	auditTrailReq, auditErr := helper.WriteLogAuditTrailPermission(ctx, perm, "Create Permission", user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.CreatePermissionResponse{
		Id:     perm.ID,
		Status: "Success",
	}, nil
}

func createNewPermission(ctx context.Context, req *api.CreatePermissionRequest, db *sql.DB, creatorID int64) (*storage.PermissionDTO, error) {
	newPerm := &storage.PermissionDTO{
		Name:         req.Name,
		Description:  req.Description,
		Status:       req.Status,
		BitwiseValue: req.Bitwise,
		ModuleID:     req.ModuleID,
		CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:    sql.NullInt64{Int64: creatorID, Valid: true},
		UpdatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy:    sql.NullInt64{Int64: creatorID, Valid: true},
	}

	err := validateNewDuplicatePermission(ctx, newPerm, db)
	if err != nil {
		return nil, err
	}

	id, err := storage.CreatePermission(ctx, db, newPerm)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create permission")
	}

	newPerm.ID = id
	return newPerm, nil
}

func validateNewDuplicatePermission(ctx context.Context, perm *storage.PermissionDTO, db *sql.DB) error {
	count, err := storage.GetCountPermissionForDuplicateChecking(ctx, db, perm)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to validate permission")
	}

	if count > 0 {
		return errorwrapper.Error(apiError.BadRequest, "failed to create permission due to duplicate name or bitwise value in the same module")
	}
	return nil
}
