package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// CreateRole for create a new role
//
// nolint: funlen,errcheck
func (p *PermissionManagementImpl) CreateRole(ctx context.Context, req *api.CreateRoleRequest) (*api.CreateRoleResponse, error) {
	// check permission
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.RoleManagement, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	roleDTO := &storage.RoleDTO{
		Name:      req.Name,
		Status:    req.Status,
		CreatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy: sql.NullInt64{Int64: user.ID, Valid: true},
		UpdatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy: sql.NullInt64{Int64: user.ID, Valid: true},
	}

	permissionDTO, err := makeRoleElementPermissionDTO(ctx, db, req.ElementPermissions, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get permission")
	}

	// create the role
	roleID, err := storage.CreateRole(ctx, db, roleDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create role")
	}
	roleDTO.ID = roleID

	// create role element permission
	if len(permissionDTO) > 0 {
		for _, perm := range permissionDTO {
			perm.RoleID = roleID
			_, errRow := storage.CreatePermissionMatrix(ctx, db, perm)
			if errRow != nil {
				return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to write permission")
			}
		}
	}

	// write audit trail
	title := "Create Role"
	description := fmt.Sprintf("%v for ID %v : %v executed successfully", title, roleDTO.ID, roleDTO.Name)
	auditTrailReq, auditErr := helper.WriteLogAuditTrailRole(ctx, roleDTO, title, description, user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.CreateRoleResponse{
		Id:        roleDTO.ID,
		Name:      roleDTO.Name,
		Status:    roleDTO.Status,
		CreatedAt: roleDTO.CreatedAt.Time.Local().String(),
		CreatedBy: user.Name,
	}, nil
}

func makeRoleElementPermissionDTO(ctx context.Context, db *sql.DB, permissions []api.ElementPermissionsRequest, userID int64) ([]*storage.RoleElementPermissionDTO, error) {
	permDTO := make([]*storage.RoleElementPermissionDTO, 0)

	if len(permissions) > 0 {
		for _, perm := range permissions {
			if len(perm.PermissionsIDs) > 0 {
				curPermIds := utils.ConvertArrayIntToAny(perm.PermissionsIDs)
				curFilter := []commonStorage.QueryCondition{
					commonStorage.In("id", curPermIds...)}

				bitwiseValue, err := storage.GetTotalBitwisePermission(ctx, db, curFilter)

				if err != nil {
					return nil, err
				}

				if bitwiseValue == 0 {
					continue
				}

				permission := &storage.RoleElementPermissionDTO{
					ElementID:    perm.ElementID,
					BitwiseValue: bitwiseValue,
					CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
					CreatedBy:    sql.NullInt64{Int64: userID, Valid: true},
				}
				permDTO = append(permDTO, permission)
			}
		}
	}

	return permDTO, nil
}
