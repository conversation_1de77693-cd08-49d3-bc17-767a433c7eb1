package logic

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/secret"
)

// CreateUser ...
func (p *PermissionManagementImpl) CreateUser(ctx context.Context, req *api.CreateUserRequest) (*api.CreateUserResponse, error) {
	// check permission
	creator, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.UserManagement, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}
	// Validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Insert user
	user, err := insertUser(ctx, req, db, creator.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create user")
	}

	// Insert role
	err = insertUserRole(ctx, req, db, creator.ID, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create role")
	}

	// Write audit trail
	title := "Create User"
	err = helper.WriteLogAuditTrailUser(ctx, user, title, title+" executed successfully", "USER", creator.ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogoutLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	// Return the created user
	return &api.CreateUserResponse{
		Status: constants.SuccessStatus,
	}, nil
}

func insertUser(ctx context.Context, req *api.CreateUserRequest, db *sql.DB, creatorID int64) (*storage.UserDTO, error) {
	// check email
	existingUser, err := storage.GetUserByEmail(ctx, db, req.Email)
	if err != nil && !errorwrapper.IsErrorCodeExist(err, apiError.ResourceNotFound) {
		return nil, err
	}
	if existingUser != nil {
		return nil, errorwrapper.Error(apiError.FieldInvalid, "email has been used for another user")
	}

	password := ""
	if req.Password != "" {
		password, err = secret.GeneratePassword(req.Password)
		if err != nil {
			return nil, err
		}
	}

	// Create the user
	user := &storage.UserDTO{
		Name:     req.Name,
		Email:    req.Email,
		Password: password,
		RefreshToken: sql.NullString{
			String: "", Valid: true,
		},
		Status:    req.Status,
		UserID:    uuid.New().String(),
		CreatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy: sql.NullInt64{Int64: creatorID, Valid: true},
		UpdatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy: sql.NullInt64{Int64: creatorID, Valid: true},
	}

	userID, err := storage.CreateUser(ctx, db, user)
	if err != nil {
		return nil, err
	}

	user.ID = userID

	return user, err
}

func insertUserRole(ctx context.Context, req *api.CreateUserRequest, db *sql.DB, creatorID, id int64) error {
	var err error

	for _, role := range req.RoleIDs {
		// Create the user role
		userRoleDTO := &storage.UserRoleDTO{
			RoleID: role,
			UserID: id,
			CreatedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			},
			CreatedBy: sql.NullInt64{
				Int64: creatorID,
				Valid: true,
			},
		}
		_, err = storage.CreateUserRole(ctx, db, userRoleDTO)
		if err != nil {
			return err
		}
	}

	return err
}
