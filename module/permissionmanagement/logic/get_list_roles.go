package logic

import (
	"context"
	"database/sql"
	"fmt"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// GetListRoles ...
func (p *PermissionManagementImpl) GetListRoles(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	curReq := setDefaultGetListRequest(req)

	// check validity of request
	ok, err := validations.IsValid(curReq)
	if !ok {
		if err != nil {
			return &api.GetListRoleResponse{}, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return &api.GetListRoleResponse{}, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	//get count
	count, err := GetCountRoles(ctx, curReq, db)
	if err != nil {
		return &api.GetListRoleResponse{}, err
	}

	// check if offset more than count
	if curReq.Offset > count {
		return &api.GetListRoleResponse{}, errorwrapper.Error(apiError.BadRequest, "offset is more than total data")
	}

	//get the data
	data, err := GetListRolesData(ctx, curReq, db)
	if err != nil {
		return &api.GetListRoleResponse{}, err
	}

	return &api.GetListRoleResponse{
		Count:  count,
		Offset: curReq.Offset,
		Data:   data,
	}, nil
}

func setDefaultGetListRequest(req *api.GetListRoleRequest) *api.GetListRoleRequest {
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}

	return req
}

// GetListRolesData ...
func GetListRolesData(ctx context.Context, req *api.GetListRoleRequest, db *sql.DB) ([]api.Role, error) {
	filters := constructGetDataDBFilters(req)
	data, err := storage.GetRoleList(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetListRoleLogTag, "error fetching list data from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	finalData := make([]api.Role, 0)
	if len(data) == 0 {
		return finalData, nil
	}

	for _, role := range data {
		finalData = append(finalData, api.Role{
			Id:        role.ID,
			Name:      role.Name,
			Status:    role.Status,
			CreatedAt: role.CreatedAt.Time.String(),
			UpdatedAt: role.UpdatedAt.Time.String(),
			CreatedBy: role.CreatedBy.String,
			UpdatedBy: role.UpdatedBy.String})
	}
	return finalData, nil
}

// GetCountRoles ...
func GetCountRoles(ctx context.Context, req *api.GetListRoleRequest, db *sql.DB) (int64, error) {
	filters := constructGetCountDBFilters(req)
	count, err := storage.GetCountRoles(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetListRoleLogTag, "error fetching count data from DB", slog.Error(err))
		return 0, errorwrapper.Error(apiError.InternalServerError, "failed to fetch count data")
	}
	return count, nil
}

func constructGetCountDBFilters(req *api.GetListRoleRequest) []commonStorage.QueryCondition {
	var filters []commonStorage.QueryCondition

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.Filter != nil && len(filters) > 0 {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}

			filters = append(filters, commonStorage.In(v.Column, v.Value...))
		}
	}
	return filters
}

func constructGetDataDBFilters(req *api.GetListRoleRequest) []commonStorage.QueryCondition {
	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit)),
		commonStorage.Offset(int(req.Offset))}
	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.Filter != nil && len(filters) > 0 {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}

			filters = append(filters, commonStorage.In("r."+v.Column, v.Value...))
		}
	}

	filters = append(filters, buildSortOrderFilter(req.SortBy))

	return filters
}

func buildSortOrderFilter(sortBy *api.Sort) commonStorage.QueryCondition {
	if sortBy != nil && sortBy.Column != "" {
		if sortBy.Sort == api.SortOrder_DESC {
			return commonStorage.DescendingOrder(fmt.Sprintf("r.%s", sortBy.Column))
		}
		return commonStorage.AscendingOrder(fmt.Sprintf("r.%s", sortBy.Column))
	}
	return commonStorage.AscendingOrder("r.id")
}
