package logic

import (
	"context"
	"database/sql"
	"fmt"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// GetPermissionList ...
func (p *PermissionManagementImpl) GetPermissionList(ctx context.Context, req *api.GetPermissionListRequest) (*api.GetPermissionListResponse, error) {
	// check permission
	_, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralRead)
	if err != nil {
		return &api.GetPermissionListResponse{}, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return &api.GetPermissionListResponse{}, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	curReq := setDefaultGetPermissionListRequest(req)

	// check validity of request
	ok, err := validations.IsValid(curReq)
	if !ok {
		if err != nil {
			return &api.GetPermissionListResponse{}, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return &api.GetPermissionListResponse{}, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// get count
	count, err := getPermissionListCount(ctx, curReq, db)
	if err != nil {
		return &api.GetPermissionListResponse{}, err
	}

	// check if offset more than count
	if curReq.Offset > count {
		return &api.GetPermissionListResponse{}, errorwrapper.Error(apiError.BadRequest, "offset is more than total data")
	}

	// get the data
	data, err := getPermissionListData(ctx, curReq, db)
	if err != nil {
		return &api.GetPermissionListResponse{}, err
	}

	return &api.GetPermissionListResponse{
		Count:  count,
		Offset: curReq.Offset,
		Data:   data,
	}, nil
}

func getPermissionListCount(ctx context.Context, req *api.GetPermissionListRequest, db *sql.DB) (int64, error) {
	filters := constructGetPermissionListCountFilter(req)

	count, err := storage.GetCountPermissions(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetPermissionListLogTag, "error fetching count data from DB", slog.Error(err))
		return 0, errorwrapper.Error(apiError.InternalServerError, "failed to fetch count data")
	}
	return count, nil
}

func getPermissionListData(ctx context.Context, req *api.GetPermissionListRequest, db *sql.DB) ([]api.Permission, error) {
	filters := constructGetPermissionListDataFilter(req)
	data, err := storage.GetPermissionList(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetPermissionListLogTag, "error fetching list data from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	finalData := make([]api.Permission, 0)
	if len(data) == 0 {
		return finalData, nil
	}

	for _, perm := range data {
		finalData = append(finalData, api.Permission{
			Id:          perm.ID,
			Name:        perm.Name,
			Description: perm.Description,
			Status:      perm.Status,
			CreatedAt:   utils.DateAsString(perm.CreatedAt.Time),
			UpdatedAt:   utils.DateAsString(perm.UpdatedAt.Time),
			CreatedBy:   perm.CreatedBy.String,
			UpdatedBy:   perm.UpdatedBy.String,
			ModuleID:    perm.ModuleID,
			ModuleName:  perm.ModuleName,
			Bitwise:     perm.BitwiseValue,
		})
	}
	return finalData, nil
}

func setDefaultGetPermissionListRequest(req *api.GetPermissionListRequest) *api.GetPermissionListRequest {
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}

	return req
}

func constructGetPermissionListCountFilter(req *api.GetPermissionListRequest) []commonStorage.QueryCondition {
	var filters []commonStorage.QueryCondition

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("p.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
	}

	if len(req.Filter) > 0 {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}

			filters = append(filters, commonStorage.In("p."+v.Column, v.Value...))
		}
	}

	return filters
}

func constructGetPermissionListDataFilter(req *api.GetPermissionListRequest) []commonStorage.QueryCondition {
	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit)),
		commonStorage.Offset(int(req.Offset))}

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("p.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
	}

	if len(req.Filter) > 0 {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}

			filters = append(filters, commonStorage.In("p."+v.Column, v.Value...))
		}
	}

	filters = append(filters, buildSortOrderGetPermissionList(req.SortBy))

	return filters
}

func buildSortOrderGetPermissionList(sortBy *api.Sort) commonStorage.QueryCondition {
	if sortBy != nil && sortBy.Column != "" {
		if sortBy.Sort == api.SortOrder_DESC {
			return commonStorage.DescendingOrder(fmt.Sprintf("p.%s", sortBy.Column))
		}
		return commonStorage.AscendingOrder(fmt.Sprintf("p.%s", sortBy.Column))
	}
	return commonStorage.AscendingOrder("p.id")
}
