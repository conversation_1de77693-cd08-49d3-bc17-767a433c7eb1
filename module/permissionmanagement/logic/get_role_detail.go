package logic

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetRoleDetail ...
func (p *PermissionManagementImpl) GetRoleDetail(ctx context.Context, req *api.GetRoleDetailRequest) (*api.GetRoleDetailResponse, error) {
	// check permission
	_, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.RoleManagement, constants.BitwiseValueReadTicket)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	roleDTO, err := permissionManagementStorage.GetRoleInfoByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get role info")
	}

	mapDTO, err := storage.GetModuleElementsByRole(ctx, db, roleDTO.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get modules info")
	}

	modules, err := structGetRoleModuleResponse(ctx, db, mapDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get permissions info")
	}

	return &api.GetRoleDetailResponse{
		Id:        roleDTO.ID,
		Name:      roleDTO.Name,
		Status:    roleDTO.Status,
		Modules:   modules,
		CreatedAt: roleDTO.CreatedAt.Time.Local().String(),
		CreatedBy: roleDTO.CreatedBy.String,
		UpdatedAt: roleDTO.UpdatedAt.Time.Local().String(),
		UpdatedBy: roleDTO.UpdatedBy.String,
	}, nil
}

func structGetRoleModuleResponse(ctx context.Context, db *sql.DB, mapDTO []*storage.ModuleElementPermissionsDTO) ([]api.RoleDetailModule, error) {
	moduleInfoMap := make(map[int64]api.RoleDetailModule)

	if len(mapDTO) > 0 {
		for _, row := range mapDTO {
			if value, ok := moduleInfoMap[row.ModuleID]; ok {
				curModule := value

				// add new element
				element, err := structGetRoleElementPermissionResponse(ctx, db, row)
				if err != nil {
					return nil, err
				}
				curModule.Elements = append(curModule.Elements, element)
				moduleInfoMap[row.ModuleID] = curModule
			} else {
				// get element
				elements := make([]api.RoleDetailElement, 0)
				element, err := structGetRoleElementPermissionResponse(ctx, db, row)
				if err != nil {
					return nil, err
				}
				elements = append(elements, element)

				// set new module
				module := api.RoleDetailModule{
					ModuleID: row.ModuleID,
					Name:     row.ModuleName,
					Elements: elements,
				}

				moduleInfoMap[row.ModuleID] = module
			}
		}
	}

	resp := make([]api.RoleDetailModule, 0)
	if len(moduleInfoMap) > 0 {
		for mod := range moduleInfoMap {
			resp = append(resp, moduleInfoMap[mod])
		}
	}
	return resp, nil
}

func structGetRoleElementPermissionResponse(ctx context.Context, db *sql.DB, dto *storage.ModuleElementPermissionsDTO) (api.RoleDetailElement, error) {
	// get permission
	permissions, err := permissionManagementStorage.GetPermissionsByModuleAndBitwise(ctx, db, dto.BitwiseValue, dto.ModuleID)
	if err != nil {
		return api.RoleDetailElement{}, err
	}

	permResp := make([]api.RoleDetailPermission, 0)
	if len(permissions) > 0 {
		for _, perm := range permissions {
			curPerm := api.RoleDetailPermission{
				PermissionID: perm.ID,
				Name:         perm.Name,
			}
			permResp = append(permResp, curPerm)
		}
	}

	return api.RoleDetailElement{
		ElementID:   dto.ElementID,
		Name:        dto.ElementName,
		Permissions: permResp,
	}, nil
}
