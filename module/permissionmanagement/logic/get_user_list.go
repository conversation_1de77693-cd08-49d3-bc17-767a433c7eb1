package logic

import (
	"context"
	"database/sql"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// GetUserList gets ticket list
func (p *PermissionManagementImpl) GetUserList(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	// check permission
	_, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.UserManagement, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}
	// Validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}

	// Get the database slave handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	//get count
	count, err := GetCountUsers(ctx, req, db)
	if err != nil {
		return &api.GetUsersResponse{}, err
	}

	// check if offset more than count
	if req.Offset > count {
		return &api.GetUsersResponse{}, errorwrapper.Error(apiError.BadRequest, "offset is more than total data")
	}

	// Get user list
	data, err := GetUsersData(ctx, req, db)
	if err != nil {
		return &api.GetUsersResponse{}, err
	}

	// Return the ticket list
	return &api.GetUsersResponse{
		Data:   data,
		Count:  count,
		Offset: req.Offset,
	}, nil
}

// GetUsersData ...
func GetUsersData(ctx context.Context, req *api.GetUsersRequest, db *sql.DB) ([]api.User, error) {
	filters, roleIDs := constructGetUsersDBFilters(req)
	data, err := permissionManagementStorage.GetUserList(ctx, db, filters, roleIDs)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetUsersLogTag, "error fetching list data from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	finalData := make([]api.User, 0)
	if data == nil {
		return finalData, nil
	}

	for _, user := range data {
		rolesData, err := permissionManagementStorage.GetUserRole(ctx, db, user.ID)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetUsersLogTag, "error fetching user role data from DB", slog.Error(err))
		}
		var roles []string
		if len(rolesData) > 0 {
			for _, role := range rolesData {
				roles = append(roles, role.Name)
			}
		}
		finalData = append(finalData, api.User{
			Id:        user.ID,
			Name:      user.Name,
			Status:    user.Status,
			Email:     user.Email,
			CreatedAt: user.CreatedAt.Time.String(),
			UpdatedAt: user.UpdatedAt.Time.String(),
			CreatedBy: user.CreatedBy.String,
			UpdatedBy: user.UpdatedBy.String,
			Roles:     roles,
			UserID:    user.UserID,
		})
	}
	return finalData, nil
}

// constructGetUsersDBFilters
func constructGetUsersDBFilters(req *api.GetUsersRequest) ([]commonStorage.QueryCondition, []string) {
	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit)),
		commonStorage.Offset(int(req.Offset))}
	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
		filters = append(filters, commonStorage.Like("r.email", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
	}

	var roleID []interface{}
	if req.Filter != nil {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}
			if v.Column == "role_id" {
				roleID = v.Value
				continue
			}
			filters = append(filters, commonStorage.In("r."+v.Column, v.Value...))
		}
	}

	sortValue := ""
	if req.SortBy != nil && req.SortBy.Sort != "" {
		sortValue = string(req.SortBy.Sort)
	}

	column := ""
	if req.SortBy != nil && req.SortBy.Column != "" {
		column = req.SortBy.Column
	}

	// append sorting
	filters = append(filters, commonStorage.BuildSortOrder(column, sortValue, "r"))

	roles := make([]string, len(roleID))
	for i, s := range roleID {
		roles[i] = s.(string)
	}

	return filters, roles
}

// constructCountGetUsersDBFilters
func constructCountGetUsersDBFilters(req *api.GetUsersRequest) ([]commonStorage.QueryCondition, []string) {
	var filters []commonStorage.QueryCondition
	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
		filters = append(filters, commonStorage.Like("r.email", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeOR))
	}

	var roleID []interface{}
	if req.Filter != nil {
		for _, v := range req.Filter {
			if v.Column == "" || v.Value == nil || len(v.Value) == 0 {
				continue
			}
			if v.Column == "role_id" {
				roleID = v.Value
				continue
			}
			filters = append(filters, commonStorage.In("r."+v.Column, v.Value...))
		}
	}

	roles := make([]string, len(roleID))
	for i, s := range roleID {
		roles[i] = s.(string)
	}

	return filters, roles
}

// GetCountUsers ...
func GetCountUsers(ctx context.Context, req *api.GetUsersRequest, db *sql.DB) (int64, error) {
	filters, roles := constructCountGetUsersDBFilters(req)
	count, err := permissionManagementStorage.GetCountUsers(ctx, db, filters, roles)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetUsersLogTag, "error fetching count data from DB", slog.Error(err))
		return 0, errorwrapper.Error(apiError.InternalServerError, "failed to fetch count data")
	}
	return count, nil
}
