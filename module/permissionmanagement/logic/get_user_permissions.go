package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

// GetUserPermissions ...
func (p *PermissionManagementImpl) GetUserPermissions(ctx context.Context) (*api.GetUserPermissionsResponse, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, err
	}

	userID := claims["id"].(string)
	userPermission, err := p.GetUserPermissionByUserID(ctx, userID, constants.GetUserPermissionsLogTag)
	if err != nil {
		return nil, err
	}

	resp := convertUserPermissionDetailToResponse(userPermission)
	return resp, nil
}

// GetUserPermissionByUserID ...
func (p *PermissionManagementImpl) GetUserPermissionByUserID(ctx context.Context, userID string, logTag string) (*storage.UserPermissionsDetail, error) {
	var user *storage.UserPermissionsDetail

	// get by redis
	user, err := p.GetUserInfoFromRedis(ctx, userID, logTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "Failed to get info from redis")
	}
	if user != nil {
		return user, nil
	}

	// Get the user by user id
	user, err = p.GetUserPermissionFromDB(ctx, userID)
	if err != nil {
		return nil, err
	}

	//Set user to redis
	err = redis.SetRedisValue(ctx, constants.UserIDRedisKey+user.UserID, user, 43200, logTag)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// GetUserPermissionFromDB ...
func (p *PermissionManagementImpl) GetUserPermissionFromDB(ctx context.Context, userID string) (*storage.UserPermissionsDetail, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "Failed to get database handle")
	}

	user, err := storage.GetUserByUserID(ctx, db, userID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user info")
	}

	// Get roles, module, permission
	elemPermissions, err := storage.GetListElementPermission(ctx, db, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user permission")
	}

	modules, roles, permissions := convertElementPermToUserPermissionDetail(elemPermissions)
	return &storage.UserPermissionsDetail{
		ID:          user.ID,
		UserID:      user.UserID,
		Name:        user.Name,
		Email:       user.Email,
		Modules:     modules,
		Roles:       roles,
		Permissions: permissions,
	}, nil
}

func convertElementPermToUserPermissionDetail(elem []*storage.RoleElementPermissionListDTO) ([]string, []string, map[string]int64) {
	permissions := make(map[string]int64)
	roles := make([]string, 0)
	modules := make([]string, 0)

	if len(elem) != 0 {
		tmpModules := make(map[string]interface{})
		tmpRoles := make(map[string]interface{})

		for _, matrix := range elem {
			tmpRoles[matrix.RoleName] = struct{}{}
			if matrix.ElementCode.String != "" {
				// if existing, doing BIT_OR
				if bitwise, ok := permissions[matrix.ElementCode.String]; ok {
					combinedBitwise := bitwise | matrix.BitwiseValue.Int64
					permissions[matrix.ElementCode.String] = combinedBitwise
				} else {
					permissions[matrix.ElementCode.String] = matrix.BitwiseValue.Int64
				}
				tmpModules[matrix.ModuleName.String] = struct{}{}
			}
		}

		for mod := range tmpModules {
			modules = append(modules, mod)
		}

		for role := range tmpRoles {
			roles = append(roles, role)
		}
	}

	return modules, roles, permissions
}

func convertUserPermissionDetailToResponse(user *storage.UserPermissionsDetail) *api.GetUserPermissionsResponse {
	permissions := make([]api.UserPermission, 0)

	if len(user.Permissions) != 0 {
		for perm := range user.Permissions {
			permission := api.UserPermission{
				ElementCode: perm,
				Bitwise:     user.Permissions[perm],
			}
			permissions = append(permissions, permission)
		}
	}

	return &api.GetUserPermissionsResponse{
		UserId:      user.UserID,
		Name:        user.Name,
		Email:       user.Email,
		Modules:     user.Modules,
		Roles:       user.Roles,
		Permissions: permissions,
	}
}
