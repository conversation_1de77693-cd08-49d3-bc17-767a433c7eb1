package helper

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

func WriteLogAuditTrailUser(ctx context.Context, user *storage.UserDTO, title string, description string, activityType string, createdBy int64) error {
	// Get IP Address and User Agent
	httpRequest := servus.HTTPRequestFromCtx(ctx)
	if httpRequest == nil {
		return errors.New("missing request in context")
	}

	// Get and normalize IP
	rawIP := httpRequest.RemoteAddr
	ip := normalizeIP(rawIP) // Converts to "127.0.0.1" if IPv6 loopback

	userAgent := httpRequest.UserAgent()

	// Create proper JSON structure
	extraParams := &auditTrailStorage.DataJSON{
		"IP":         ip,
		"User-Agent": userAgent,
	}

	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     user.UserID,
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: createdBy,
			Valid: true,
		},
		Title:        title,
		Description:  description,
		ActivityType: activityType,
		ExtraParams:  extraParams,
	}
	_, err := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return err
}

func WriteLogAuditTrailPermission(ctx context.Context, perm *storage.PermissionDTO, title string, creatorID int64) (*auditTrailStorage.AuditTrailsDTO, error) {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     strconv.FormatInt(perm.ID, 10),
		IdentifierType: constants.PermissionID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: creatorID,
			Valid: true,
		},
		Title:        title,
		Description:  fmt.Sprintf("%v for ID %v : %v executed successfully", title, perm.ID, perm.Name),
		ActivityType: constants.Permission,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return auditTrailsDTO, auditErr
}

func WriteLogAuditTrailRole(ctx context.Context, role *storage.RoleDTO, title string, description string, creatorID int64) (*auditTrailStorage.AuditTrailsDTO, error) {
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     strconv.FormatInt(role.ID, 10),
		IdentifierType: constants.RoleID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: creatorID,
			Valid: true,
		},
		Title:        title,
		Description:  description,
		ActivityType: constants.Role,
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	return auditTrailsDTO, auditErr
}

// Improved IP normalization
func normalizeIP(rawIP string) string {
	// First remove port if present
	ip, _, err := net.SplitHostPort(rawIP)
	if err != nil {
		// If not in IP:port format, use as-is
		ip = rawIP
	}

	// Remove brackets for IPv6 (e.g., "[::1]" → "::1")
	ip = strings.Trim(ip, "[]")

	// Parse the IP
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return ip // Return original if invalid
	}

	// Convert IPv6 loopback to IPv4
	if parsedIP.IsLoopback() {
		return "127.0.0.1"
	}

	return parsedIP.String()
}
