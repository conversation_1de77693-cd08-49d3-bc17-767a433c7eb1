package logic

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// IPermissionManagement exposes methods for cross module
//
//go:generate mockery --name=IPermissionManagement --output=mock_i_permission_management --inpackage
type IPermissionManagement interface {
	AuthenticateRequestByElementCode(ctx context.Context, code constants.ElementCodes, bitwiseValueRequired int64) (*storage.UserDTO, bool, error)
	GetUserInfoFromRedis(ctx context.Context, userID string, logTag string) (*storage.UserPermissionsDetail, error)
	AuthenticateRequest(ctx context.Context) (*storage.UserDTO, error)
	AuthenticateRequestForElement(ctx context.Context, elementID int64, bitwiseValueRequired int64) (*storage.UserDTO, int64, error)
	AuthenticateInternalRequestByElementCode(ctx context.Context, req *api.AuthenticateRequest) (*api.AuthenticateResponse, error)
}
