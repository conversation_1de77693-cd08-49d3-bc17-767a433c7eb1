package logic

import (
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

type PermissionManagementImpl struct {
	AppConfig *config.AppConfig `inject:"config"`
}

var (
	// Process is the interactor for all logic
	PermissionManagementProcess IPermissionManagement
)

// InitLogic ...
func InitLogic(app *servus.Application, conf *config.AppConfig) {
	PermissionManagementProcess = NewPermissionManagementProcess(conf)
	app.MustRegister("process.permissionManagement", PermissionManagementProcess)
}

// NewPermissionManagementProcess ...
func NewPermissionManagementProcess(cfg *config.AppConfig) *PermissionManagementImpl {
	return &PermissionManagementImpl{AppConfig: cfg}
}
