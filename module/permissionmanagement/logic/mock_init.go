package logic

import (
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

var (
	// MockProcess ...
	MockPermissionManagementProcess = &PermissionManagementImpl{}
)

// MockInitLogic ...
func MockInitLogic(mockConfig *config.AppConfig) {
	//mockRepository := &storage.PermissionManagementRepositoryImpl{AppConfig: mockConfig}
	MockPermissionManagementProcess.AppConfig = mockConfig
	//MockPermissionManagementProcess.Repository = mockRepository
	PermissionManagementProcess = MockPermissionManagementProcess
}
