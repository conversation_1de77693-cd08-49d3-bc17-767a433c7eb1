package logic

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// RefreshLogin ...
func (p *PermissionManagementImpl) RefreshLogin(ctx context.Context, req *api.RefreshLoginRequest) (*api.RefreshLoginResponse, error) {
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	// Check refresh token
	claims, err := jwt.ParseJWTStringWithClaims(req.RefreshToken, p.AppConfig.TokenKey.RefreshTokenKey)
	if err != nil {
		return nil, err
	}

	// Get user
	user, _, err := p.GetUser(ctx, claims)
	if err != nil {
		return nil, err
	}

	if user.RefreshToken.String != "" && user.RefreshToken.String == req.RefreshToken[7:] {
		// create new token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": user.UserID,
		}, p.AppConfig.TokenKey.AccessTokenExpiryTime, p.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			return nil, err
		}

		return &api.RefreshLoginResponse{
			Status: constants.SuccessStatus,
			Token:  token,
		}, nil
	}

	return nil, errorwrapper.Error(apiError.BadRequest, "Invalid refresh token")
}

// GetUser ...
func (p *PermissionManagementImpl) GetUser(ctx context.Context, claims map[string]interface{}) (*storage.UserDTO, *sql.DB, error) {
	var user *storage.UserDTO
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to connect to DB")
	}

	// Get the user by user id
	user, err = storage.GetUserByUserID(ctx, db, claims["id"].(string))
	if err != nil {
		return nil, nil, err
	}

	return user, db, err
}
