package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/secret"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// ResetPassword ...
func (p *PermissionManagementImpl) ResetPassword(ctx context.Context, req *api.ResetPasswordRequest) (*api.ResetPasswordResponse, error) {
	// Authenticate the request
	claims, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, err
	}

	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	// Check password validity
	if req.Password != req.ConfirmPassword {
		return nil, errorwrapper.Error(apiError.BadRequest, "Password do not match")
	}

	// Encrypt password
	password, err := secret.GeneratePassword(req.Password)
	if err != nil {
		return nil, err
	}

	// Get user
	user, db, err := p.GetUser(ctx, claims)
	if err != nil {
		return nil, err
	}

	// Update password in db
	err = storage.UpdateUserPasswordToken(ctx, db, password, claims["id"].(string), user.ID)
	if err != nil {
		return nil, err
	}

	return &api.ResetPasswordResponse{
		Status: constants.SuccessStatus,
	}, nil
}
