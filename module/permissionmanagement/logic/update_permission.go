package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// UpdatePermission ...
//
// nolint: dupl
func (p *PermissionManagementImpl) UpdatePermission(ctx context.Context, req *api.UpdatePermissionRequest) (*api.UpdatePermissionResponse, error) {
	// check permission
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	perm, err := updatePermission(ctx, req, db, user.ID)
	if err != nil {
		return nil, err
	}

	auditTrailReq, auditErr := helper.WriteLogAuditTrailPermission(ctx, perm, "Update Permission", user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreatePermissionLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.UpdatePermissionResponse{
		Id:     perm.ID,
		Status: "Success",
	}, nil
}

func updatePermission(ctx context.Context, req *api.UpdatePermissionRequest, db *sql.DB, creatorID int64) (*storage.PermissionDTO, error) {
	// get existing permission by id
	existingPerm, err := storage.GetPermissionByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to get permission")
	}

	updatedPerm := constructUpdatePermission(req, creatorID)
	err = validatePermissionEligibilityForUpdate(ctx, existingPerm, updatedPerm, db)
	if err != nil {
		return nil, err
	}

	err = storage.UpdatePermission(ctx, db, updatedPerm)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to update permission")
	}

	return updatedPerm, nil
}

func constructUpdatePermission(req *api.UpdatePermissionRequest, creatorID int64) *storage.PermissionDTO {
	return &storage.PermissionDTO{
		ID:           req.Id,
		Name:         req.Name,
		Description:  req.Description,
		BitwiseValue: req.Bitwise,
		Status:       req.Status,
		ModuleID:     req.ModuleID,
		UpdatedBy:    sql.NullInt64{Int64: creatorID, Valid: true},
		UpdatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
	}
}

func validatePermissionEligibilityForUpdate(ctx context.Context, existingPerm *storage.PermissionDTO, updatedPerm *storage.PermissionDTO, db *sql.DB) error {
	// check if permission used when deactivate the permission
	if (existingPerm.Status != updatedPerm.Status && updatedPerm.Status == 0) || existingPerm.BitwiseValue != updatedPerm.BitwiseValue {
		roles, err := storage.GetRolesByPermissionUsed(ctx, db, existingPerm)
		if err != nil {
			return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to validate permission")
		}
		if len(roles) > 0 {
			return errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("failed to update permission due to still used in roles: %v.", strings.Join(roles, ", ")))
		}
	}

	count, err := storage.GetCountPermissionForDuplicateChecking(ctx, db, updatedPerm)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to validate permission")
	}

	if count > 0 {
		return errorwrapper.Error(apiError.BadRequest, "failed to update permission due to duplicate name or bitwise value in the same module")
	}

	return nil
}
