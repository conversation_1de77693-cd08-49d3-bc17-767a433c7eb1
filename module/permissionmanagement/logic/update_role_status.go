package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// UpdateRoleStatus ...
//
// nolint: funlen
func (p *PermissionManagementImpl) UpdateRoleStatus(ctx context.Context, req *api.UpdateRoleStatusRequest) (*api.UpdateRoleStatusResponse, error) {
	// check permission
	user, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.RoleManagement, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get role
	role, err := storage.GetRoleByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to get role")
	}

	role.Status = req.Status
	role.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	role.UpdatedBy = sql.NullInt64{Int64: user.ID, Valid: true}

	err = storage.UpdateRoleStatus(ctx, db, role)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to update role status")
	}

	// if deactivate expiry the redis and delete the user_roles
	if req.Status == 0 {
		errExp := expiringExistingUserByRole(ctx, db, role.ID)
		if errExp != nil {
			return nil, errExp
		}
		errRemove := storage.RemoveUserRoleByRoleID(ctx, db, role.ID)
		if errRemove != nil {
			return nil, errRemove
		}
	}

	var actionName string
	if req.Status == 0 {
		actionName = "deactivated and detached assigned user"
	} else {
		actionName = "reactivated"
	}

	// write audit trail
	description := fmt.Sprintf("User %s successfully %s for role with ID %v:%v", user.Name, actionName, role.ID, role.Name)
	title := fmt.Sprintf("Role %v successfully %s", role.Status, actionName)
	auditTrailReq, auditErr := helper.WriteLogAuditTrailRole(ctx, role, title, description, user.ID)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.UpdateStatusRoleLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.UpdateRoleStatusResponse{
		Id:     role.ID,
		Status: constants.SuccessStatus,
	}, nil
}

func expiringExistingUserByRole(ctx context.Context, db *sql.DB, roleID int64) error {
	assignedUsers, errCheck := storage.GetUsersByRoleID(ctx, db, roleID)
	if errCheck != nil {
		return errCheck
	}

	if len(assignedUsers) > 0 {
		for _, userRow := range assignedUsers {
			errRow := redis.DeleteRedisKey(ctx, constants.UserIDRedisKey+userRow.UserID, constants.UpdateStatusRoleLogTag)
			if errRow != nil {
				return errRow
			}
		}
	}

	return nil
}
