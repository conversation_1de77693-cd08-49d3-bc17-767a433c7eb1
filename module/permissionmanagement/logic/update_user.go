package logic

import (
	"context"
	"database/sql"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateUser ...
func (p *PermissionManagementImpl) UpdateUser(ctx context.Context, req *api.UpdateUserRequest) (*api.UpdateUserResponse, error) {
	// check permission
	creator, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.UserManagement, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}
	// Validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Update user
	user, err := updateUser(ctx, req, db, creator.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update user")
	}

	// Update role
	err = updateUserRole(ctx, req, db, creator.ID, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create role")
	}

	// reset user permission
	_, err = p.ResetPermissionByUserID(ctx, user.UserID, constants.UpdateUserLogTag)
	if err != nil {
		return nil, err
	}

	// Write audit trail
	title := "Update User"
	err = helper.WriteLogAuditTrailUser(ctx, user, title, title+" executed successfully", "USER", user.ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogoutLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	// Return the created user
	return &api.UpdateUserResponse{
		Status: constants.SuccessStatus,
	}, nil
}

func updateUser(ctx context.Context, req *api.UpdateUserRequest, db *sql.DB, creatorID int64) (*storage.UserDTO, error) {
	// validate user
	user, err := storage.GetUserByUserID(ctx, db, req.UserID)
	if err != nil {
		if errorwrapper.IsErrorCodeExist(err, apiError.ResourceNotFound) {
			return nil, errorwrapper.Error(apiError.FieldInvalid, "user doesn't exist")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	// Update user's info
	user.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	user.UpdatedBy = sql.NullInt64{Int64: creatorID, Valid: true}
	user.Email = req.Email
	user.Name = req.Name
	user.Status = req.Status

	err = storage.UpdateUser(ctx, db, user)
	if err != nil {
		return nil, err
	}

	return user, err
}

func updateUserRole(ctx context.Context, req *api.UpdateUserRequest, db *sql.DB, creatorID, id int64) error {
	// Get existing user role
	existingRole, err := storage.GetUserRole(ctx, db, id)
	if err != nil {
		return err
	}

	var roles []int64
	for _, value := range existingRole {
		roles = append(roles, value.ID)
	}

	// get unique role from current and request
	currRoleIDs, newRoleIDs := getUniqueRoles(roles, req.RoleIDs)

	// remove role
	for _, role := range currRoleIDs {
		err = storage.DeleteUserRole(ctx, db, id, role)
		if err != nil {
			return err
		}
	}

	// add new user role
	for _, role := range newRoleIDs {
		userRoleDTO := &storage.UserRoleDTO{
			RoleID: role,
			UserID: id,
			CreatedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			},
			CreatedBy: sql.NullInt64{
				Int64: creatorID, // FIXME: Hardcoded
				Valid: true,
			},
		}
		_, err = storage.CreateUserRole(ctx, db, userRoleDTO)
		if err != nil {
			return err
		}
	}

	return err
}

func getUniqueRoles(curr, new []int64) ([]int64, []int64) {
	existsInCurr := make(map[int64]bool)
	existsInNew := make(map[int64]bool)

	for _, role := range curr {
		existsInCurr[role] = true
	}
	for _, role := range new {
		existsInNew[role] = true
	}

	// get unique roles from current
	var uniqueInCurr []int64
	for role := range existsInCurr {
		if !existsInNew[role] {
			uniqueInCurr = append(uniqueInCurr, role)
		}
	}

	// get unique roles from request
	var uniqueInNew []int64
	for role := range existsInNew {
		if !existsInCurr[role] {
			uniqueInNew = append(uniqueInNew, role)
		}
	}

	return uniqueInCurr, uniqueInNew
}
