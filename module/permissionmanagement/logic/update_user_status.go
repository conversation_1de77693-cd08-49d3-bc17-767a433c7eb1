package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateUserStatus ...
func (p *PermissionManagementImpl) UpdateUserStatus(ctx context.Context, request *api.UpdateUserStatusRequest) (*api.UpdateUserStatusResponse, error) {
	// check permission
	creator, hasPerm, err := p.AuthenticateRequestByElementCode(ctx, constants.UserManagement, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}
	// Validate the request
	err = validations.ValidateRequest(request)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get user
	user, err := storage.GetUserByUserID(ctx, db, request.UserID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user by user id")
	}

	// Check current status
	if user.Status == request.Status {
		return nil, errorwrapper.Error(apiError.FieldInvalid, "current status matches the requested status")
	}

	// Update user status
	err = storage.UpdateUserStatus(ctx, db, request.Status, request.UserID, creator.ID)
	if err != nil {
		return nil, err
	}

	// write audit trail
	title := "Update User Status"
	err = helper.WriteLogAuditTrailUser(ctx, user, title, title+" executed successfully", "USER", user.ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogoutLogTag, err.Error(), utils.GetTraceID(ctx))
	}

	return &api.UpdateUserStatusResponse{
		Status: "Success",
	}, nil
}
