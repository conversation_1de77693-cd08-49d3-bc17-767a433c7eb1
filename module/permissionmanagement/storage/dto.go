package storage

import "database/sql"

// UserDTO represents the user data transfer object
type UserD<PERSON> struct {
	ID           int64          `json:"id" db:"id"`
	CreatedAt    sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullInt64  `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullInt64  `json:"updated_by" db:"updated_by"`
	Name         string         `json:"name" db:"name"`
	Email        string         `json:"email" db:"email"`
	Status       int64          `json:"status" db:"status"`
	Password     string         `json:"password" db:"password"`
	RefreshToken sql.NullString `json:"refresh_token" db:"refresh_token"`
	UserID       string         `json:"user_id" db:"user_id"`
}

// UserListDTO represents the user data transfer object
type UserListD<PERSON> struct {
	ID           int64          `json:"id" db:"id"`
	CreatedAt    sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullString `json:"updated_by" db:"updated_by"`
	Name         string         `json:"name" db:"name"`
	Email        string         `json:"email" db:"email"`
	Status       int64          `json:"status" db:"status"`
	Password     string         `json:"password" db:"password"`
	RefreshToken sql.NullString `json:"refresh_token" db:"refresh_token"`
	UserID       string         `json:"user_id" db:"user_id"`
}

// RoleDTO represents the role data transfer object
type RoleDTO struct {
	ID        int64         `json:"id" db:"id"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name      string        `json:"name" db:"name"`
	Status    int64         `json:"status" db:"status"`
}

// UserRoleDTO represents the user role data transfer object
type UserRoleDTO struct {
	ID        int64         `json:"id" db:"id"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	UserID    int64         `json:"user_id" db:"user_id"`
	RoleID    int64         `json:"role_id" db:"role_id"`
}

// PermissionDTO represents the permission data transfer object
type PermissionDTO struct {
	ID           int64         `json:"id" db:"id"`
	CreatedAt    sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name         string        `json:"name" db:"name"`
	BitwiseValue int64         `json:"bitwise_value" db:"bitwise_value"`
	Description  string        `json:"description" db:"description"`
	ModuleID     int64         `json:"module_id" db:"module_id"`
	Status       int32         `json:"status" db:"status"`
}

// RoleElementPermissionDTO represents the role element permission data transfer object
type RoleElementPermissionDTO struct {
	ID           int64         `json:"id" db:"id"`
	CreatedAt    sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullInt64 `json:"updated_by" db:"updated_by"`
	ElementID    int64         `json:"element_id" db:"element_id"`
	BitwiseValue int64         `json:"bitwise_value" db:"bitwise_value"`
	RoleID       int64         `json:"role_id" db:"role_id"`
}

// RoleListDTO represent role data only for get list role
type RoleListDTO struct {
	ID        int64          `json:"id" db:"id"`
	CreatedAt sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy sql.NullString `json:"updated_by" db:"updated_by"`
	Name      string         `json:"name" db:"name"`
	Status    int64          `json:"status" db:"status"`
}

// UserPermissionsDetail only for user permission DTO from redis
type UserPermissionsDetail struct {
	ID          int64
	UserID      string
	Email       string
	Name        string
	Modules     []string
	Roles       []string
	Permissions map[string]int64
}

// RoleElementPermissionListDTO ...
type RoleElementPermissionListDTO struct {
	ElementID    sql.NullInt64  `json:"element_id" db:"element_id"`
	ElementCode  sql.NullString `json:"element_code" db:"element_code"`
	BitwiseValue sql.NullInt64  `json:"bitwise_value" db:"bitwise_value"`
	RoleName     string         `json:"role_name" db:"role_name"`
	ModuleName   sql.NullString `json:"module_name" db:"module_name"`
}

// PermissionListDTO ...
type PermissionListDTO struct {
	ID           int64          `json:"id" db:"id"`
	Name         string         `json:"name" db:"name"`
	Description  string         `json:"description" db:"description"`
	Status       int64          `json:"status" db:"status"`
	BitwiseValue int64          `json:"bitwise_value" db:"bitwise_value"`
	CreatedAt    sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullString `json:"updated_by" db:"updated_by"`
	ModuleID     int64          `json:"module_id" db:"module_id"`
	ModuleName   string         `json:"module_name" db:"module_name"`
}
