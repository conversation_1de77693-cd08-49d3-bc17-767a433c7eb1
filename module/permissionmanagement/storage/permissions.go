package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// IsUserHasPermissionForElementID checks if a user has a permission for an element
//
//nolint:dupl
func IsUserHasPermissionForElementID(ctx context.Context, db *sql.DB, userID int64, elementID int64, bitwiseValueRequired int64) (bool, int64, error) {
	bitwiseValue, err := GetUserBitwiseValueForElementID(ctx, db, userID, elementID)
	if err != nil {
		return false, 0, errorwrapper.WrapError(err, apiError.Idem, "failed to get user bitwise value for element id")
	}

	return bitwiseValue&bitwiseValueRequired == bitwiseValueRequired, bitwiseValue, nil
}

// GetUserBitwiseValueForElementID gets the bitwise value of a user for an element
func GetUserBitwiseValueForElementID(ctx context.Context, db *sql.DB, userID int64, elementID int64) (int64, error) {
	var query = `
	select BIT_OR(rep.bitwise_value) as bitwise_value
	from roles_elements_permissions rep
	inner join users_roles ur on ur.role_id = rep.role_id
	where rep.element_id = ?
	and ur.user_id = ?;
	`

	var bitwiseValue int64
	err := db.QueryRowContext(ctx, query, elementID, userID).Scan(&bitwiseValue)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, errorwrapper.Error(apiError.Forbidden, "user bitwise value not found for given element id")
		}
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return bitwiseValue, nil
}

// CreatePermission creates a permission
//
//nolint:dupl
func CreatePermission(ctx context.Context, db *sql.DB, p *PermissionDTO) (id int64, err error) {
	var query = `INSERT INTO permissions (name, description, created_at, updated_at, created_by, updated_by, bitwise_value, module_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result, err := db.ExecContext(ctx, query, p.Name, p.Description, p.CreatedAt, p.UpdatedAt, p.CreatedBy, p.UpdatedBy, p.BitwiseValue, p.ModuleID, p.Status)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return id, nil
}

// UpdatePermission updates a permission
func UpdatePermission(ctx context.Context, db *sql.DB, p *PermissionDTO) error {
	var query = `UPDATE permissions SET name = ?, description = ?, bitwise_value = ?, status = ?, module_id = ?, updated_at = ?, updated_by = ? WHERE id = ?`
	_, err := db.ExecContext(ctx, query, p.Name, p.Description, p.BitwiseValue, p.Status, p.ModuleID, p.UpdatedAt, p.UpdatedBy, p.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return nil
}

// IsUserHasPermissionForElementCode checks if a user has a permission for an element
func IsUserHasPermissionForElementCode(ctx context.Context, db *sql.DB, userID int64, code string, bitwiseValueRequired int64) (bool, int64, error) {
	bitwiseValue, err := GetUserBitwiseValueForElementCode(ctx, db, userID, code)
	if err != nil {
		return false, 0, errorwrapper.WrapError(err, apiError.Idem, "failed to get user bitwise value for element id")
	}

	return bitwiseValue&bitwiseValueRequired == bitwiseValueRequired, bitwiseValue, nil
}

// GetUserBitwiseValueForElementCode gets the bitwise value of a user for an element
func GetUserBitwiseValueForElementCode(ctx context.Context, db *sql.DB, userID int64, code string) (int64, error) {
	var query = `
	select BIT_OR(rep.bitwise_value) from roles_elements_permissions rep
	inner join elements e on e.id = rep.element_id
	inner join users_roles ur on ur.role_id = rep.role_id
	where e.code = ?
	and ur.user_id = ?;
	`

	var bitwiseValue int64
	err := db.QueryRowContext(ctx, query, code, userID).Scan(&bitwiseValue)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, errorwrapper.Error(apiError.Forbidden, "user bitwise value not found for given element id")
		}
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return bitwiseValue, nil
}

// GetTotalBitwisePermission ...
func GetTotalBitwisePermission(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (int64, error) {
	var query = `
		SELECT COALESCE(sum(bitwise_value),0) as total_bitwise from permissions
	`
	query, args := storage.BuildQuery(query, conditions...)
	var totalBitwise int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&totalBitwise)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return totalBitwise, nil
}

// GetPermissionsByModuleAndBitwise ...
//
// nolint: errcheck
func GetPermissionsByModuleAndBitwise(ctx context.Context, db *sql.DB, totalBitwise int64, moduleID int64) ([]*PermissionDTO, error) {
	var query = `
	WITH perm_ids as (
		SELECT * from permissions 
		where module_id = ?
	)
	SELECT id, name, bitwise_value, description, module_id, created_at, created_by, updated_at, updated_by, status from perm_ids pi
	WHERE ? & pi.bitwise_value = pi.bitwise_value and pi.status = 1;
	`

	// Execute the query
	rows, err := db.QueryContext(ctx, query, moduleID, totalBitwise)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	// Scan the results
	permissions := []*PermissionDTO{}
	for rows.Next() {
		perm := PermissionDTO{}
		err := rows.Scan(&perm.ID, &perm.Name, &perm.BitwiseValue, &perm.Description, &perm.ModuleID, &perm.CreatedAt, &perm.CreatedBy, &perm.UpdatedAt, &perm.UpdatedBy, &perm.Status)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		permissions = append(permissions, &perm)
	}

	return permissions, nil
}

// GetPermissions ...
//
// nolint: dupl
func GetPermissions(ctx context.Context, db *sql.DB) ([]PermissionDTO, error) {
	var query = `
		SELECT id, name, bitwise_value, description, module_id, created_at, created_by, updated_at, updated_by FROM permissions
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var permissions []PermissionDTO
	for rows.Next() {
		var perm PermissionDTO
		scanErr := rows.Scan(&perm.ID,
			&perm.Name,
			&perm.BitwiseValue,
			&perm.Description,
			&perm.ModuleID,
			&perm.CreatedAt,
			&perm.CreatedBy,
			&perm.UpdatedAt,
			&perm.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		permissions = append(permissions, perm)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return permissions, nil
}

// GetPermissionList ...
func GetPermissionList(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]*PermissionListDTO, error) {
	var query = `
	SELECT p.id, p.name, p.description, p.status, p.bitwise_value, p.created_at, p.updated_at, u.name as created_by, u2.name as updated_by, p.module_id, m.name as module_name from permissions p
	INNER JOIN modules m ON p.module_id = m.id
	LEFT JOIN users u ON p.created_by = u.id
	LEFT JOIN users u2 ON p.updated_by = u2.id`

	query, args := storage.BuildQuery(query, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var permissions []*PermissionListDTO
	for rows.Next() {
		var perm PermissionListDTO
		scanErr := rows.Scan(&perm.ID,
			&perm.Name,
			&perm.Description,
			&perm.Status,
			&perm.BitwiseValue,
			&perm.CreatedAt,
			&perm.UpdatedAt,
			&perm.CreatedBy,
			&perm.UpdatedBy,
			&perm.ModuleID,
			&perm.ModuleName)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		permissions = append(permissions, &perm)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return permissions, nil
}

// GetCountPermissions ...
func GetCountPermissions(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (int64, error) {
	var query = `SELECT count(*) FROM permissions p`
	query, args := storage.BuildQuery(query, conditions...)
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}

// GetCountPermissionForDuplicateChecking ...
func GetCountPermissionForDuplicateChecking(ctx context.Context, db *sql.DB, perm *PermissionDTO) (int64, error) {
	var query = `SELECT count(*) from permissions 
	WHERE (name = ? OR bitwise_value = ?) AND module_id = ?`

	args := []any{perm.Name, perm.BitwiseValue, perm.ModuleID}

	if perm.ID != 0 {
		query += ` AND id != ?`
		args = append(args, perm.ID)
	}

	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}

// GetPermissionByID ...
//
// nolint: dupl
func GetPermissionByID(ctx context.Context, db *sql.DB, id int64) (*PermissionDTO, error) {
	var query = `SELECT id, name, bitwise_value, description, module_id, status, created_at, updated_at, created_by, updated_by from permissions
	WHERE id = ?`

	var perm PermissionDTO
	err := db.QueryRowContext(ctx, query, id).Scan(&perm.ID,
		&perm.Name,
		&perm.BitwiseValue,
		&perm.Description,
		&perm.ModuleID,
		&perm.Status,
		&perm.CreatedAt,
		&perm.UpdatedAt,
		&perm.CreatedBy,
		&perm.UpdatedBy)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("Permission not found, err: %s", err.Error()))
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &perm, nil
}

// GetRolesByPermissionUsed ...
func GetRolesByPermissionUsed(ctx context.Context, db *sql.DB, perm *PermissionDTO) ([]string, error) {
	var query = `SELECT DISTINCT(r.name) as name FROM roles_elements_permissions rep
    INNER JOIN roles r ON rep.role_id = r.id
	WHERE element_id IN (
	    SELECT id from elements WHERE module_id = ?
	) AND (rep.bitwise_value & ?) = ?`

	args := []any{perm.ModuleID, perm.BitwiseValue, perm.BitwiseValue}

	var roles []string
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return roles, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	for rows.Next() {
		var role string
		scanErr := rows.Scan(&role)
		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, role)
	}

	if err = rows.Err(); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return roles, nil
}

// GetPermissionByModuleID ...
func GetPermissionByModuleID(ctx context.Context, db *sql.DB, moduleID int64) ([]*PermissionDTO, error) {
	var query = `SELECT id, name, bitwise_value, description, module_id, status, created_at, updated_at, created_by, updated_by from permissions
	WHERE module_id = ?`

	rows, err := db.QueryContext(ctx, query, moduleID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var permissions []*PermissionDTO
	for rows.Next() {
		var perm PermissionDTO
		scanErr := rows.Scan(&perm.ID,
			&perm.Name,
			&perm.BitwiseValue,
			&perm.Description,
			&perm.ModuleID,
			&perm.Status,
			&perm.CreatedAt,
			&perm.UpdatedAt,
			&perm.CreatedBy,
			&perm.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		permissions = append(permissions, &perm)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return permissions, nil
}

// UpdatePermissionStatus ...
func UpdatePermissionStatus(ctx context.Context, db *sql.DB, p *PermissionDTO) error {
	var query = `UPDATE permissions SET status = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	_, err := db.ExecContext(ctx, query, p.Status, p.UpdatedAt, p.UpdatedBy, p.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}
