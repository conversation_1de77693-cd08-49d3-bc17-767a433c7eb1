package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreatePermissionMatrix ...
func CreatePermissionMatrix(ctx context.Context, db *sql.DB, permissionMatrixDTO *RoleElementPermissionDTO) (int64, error) {
	// Prepare the query
	query := `
		INSERT INTO roles_elements_permissions (
			role_id,
			element_id,
			bitwise_value,
			created_at,
			created_by
		) VALUES (
			?, ?, ?, ?, ?
		)
	`

	// Execute the query
	result, err := db.ExecContext(
		ctx,
		query,
		permissionMatrixDTO.RoleID,
		permissionMatrixDTO.ElementID,
		permissionMatrixDTO.BitwiseValue,
		permissionMatrixDTO.CreatedAt,
		permissionMatrixDTO.CreatedBy,
	)
	if err != nil {
		return 0, err
	}

	// Get the created ID
	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return id, nil
}

// UpdatePermissionMatrix ...
func UpdatePermissionMatrix(ctx context.Context, db *sql.DB, permissionMatrixDTO *RoleElementPermissionDTO) error {
	// Prepare the query
	query := `
		UPDATE roles_elements_permissions
		SET bitwise_value = ?,
			updated_at = ?,
			updated_by = ?
		WHERE id = ?
	`

	// Execute the query
	_, err := db.ExecContext(
		ctx,
		query,
		permissionMatrixDTO.BitwiseValue,
		permissionMatrixDTO.UpdatedAt,
		permissionMatrixDTO.UpdatedBy,
		permissionMatrixDTO.ID,
	)
	if err != nil {
		return err
	}

	return nil
}

// GetListElementPermission ...
//
// nolint: dupl
func GetListElementPermission(ctx context.Context, db *sql.DB, userID int64) ([]*RoleElementPermissionListDTO, error) {
	var query = `
		select 
		    ro.role_name,
		    rp.element_id,
		    rp.element_code,
		    rp.bitwise_value,
		    rp.module_name
		from 
		(
			select ur.role_id, r.name as role_name from users_roles ur 
			inner join roles r on ur.role_id = r.id 
			where r.status = 1 AND ur.user_id = ?
		) as ro 
		left join (
			select rep.role_id, rep.element_id, rep.bitwise_value, e.code as element_code, m.name as module_name from roles_elements_permissions rep 
			inner join elements e on rep.element_id = e.id 
			inner join modules m on e.module_id = m.id 
		) as rp 
		on ro.role_id = rp.role_id;
	`

	rows, err := db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var elemPermissions []*RoleElementPermissionListDTO
	for rows.Next() {
		var matrix RoleElementPermissionListDTO
		scanErr := rows.Scan(&matrix.RoleName,
			&matrix.ElementID,
			&matrix.ElementCode,
			&matrix.BitwiseValue,
			&matrix.ModuleName)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		elemPermissions = append(elemPermissions, &matrix)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return elemPermissions, nil
}

// GetPermissionByRoleID ...
func GetPermissionByRoleID(ctx context.Context, db *sql.DB, roleID int64) ([]*RoleElementPermissionDTO, error) {
	var query = `
		SELECT id, element_id, role_id, bitwise_value, created_at, updated_at, created_by, updated_by from roles_elements_permissions
		where role_id = ?
	`

	rows, err := db.QueryContext(ctx, query, roleID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var permissions []*RoleElementPermissionDTO
	for rows.Next() {
		var perm RoleElementPermissionDTO
		scanErr := rows.Scan(&perm.ID,
			&perm.ElementID,
			&perm.RoleID,
			&perm.BitwiseValue,
			&perm.CreatedAt,
			&perm.UpdatedAt,
			&perm.CreatedBy,
			&perm.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		permissions = append(permissions, &perm)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return permissions, nil
}

// RemovePermissions ...
func RemovePermissions(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) error {
	var query = `DELETE from roles_elements_permissions`
	query, args := storage.BuildQuery(query, conditions...)

	_, err := db.ExecContext(ctx, query, args...)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}
