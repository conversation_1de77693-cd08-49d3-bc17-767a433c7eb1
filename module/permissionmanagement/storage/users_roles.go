package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateUserRole ...
func CreateUserRole(ctx context.Context, db *sql.DB, r *UserRoleDTO) (int64, error) {
	var query = `INSERT INTO users_roles (role_id, created_at, created_by, user_id)
	VALUES (?, ?, ?, ?)`

	result, err := db.ExecContext(ctx, query, r.<PERSON>, r.CreatedAt, r.CreatedBy, r.UserID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateUserRole ...
func UpdateUserRole(ctx context.Context, db *sql.DB, r *UserRoleDTO) error {
	var query = `UPDATE users_roles
	SET role_id = ?, updated_at = ?, updated_by = ?, user_id = ?
	WHERE id = ?`

	_, err := db.ExecContext(ctx, query, r.RoleID, r.UpdatedAt, r.UpdatedBy, r.UserID, r.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// DeleteUserRole ...
func DeleteUserRole(ctx context.Context, db *sql.DB, userID, roleID int64) error {
	var query = `DELETE FROM users_roles
	WHERE user_id = ? and role_id = ?`

	_, err := db.ExecContext(ctx, query, userID, roleID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// RemoveUserRoleByRoleID ...
func RemoveUserRoleByRoleID(ctx context.Context, db *sql.DB, roleID int64) error {
	var query = `DELETE from users_roles
	WHERE role_id = ?`

	_, err := db.ExecContext(ctx, query, roleID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}
