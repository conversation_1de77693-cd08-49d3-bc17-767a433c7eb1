//nolint:golint,typecheck
package custom

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// RolesFilterValidation ...
func RolesFilterValidation(fl validations.FieldLevel) bool {
	allowedFilterColumn := []string{"status"}

	filters, ok := fl.Field().Interface().([]api.Filter)
	if !ok {
		slog.FromContext(context.Background()).Error("RolesFilterValidation", "error getting filter")
		return false
	}

	for _, filter := range filters {
		if filter.Column != "" && !utils.SliceContains(allowedFilterColumn, filter.Column) {
			slog.FromContext(context.Background()).Error("RolesFilterValidation", "invalid column")
			return false
		}
	}

	return true
}

// RolesSortByValidation ...
func RolesSortByValidation(fl validations.FieldLevel) bool {
	allowedSortColumn := []string{"id", "name", "created_at", "updated_at"}

	sort, ok := fl.Field().Interface().(api.Sort)
	if !ok {
		slog.FromContext(context.Background()).Error("RolesSortValidation", "error getting filter")
		return false
	}

	if sort.Column != "" && !utils.SliceContains(allowedSortColumn, sort.Column) {
		slog.FromContext(context.Background()).Error("RolesSortValidation", "invalid column")
		return false
	}

	return true
}
