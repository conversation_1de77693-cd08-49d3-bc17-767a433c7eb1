// Package generateexecutionresultfile provides the background process to generate execution result file.
package generateexecutionresultfileV2

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// New is the constructor of handleGenerateExecutionResultFile
func New(appConfig *config.AppConfig, cache redis.Client, types string) background.Handler {
	return &handleGenerateExecutionResultFile{
		AppConfig: appConfig,
		Cache:     cache,
		Type:      types,
	}
}

type handleGenerateExecutionResultFile struct {
	AppConfig *config.AppConfig
	Cache     redis.Client
	Type      string
}

// Handle is the main function to handle the background process
//
// nolint: funlen
func (h *handleGenerateExecutionResultFile) Handle() {
	tag := "handleGenerateExecutionResultFileV2.Handle"
	ctx, cancel := background.Context()
	defer cancel()
	defer func() {
		if r := recover(); r != nil {
			slog.FromContext(ctx).Error(tag, "panic on execution: "+fmt.Sprintf("%v", r))
		}
	}()
	slog.FromContext(ctx).Info(tag, "generate execution result file process started")

	var cmder []string
	err := h.Cache.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		datas, err := pipe.Keys(ctx, fmt.Sprintf("fp:%s:*:fileName", h.Type)).Result()
		if err != nil {
			return err
		}
		cmder = datas
		return nil
	})

	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to get list fileName:"+err.Error())
		return
	}

	for _, data := range cmder {
		fileName, err := h.Cache.GetString(ctx, data)

		if err != nil {
			continue
		}

		_, done := h.getFileResponse(ctx, fileName)
		if !done {
			continue
		}

		// clean up file processor
		h.cleanup(ctx, fileName)
	}
}

// nolint: dupl
func (h *handleGenerateExecutionResultFile) getFileResponse(ctx context.Context, fileName string) (dto.FileResponse, bool) {
	// TODO: switch case for different file processor usecases
	interactor := fileprocessor.NewBulkWriteOffFileProcessorV2().WithCache(h.Cache)
	if !interactor.IsLocked(ctx, fileName) {
		slog.FromContext(ctx).Info("tagBulkWriteOff", "file processor is not locked. Skipping...")
		return dto.FileResponse{}, false
	}
	if !interactor.IsExecutionDone(ctx, fileName) {
		slog.FromContext(ctx).Info("tagBulkWriteOff", "execution is not done")
		return dto.FileResponse{}, false
	}

	return interactor.CompileExecutionResult(ctx, fileName), true
}

// nolint: dupl
func (h *handleGenerateExecutionResultFile) cleanup(ctx context.Context, fileName string) {
	// TODO: switch case for different file processor usecases
	interactor := fileprocessor.NewBulkWriteOffFileProcessorV2().WithCache(h.Cache)
	interactor.PostExecutionCleanup(ctx, fileName)
}
