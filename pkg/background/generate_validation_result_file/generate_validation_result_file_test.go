// Package generatevalidationresultfile provides the background process to generate validation result file.
package generatevalidationresultfile

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	mockAppianLib "gitlab.super-id.net/bersama/opsce/onedash-be/external/appian/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

func Test_handleGenerateValidationResultFile_Handle(t *testing.T) {
	type fields struct {
		FileName     string
		Success      dto.FileResult
		Failed       dto.FileResult
		AppConfig    *config.AppConfig
		AppianClient appian.Appian
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "error failed to post bulk file validation status",
			fields: fields{
				FileName: "filename",
				Success: dto.FileResult{
					FileName: "success.csv",
					Payload:  "success-payload",
					Count:    10,
				},
				Failed: dto.FileResult{
					FileName: "failed.csv",
					Payload:  "failed-payload",
					Count:    5,
				},
				AppConfig: &config.AppConfig{
					Appian: config.AppianConfig{
						Config: &appian.Config{
							RegisteredClientID:     "client-id",
							RegisteredClientSecret: "client-secret",
							GrantType:              "client_credentials",
						},
					},
				},
				AppianClient: func() appian.Appian {
					m := mockAppianLib.NewAppian(t)
					m.On("GetAccessToken", mock.Anything, &dto.GetAccessTokenRequest{
						ClientID:     "client-id",
						ClientSecret: "client-secret",
						GrantType:    "client_credentials",
					}).Return(&dto.GetAccessTokenResponse{
						AccessToken: "test-token",
					}, nil)

					// Match with authorization header that contains the token
					m.On("PostBulkFileValidationStatus", mock.MatchedBy(func(ctx context.Context) bool {
						return true
					}), mock.MatchedBy(func(req *dto.BulkFileStatusRequest) bool {
						return req.SourceFileName == "filename" &&
							req.Result["success"].Count == 10 &&
							req.Result["failed"].Count == 5 &&
							req.Status == fileprocessor.StatusSuccess
					})).Return(nil, fmt.Errorf("error posting bulk status"))

					return m
				}(),
			},
		},
		{
			name: "success with all operations",
			fields: fields{
				FileName: "filename",
				Success: dto.FileResult{
					FileName: "success.csv",
					Payload:  "success-payload",
					Count:    15,
				},
				Failed: dto.FileResult{
					FileName: "failed.csv",
					Payload:  "failed-payload",
					Count:    3,
				},
				AppConfig: &config.AppConfig{
					Appian: config.AppianConfig{
						Config: &appian.Config{
							RegisteredClientID:     "client-id",
							RegisteredClientSecret: "client-secret",
							GrantType:              "client_credentials",
						},
					},
				},
				AppianClient: func() appian.Appian {
					m := mockAppianLib.NewAppian(t)
					m.On("GetAccessToken", mock.Anything, &dto.GetAccessTokenRequest{
						ClientID:     "client-id",
						ClientSecret: "client-secret",
						GrantType:    "client_credentials",
					}).Return(&dto.GetAccessTokenResponse{
						AccessToken: "test-token",
					}, nil)

					// Match with authorization header that contains the token
					m.On("PostBulkFileValidationStatus", mock.MatchedBy(func(ctx context.Context) bool {
						return true
					}), mock.MatchedBy(func(req *dto.BulkFileStatusRequest) bool {
						return req.SourceFileName == "filename" &&
							req.Result["success"].Count == 15 &&
							req.Result["failed"].Count == 3 &&
							req.Status == fileprocessor.StatusSuccess &&
							req.Message == "Successfully processed 18 records"
					})).Return(&dto.BulkFileStatusResponse{Status: "success"}, nil)

					return m
				}(),
			},
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			h := &handleGenerateValidationResultFile{
				FileName:     tt.fields.FileName,
				Success:      tt.fields.Success,
				Failed:       tt.fields.Failed,
				AppConfig:    tt.fields.AppConfig,
				AppianClient: tt.fields.AppianClient,
			}
			h.Handle()
		})
	}
}

// nolint:gocognit
func TestNew(t *testing.T) {
	type args struct {
		filename     string
		success      dto.FileResult
		failed       dto.FileResult
		appConfig    *config.AppConfig
		appianClient appian.Appian
	}
	tests := []struct {
		name string
		args args
		want background.Handler
	}{
		{
			name: "successfully create a new handler",
			args: args{
				filename: "test-file.csv",
				success: dto.FileResult{
					FileName: "success.csv",
					Payload:  "success-payload",
					Count:    10,
				},
				failed: dto.FileResult{
					FileName: "failed.csv",
					Payload:  "failed-payload",
					Count:    5,
				},
				appConfig: &config.AppConfig{
					Appian: config.AppianConfig{
						Config: &appian.Config{
							RegisteredClientID:     "client-id",
							RegisteredClientSecret: "client-secret",
							GrantType:              "client_credentials",
						},
					},
				},
				appianClient: mockAppianLib.NewAppian(t),
			},
			want: &handleGenerateValidationResultFile{
				FileName: "test-file.csv",
				Success: dto.FileResult{
					FileName: "success.csv",
					Payload:  "success-payload",
					Count:    10,
				},
				Failed: dto.FileResult{
					FileName: "failed.csv",
					Payload:  "failed-payload",
					Count:    5,
				},
				AppConfig: &config.AppConfig{
					Appian: config.AppianConfig{
						Config: &appian.Config{
							RegisteredClientID:     "client-id",
							RegisteredClientSecret: "client-secret",
							GrantType:              "client_credentials",
						},
					},
				},
				AppianClient: mockAppianLib.NewAppian(t),
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			got := New(tt.args.filename, tt.args.success, tt.args.failed, tt.args.appConfig, tt.args.appianClient)

			// Check that we got the right type
			handler, ok := got.(*handleGenerateValidationResultFile)
			if !ok {
				t.Errorf("New() returned wrong type, expected *handleGenerateValidationResultFile, got %T", got)
				return
			}

			// Check individual fields
			if handler.FileName != tt.args.filename {
				t.Errorf("New() FileName = %v, want %v", handler.FileName, tt.args.filename)
			}

			if handler.Success.FileName != tt.args.success.FileName ||
				handler.Success.Count != tt.args.success.Count ||
				handler.Success.Payload != tt.args.success.Payload {
				t.Errorf("New() Success = %v, want %v", handler.Success, tt.args.success)
			}

			if handler.Failed.FileName != tt.args.failed.FileName ||
				handler.Failed.Count != tt.args.failed.Count ||
				handler.Failed.Payload != tt.args.failed.Payload {
				t.Errorf("New() Failed = %v, want %v", handler.Failed, tt.args.failed)
			}

			// For AppConfig, just check it's the same pointer
			if handler.AppConfig != tt.args.appConfig {
				t.Errorf("New() AppConfig = %p, want %p", handler.AppConfig, tt.args.appConfig)
			}

			// For AppianClient interface, we can only check it's not nil
			if handler.AppianClient == nil {
				t.Errorf("New() AppianClient is nil")
			}
		})
	}
}
