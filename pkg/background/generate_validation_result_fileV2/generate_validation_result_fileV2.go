// Package generatevalidationresultfile provides the background process to generate validation result file.
package generatevalidationresultfileV2

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"fmt"
	"mime"
	"strings"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// New is the constructor of handleGenerateValidationResultFile
func New(document *storage.DocumentDTO, appConfig *config.AppConfig, s3Client s3client.S3, redisClient redis.Client, types string) background.Handler {
	return &handleGenerateValidationResultFile{
		Document:    document,
		AppConfig:   appConfig,
		S3Client:    s3Client,
		RedisClient: redisClient,
		Type:        types,
	}
}

type handleGenerateValidationResultFile struct {
	Document    *storage.DocumentDTO
	AppConfig   *config.AppConfig
	S3Client    s3client.S3
	RedisClient redis.Client
	Type        string
}

// Handle is the main function to handle the background process
//
// nolint: funlen
func (h *handleGenerateValidationResultFile) Handle() {
	tag := "handleGenerateValidationResultFileV2.Handle"
	ctx, cancel := background.Context()
	defer cancel()
	defer func() {
		if r := recover(); r != nil {
			slog.FromContext(ctx).Error(tag, "panic on execution: "+fmt.Sprintf("%v", r))
		}
	}()
	slog.FromContext(ctx).Info(tag, "generate validation result file process started")

	validateData(ctx, h, tag)
}

func (h *handleGenerateValidationResultFile) generateDocumentBucketPath(documentName string) string {
	return fmt.Sprintf("%s/%s", h.AppConfig.S3Config.Directory, documentName)
}

func getFileExtensionFromFileName(name string) (string, error) {
	splittedOriginalFileName := strings.Split(name, ".")
	if len(splittedOriginalFileName) < 2 {
		return "", fmt.Errorf("invalid filename: no extension found")
	}
	return splittedOriginalFileName[len(splittedOriginalFileName)-1], nil
}

func updateDocumentFailed(ctx context.Context, db *sql.DB, document *storage.DocumentDTO, tag string) {
	document.ValidationStatus = sql.NullString{String: "FAILED", Valid: true}
	err := storage.UpdateDocumentsForValidation(ctx, db, document)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to update parent document:"+err.Error())
	}
}

// nolint:funlen
func validateData(ctx context.Context, h *handleGenerateValidationResultFile, tag string) {
	// set interactor
	var success *fileprocessor.WriteOffFileProcessor
	var failed *fileprocessor.WriteOffFileProcessor
	var interactor *fileprocessor.WriteOffFileProcessor

	switch h.Type {
	case fileprocessor.WriteOffFileProcessorUsecaseV2:
		success = fileprocessor.NewBulkWriteOffFileProcessorV2() // file generator only
		failed = fileprocessor.NewBulkWriteOffFileProcessorV2()  // file generator only
		interactor = fileprocessor.NewBulkWriteOffFileProcessorV2().
			WithCache(h.RedisClient).
			WithTimeoutInSec(h.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.Timeout).
			WithRecordTTL(h.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.RecordTTL)
	case fileprocessor.WaiveOffFileProcessorUsecaseV2:
		success = fileprocessor.NewBulkWaiveOffFileProcessorV2() // file generator only
		failed = fileprocessor.NewBulkWaiveOffFileProcessorV2()  // file generator only
		interactor = fileprocessor.NewBulkWaiveOffFileProcessorV2().
			WithCache(h.RedisClient).
			WithTimeoutInSec(h.AppConfig.FileProcessorConfig.WaiveOffFileProcessorConfig.Timeout).
			WithRecordTTL(h.AppConfig.FileProcessorConfig.WaiveOffFileProcessorConfig.RecordTTL)
	}

	// init db
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, h.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to get database handle:"+err.Error())
	}

	obj, err := h.S3Client.GetObjectWithContext(ctx, h.AppConfig.S3Config.BucketName, "onedash-documents/"+h.Document.Name)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to get csv from s3:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	// read the file
	records, err := interactor.LoadFromCSV(ctx, obj)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed load csv:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
	if len(records) > h.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.MaxRecords {
		slog.FromContext(ctx).Error(tag, fmt.Sprintf("Bulk write off is limited to %d records", h.AppConfig.FileProcessorConfig.WriteOffFileProcessorConfig.MaxRecords))
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	successCount, failedCount := validateDataCSV(ctx, records, success, failed)

	// generate file validation
	successFile, errSuccess := success.GenerateValidationResultFile(ctx, h.Document.Name, "success")
	if errSuccess != nil {
		slog.FromContext(ctx).Error(tag, "failed to generate success file, fileName: "+h.Document.Name)
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
	failedFile, errFailed := failed.GenerateValidationResultFile(ctx, h.Document.Name, "failed")
	if errFailed != nil {
		slog.FromContext(ctx).Error(tag, "failed to generate failed file, fileName: "+h.Document.Name)
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	uploadFileResultToS3(ctx, db, h, tag, &successFile, &failedFile)

	insertValidationResultToDatabase(ctx, db, h, tag, &successFile, &failedFile, int64(len(records)), successCount, failedCount)
}

func validateDataCSV(
	ctx context.Context,
	records []fileprocessor.WriteOffFileRequestDTO,
	success, failed *fileprocessor.WriteOffFileProcessor,
) (int64, int64) {
	cacheRows := map[string]interface{}{}
	successCount := 0
	failedCount := 0

	// validate
	for _, r := range records {
		meta := fileprocessor.WriteOffDTO{
			CIFNumber:     r.CIFNumber,
			OverallStatus: fileprocessor.StatusPending, // initial status
		}

		if _, ok := cacheRows[r.CIFNumber]; ok {
			slog.FromContext(ctx).Warn("tagBulkWaiveOff", fmt.Sprintf("Duplicate record found for CIF: %s", r.CIFNumber))
			meta.OverallStatus = fileprocessor.StatusFailed
			meta.FailureReason = "Duplicate CIF Number"

			failed.Records = append(failed.Records, meta)
			failedCount++
			continue
		}

		// first gate of validation
		_, err := validations.IsValid(r)
		if err != nil {
			meta.OverallStatus = fileprocessor.StatusFailed
			meta.FailureReason = "Invalid CIF Format"

			failed.Records = append(failed.Records, meta)
			failedCount++
		} else {
			success.Records = append(success.Records, meta)
			successCount++ // don't count duplicates
		}
		cacheRows[r.CIFNumber] = r
	}

	return int64(successCount), int64(failedCount)
}

func uploadFileResultToS3(
	ctx context.Context,
	db *sql.DB,
	h *handleGenerateValidationResultFile,
	tag string,
	successFile, failedFile *dto.FileResult,
) {
	// Decode base64 content for success
	fileExtension, err := getFileExtensionFromFileName(successFile.FileName)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed get extention success file:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
	decodedData, err := base64.StdEncoding.DecodeString(successFile.Payload)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed decode payload success file:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	err = h.S3Client.Upload(
		h.AppConfig.S3Config.BucketName,
		h.generateDocumentBucketPath(successFile.FileName),
		bytes.NewReader(decodedData),
		s3client.WithContentTypeForUpload(mime.TypeByExtension(fmt.Sprintf(".%s", fileExtension))),
	)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed upload success document:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	// Decode base64 content for failed
	fileExtension, err = getFileExtensionFromFileName(failedFile.FileName)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed get extention failed file:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
	decodedData, err = base64.StdEncoding.DecodeString(failedFile.Payload)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed decode payload success file:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}

	err = h.S3Client.Upload(
		h.AppConfig.S3Config.BucketName,
		h.generateDocumentBucketPath(failedFile.FileName),
		bytes.NewReader(decodedData),
		s3client.WithContentTypeForUpload(mime.TypeByExtension(fmt.Sprintf(".%s", fileExtension))),
	)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed upload failed document:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
}

func insertValidationResultToDatabase(
	ctx context.Context,
	db *sql.DB,
	h *handleGenerateValidationResultFile,
	tag string,
	successFile, failedFile *dto.FileResult,
	records, successCount, failedCount int64) {
	tx, err := db.Begin()
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to insert result document:"+err.Error())
		updateDocumentFailed(ctx, db, h.Document, tag)
	}
	// nolint:errcheck
	defer tx.Rollback()

	err = insertDocument(ctx, tx, h, successFile, successCount, tag, "Success")
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to insert Success document:"+err.Error())
	}

	err = insertDocument(ctx, tx, h, failedFile, failedCount, tag, "Failed")
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to insert Failed document:"+err.Error())
	}

	h.Document.Count = sql.NullInt64{Int64: records, Valid: true}
	h.Document.ValidationStatus = sql.NullString{String: "SUCCESS", Valid: true}
	err = storage.UpdateDocumentsForValidation(ctx, db, h.Document)
	if err != nil {
		slog.FromContext(ctx).Error(tag, "failed to update parent document:"+err.Error())
	}

	// nolint:errcheck,gosec
	tx.Commit()
}

func insertDocument(ctx context.Context, db *sql.Tx, h *handleGenerateValidationResultFile, file *dto.FileResult, count int64, tag, types string) error {
	_, err := storage.CreateDocument(ctx, db, &storage.DocumentDTO{
		CreatedAt:        sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:        sql.NullInt64{},
		Name:             file.FileName,
		URL:              fmt.Sprintf("%s/%s/%s", h.AppConfig.S3Config.BucketURL, h.AppConfig.S3Config.Directory, file.FileName),
		Type:             sql.NullString{String: fmt.Sprintf("Bulk Validation %s", types), Valid: true},
		Count:            sql.NullInt64{Int64: count, Valid: true},
		ParentDocumentID: sql.NullInt64{Int64: h.Document.ID, Valid: true},
	})
	if err != nil {
		slog.FromContext(ctx).Error(tag, fmt.Sprintf("failed to insert %s document:", types)+err.Error())
		return err
	}

	return nil
}
