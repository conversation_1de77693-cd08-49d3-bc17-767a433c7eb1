// Package generatevalidationresultfile provides the background process to generate validation result file.
package generatevalidationresultfileV2

import (
	"testing"

	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

func Test_handleGenerateValidationResultFile_Handle(t *testing.T) {
	type fields struct {
		Document    *storage.DocumentDTO
		AppConfig   *config.AppConfig
		S3Client    s3client.S3
		RedisClient redis.Client
		Type        string
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "error failed to post bulk file validation status",
			fields: fields{
				Document: &storage.DocumentDTO{
					ID:   1,
					Name: "test.csv",
				},
				AppConfig: &config.AppConfig{},
			},
		},
		{
			name: "success with all operations",
			fields: fields{
				Document: &storage.DocumentDTO{
					ID:   1,
					Name: "test.csv",
				},
				AppConfig: &config.AppConfig{},
			},
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			h := &handleGenerateValidationResultFile{
				Document:    tt.fields.Document,
				AppConfig:   tt.fields.AppConfig,
				S3Client:    new(s3client.MockS3),
				RedisClient: mocks.NewClient(t),
				Type:        fileprocessor.WriteOffFileProcessorUsecaseV2,
			}
			h.Handle()
		})
	}
}

// nolint:gocognit
func TestNew(t *testing.T) {
	type args struct {
		Document    *storage.DocumentDTO
		AppConfig   *config.AppConfig
		S3Client    s3client.S3
		RedisClient redis.Client
		Type        string
	}
	tests := []struct {
		name string
		args args
		want background.Handler
	}{
		{
			name: "successfully create a new handler",
			args: args{
				Document: &storage.DocumentDTO{
					ID:   1,
					Name: "test.csv",
				},
				AppConfig: &config.AppConfig{},
			},
			want: &handleGenerateValidationResultFile{
				Document: &storage.DocumentDTO{
					ID:   1,
					Name: "test.csv",
				},
				AppConfig: &config.AppConfig{},
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			got := New(tt.args.Document, tt.args.AppConfig, tt.args.S3Client, tt.args.RedisClient, tt.args.Type)

			// Check that we got the right type
			handler, ok := got.(*handleGenerateValidationResultFile)
			if !ok {
				t.Errorf("New() returned wrong type, expected *handleGenerateValidationResultFile, got %T", got)
				return
			}

			// Check individual fields
			if handler.Document != tt.args.Document {
				t.Errorf("New() Document = %v, want %v", handler.Document, tt.args.Document)
			}

			// For AppConfig, just check it's the same pointer
			if handler.AppConfig != tt.args.AppConfig {
				t.Errorf("New() AppConfig = %p, want %p", handler.AppConfig, tt.args.AppConfig)
			}
		})
	}
}
