// Package fileprocessor ...
//
// nolint: errcheck, gosec
package fileprocessor

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	redisLib "github.com/redis/go-redis/v9"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/dto"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/file"
)

// NewBulkWriteOffFileProcessor creates a new instance of the write off file processor
func NewBulkWriteOffFileProcessor() *WriteOffFileProcessor {
	return &WriteOffFileProcessor{
		CoreFileProcessor: &core{
			Usecase:      WriteOffFileProcessorUsecase,
			TimeoutInSec: 300,            // 5 minutes default
			RecordTTL:    time.Hour * 24, // 1 day default
		}, // Initialize the embedded struct
	}
}

// NewBulkWriteOffFileProcessor creates a new instance of the write off file processor
func NewBulkWriteOffFileProcessorV2() *WriteOffFileProcessor {
	return &WriteOffFileProcessor{
		CoreFileProcessor: &core{
			Usecase:      WriteOffFileProcessorUsecaseV2,
			TimeoutInSec: 300,            // 5 minutes default
			RecordTTL:    time.Hour * 24, // 1 day default
		}, // Initialize the embedded struct
	}
}

func NewBulkWaiveOffFileProcessorV2() *WriteOffFileProcessor {
	return &WriteOffFileProcessor{
		CoreFileProcessor: &core{
			Usecase:      WaiveOffFileProcessorUsecaseV2,
			TimeoutInSec: 300,            // 5 minutes default
			RecordTTL:    time.Hour * 24, // 1 day default
		}, // Initialize the embedded struct
	}
}

// WithTimeoutInSec sets the timeout in seconds
func (w *WriteOffFileProcessor) WithTimeoutInSec(timeoutInSec int) *WriteOffFileProcessor {
	if w.CoreFileProcessor == nil {
		w.CoreFileProcessor = &core{
			Usecase:      WriteOffFileProcessorUsecase,
			TimeoutInSec: timeoutInSec,
		}
	} else {
		w.CoreFileProcessor.(*core).TimeoutInSec = timeoutInSec
	}
	return w
}

// WithRecordTTL sets the record TTL in seconds
func (w *WriteOffFileProcessor) WithRecordTTL(ttlInSec int) *WriteOffFileProcessor {
	if w.CoreFileProcessor == nil {
		w.CoreFileProcessor = &core{
			Usecase:   WriteOffFileProcessorUsecase,
			RecordTTL: time.Duration(ttlInSec) * time.Second,
		}
	} else {
		w.CoreFileProcessor.(*core).RecordTTL = time.Duration(ttlInSec) * time.Second
	}
	return w
}

// WithCache sets the cache
func (w *WriteOffFileProcessor) WithCache(rc redis.Client) *WriteOffFileProcessor {
	if w.CoreFileProcessor == nil {
		w.CoreFileProcessor = &core{
			Usecase: WriteOffFileProcessorUsecase,
			Cache:   rc,
		}
	} else {
		w.CoreFileProcessor.(*core).Cache = rc
	}

	w.Cache = rc
	return w
}

// WithDB sets the database
func (w *WriteOffFileProcessor) WithDB(db *sql.DB) *WriteOffFileProcessor {
	w.DB = db
	return w
}

// WriteOffFileProcessor is the processor for the write off file
type WriteOffFileProcessor struct {
	CoreFileProcessor

	Records []interface{}
	Cache   redis.Client
	DB      *sql.DB
}

// LoadFromCSV loads the records from the CSV file
func (w *WriteOffFileProcessor) LoadFromCSV(ctx context.Context, payload []byte) ([]WriteOffFileRequestDTO, error) {
	obj, err := file.ParseCSVToStructs[WriteOffFileRequestDTO](payload)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return obj, nil
}

// Stage stages the records in the cache
func (w *WriteOffFileProcessor) Stage(ctx context.Context, fileName string, m map[string]interface{}, l []string, rowCount int) error {
	// if previously staged then need to delete the record first
	staged, _ := w.Cache.GetBool(ctx, w.MakeCacheKey(fileName, suffixStaged))
	if staged {
		w.Cache.Delete(ctx, w.MakeCacheKey(fileName, suffixRecord))
	}
	// set records
	w.Cache.HSet(ctx, w.MakeCacheKey(fileName, suffixRecord), m)

	err := w.Cache.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.Expire(ctx, w.MakeCacheKey(fileName, suffixRecord), w.GetRecordTTL())

		// set eligible cif list
		b, _ := json.Marshal(l)
		pipe.Set(ctx, w.MakeCacheKey(fileName, suffixList), b, w.GetRecordTTL())

		// set the counter
		pipe.Set(ctx, w.MakeCacheKey(fileName, suffixCounter), 0, w.GetRecordTTL())
		// set the expected count
		pipe.Set(ctx, w.MakeCacheKey(fileName, suffixExpect), rowCount, w.GetRecordTTL())

		// set the flag to execute
		pipe.Set(ctx, w.MakeCacheKey(fileName, suffixStaged), true, w.GetRecordTTL())

		return nil
	})
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to stage the records: "+err.Error())
	}

	return nil
}

// GenerateValidationResultFile generates the validation result file. This ASSUMES that the records are ALREADY FILLED IN w.Records
func (w *WriteOffFileProcessor) GenerateValidationResultFile(ctx context.Context, fileName string, verdict string) (dto.FileResult, error) {
	var b []byte
	var err error

	if verdict == "success" {
		rows := []WriteOffSuccessValidationFileResult{}
		for _, r := range w.Records {
			dto := r.(WriteOffDTO)
			rows = append(rows, WriteOffSuccessValidationFileResult{
				CIFNumber: dto.CIFNumber,
			})
		}
		b, err = file.ExportStructsToCSV(rows)
	} else {
		rows := []WriteOffFailedValidationFileResult{}
		for _, r := range w.Records {
			dto := r.(WriteOffDTO)
			rows = append(rows, WriteOffFailedValidationFileResult{
				CIFNumber: dto.CIFNumber,
				Message:   dto.FailureReason,
			})
		}
		b, err = file.ExportStructsToCSV(rows)
	}

	if err != nil {
		return dto.FileResult{}, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return dto.FileResult{
		FileName: w.renameFile(fileName, "validation-"+verdict),
		Payload:  base64.StdEncoding.EncodeToString(b),
		Count:    len(w.Records),
	}, nil
}

func (w *WriteOffFileProcessor) renameFile(fileName string, suffix string) string {
	ext := filepath.Ext(fileName)             // Get file extension (.csv)
	name := strings.TrimSuffix(fileName, ext) // Remove extension from filename
	return fmt.Sprintf("%s-%s%s", name, suffix, ext)
}

// GenerateExecutionResultFile generates the execution result file. This ASSUMES that the records are ALREADY FILLED IN w.Records
func (w *WriteOffFileProcessor) GenerateExecutionResultFile(ctx context.Context, fileName string) (dto.FileResult, error) {
	rows := []WriteOffExecutionFileResult{}
	for _, r := range w.Records {
		dto := r.(WriteOffDTO)
		rows = append(rows, WriteOffExecutionFileResult{
			CIFNumber:        dto.CIFNumber,
			LoanAcceleration: dto.LoanAccelerationStatus,
			WriteOff:         dto.WriteOffStatus,
			WaiveOff:         dto.WaiveOffStatus,
			CloseLOC:         dto.CloseLOCStatus,
			FailedReason:     dto.FailureReason,
		})
	}
	b, err := file.ExportStructsToCSV(rows)
	if err != nil {
		return dto.FileResult{}, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return dto.FileResult{
		FileName: w.renameFile(fileName, "result"),
		Payload:  base64.StdEncoding.EncodeToString(b),
		Count:    len(rows),
	}, nil
}

// CompileExecutionResult compiles the execution result
func (w *WriteOffFileProcessor) CompileExecutionResult(ctx context.Context, name string) dto.FileResponse {
	slog.FromContext(ctx).Info(TagBulkFileProcessor, "compiling execution result")

	fileName := w.GetStagedFileName(ctx, name)
	if fileName == "" {
		return dto.FileResponse{
			Status:  StatusFailed,
			Message: "failed to get file name",
		}
	}
	list := w.GetStagedList(ctx, fileName)

	var cmder *redisLib.MapStringStringCmd
	err := w.Cache.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		cmder = pipe.HGetAll(ctx, w.MakeCacheKey(fileName, suffixRecord))
		return nil
	})
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get records: "+err.Error())
		return dto.FileResponse{
			Status:  StatusFailed,
			Message: "failed to get records: " + err.Error(),
		}
	}

	mapStr := cmder.Val()
	for _, cif := range list {
		bs, ok := mapStr[cif]
		if !ok {
			slog.FromContext(ctx).Error(TagBulkFileProcessor, "record is missing for cif="+cif)
			continue
		}

		var dto WriteOffDTO
		jsonErr := json.Unmarshal([]byte(bs), &dto)
		if jsonErr != nil {
			slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to unmarshal record: "+jsonErr.Error())
		}
		w.Records = append(w.Records, dto)
	}

	fileResult, err := w.GenerateExecutionResultFile(ctx, fileName)
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to generate the execution result file: "+err.Error())
		return dto.FileResponse{
			Status:  StatusFailed,
			Message: "failed to generate the execution result file: " + err.Error(),
		}
	}

	return dto.FileResponse{
		SourceFileName: fileName,
		Type:           WriteOffFileProcessorUsecase,
		Status:         StatusSuccess,
		Message:        "success",
		Result:         map[string]dto.FileResult{"execution": fileResult},
	}
}

// BeginExecution locks the execution and sets the timeout
func (w *WriteOffFileProcessor) BeginExecution(ctx context.Context, fileName string) error {
	// lock the execution
	err := w.Lock(ctx, fileName)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to lock the execution: "+err.Error())
	}

	// set filename that is being executed
	err = w.SetStagedFileName(ctx, fileName)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to set the staged file name: "+err.Error())
	}

	// set the timeout
	ok, err := w.Cache.Set(ctx, w.MakeCacheKey(fileName, suffixTimeout), w.calculateTimeout(), w.GetRecordTTL())
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to set the timeout: "+err.Error())
	}
	if !ok {
		return errorwrapper.Error(apiError.InternalServerError, "failed to set the timeout")
	}

	return nil
}

// UpdateRowStatus updates the row status
//
// nolint: funlen, dupl
func (w *WriteOffFileProcessor) UpdateRowStatus(ctx context.Context, cif string, dto WriteOffDTO, raw map[string]string, name string) error {
	if w.DB != nil {
		_, err := storage.CreateBulkFileProcessorDumpRow(ctx, w.DB, &storage.BulkFileProccessorDumpDTO{
			Identifier:     dto.CIFNumber,
			IdentifierType: constants.CifNumberIdentifierType,
			Type:           WriteOffFileProcessorUsecase,
			Status:         dto.OverallStatus,
			Data:           raw,
			ReferenceID:    sql.NullString{String: dto.ReferenceID, Valid: true},
		})
		if err != nil {
			slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to create bulk file processor dump row: "+err.Error())
		}
	} else {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "DB is not set, skipping the update")
	}

	// update only if file processor is locked
	if !w.IsLocked(ctx, name) {
		slog.FromContext(ctx).Info(constants.LendingActionStatusKafkaTag, "File processor is not locked, skipping event...")
		return nil
	}

	fileName := w.GetStagedFileName(ctx, name)
	if fileName == "" {
		return errorwrapper.Error(apiError.InternalServerError, "failed to get file name")
	}

	b, _ := json.Marshal(dto)

	var cmdIncr *redisLib.IntCmd
	var cmdExpect *redisLib.StringCmd

	isNew, err := w.Cache.SAdd(ctx, w.MakeCacheKey(fileName, suffixProcessed), dto.ReferenceID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to add to processed set: "+err.Error())
	}

	err = w.Cache.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.HSet(ctx, w.MakeCacheKey(fileName, suffixRecord), map[string]interface{}{cif: b})

		if isNew {
			cmdIncr = pipe.Incr(ctx, w.MakeCacheKey(fileName, suffixCounter))
		}
		cmdExpect = pipe.Get(ctx, w.MakeCacheKey(fileName, suffixExpect))

		return nil
	})
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to update the row status: "+err.Error())
	}

	// Execute `GET` after pipeline execution
	expect, _ := strconv.Atoi(cmdExpect.Val())

	if isNew && cmdIncr.Val() == int64(expect) {
		_, err = w.Cache.Set(ctx, w.MakeCacheKey(fileName, suffixDone), true, w.GetRecordTTL())
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, "failed to set done status: "+err.Error())
		}
	}

	return nil
}

// IsExecutionDone checks if the execution is done or timeout
func (w *WriteOffFileProcessor) IsExecutionDone(ctx context.Context, name string) bool {
	fileName := w.GetStagedFileName(ctx, name)
	if fileName == "" {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get file name")
		return false
	}

	// check if all records are done
	isDone, err := w.Cache.GetBool(ctx, w.MakeCacheKey(fileName, suffixDone))
	if err != nil {
		if err == redis.ErrNoData {
			slog.FromContext(ctx).Info(TagBulkFileProcessor, "execution is not done: "+err.Error())
		}
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get done status: "+err.Error())
	}

	// check if timeout
	timeoutStr, err := w.Cache.GetString(ctx, w.MakeCacheKey(suffixTimeout))
	if err != nil {
		if err == redis.ErrNoData {
			slog.FromContext(ctx).Info(TagBulkFileProcessor, "timeout is not set: "+err.Error())
		}
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get timeout: "+err.Error())
	}

	timeout, err := time.Parse(time.RFC3339, timeoutStr)
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to parse timeout: "+err.Error())
	}
	isTimeout := timeout.Before(time.Now())
	return isDone || isTimeout
}

// GetStagedList gets the list of staged records (by identifier)
func (w *WriteOffFileProcessor) GetStagedList(ctx context.Context, fileName string) []string {
	b, _ := w.Cache.GetBytes(ctx, w.MakeCacheKey(fileName, suffixList))
	var list []string
	_ = json.Unmarshal(b, &list)
	return list
}

// PostExecutionCleanup cleans up the cache after execution
func (w *WriteOffFileProcessor) PostExecutionCleanup(ctx context.Context, name string) {
	fileName := w.GetStagedFileName(ctx, name)
	if fileName == "" {
		return
	}

	w.Cache.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixRecord))
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixList))
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixCounter))
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixExpect))
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixStaged))
		pipe.Del(ctx, w.MakeCacheKey(fileName, suffixDone))
		pipe.Del(ctx, w.MakeCacheKey(suffixFileName))
		pipe.Del(ctx, w.MakeCacheKey(suffixTimeout))
		pipe.Del(ctx, w.MakeCacheKey(suffixLock))
		return nil
	})
}

func (w *WriteOffFileProcessor) calculateTimeout() time.Time {
	return time.Now().Add(time.Duration(w.GetTimeoutInSec()) * time.Second)
}
