package fileprocessor

const (
	// StatusSuccess ...
	StatusSuccess = "Success"
	// StatusFailed ...
	StatusFailed = "Failed"
	// StatusRequestCreated ...
	StatusRequestCreated = "Request Created"
	// StatusPending ...
	StatusPending = "Pending"
	// StatusNotProcessed ...
	StatusNotProcessed = "Not Processed"
	// StatusInvalid ...
	StatusInvalid = "Invalid"

	// FieldLoanAccelerationStatus is the field from lending payload kafka
	FieldLoanAccelerationStatus = "accelerationStatus"

	// FieldWriteOffStatus is the field from lending payload kafka
	FieldWriteOffStatus = "writeOffStatus"

	// FieldWaiveOffStatus is the field from lending payload kafka
	FieldWaiveOffStatus = "waiveOffStatus"

	// FieldCloseLOCStatus is the field from lending payload kafka
	FieldCloseLOCStatus = "locClosure"

	// FieldFailureReason is the field from lending payload kafka
	FieldFailureReason = "statusReason"

	suffixExpect    = "expect"
	suffixCounter   = "counter"
	suffixList      = "list"
	suffixRecord    = "record"
	suffixStaged    = "staged"
	suffixDone      = "done"
	suffixTimeout   = "timeout"
	suffixFileName  = "fileName"
	suffixLock      = "lock"
	suffixProcessed = "processed"
)
