package fileprocessor

// WriteOffDTO is the compact to store the status to redis
type WriteOffDTO struct {
	CIFNumber              string `json:"cif"`
	OverallStatus          string `json:"st"`
	LoanAccelerationStatus string `json:"lcc"`
	WriteOffStatus         string `json:"wro"`
	WaiveOffStatus         string `json:"wao"`
	CloseLOCStatus         string `json:"cls"`
	FailureReason          string `json:"msg"`
	ReferenceID            string `json:"ref,omitempty"`
}

// WriteOffFileRequestDTO is the DTO for the write off file
type WriteOffFileRequestDTO struct {
	CIFNumber string `column:"CIF Number" validate:"cif"`
}

// WriteOffValidationFileResult is the result of the validation
type WriteOffValidationFileResult struct {
	CIFNumber string `column:"CIF Number"`
	Status    string `column:"Status"`
	Message   string `column:"Failed reason"`
}

// WriteOffSuccessValidationFileResult is the result of successful validation
type WriteOffSuccessValidationFileResult struct {
	CIFNumber string `column:"CIF Number"`
}

// WriteOffFailedValidationFileResult is the result of failed validation
type WriteOffFailedValidationFileResult struct {
	CIFNumber string `column:"CIF Number"`
	Message   string `column:"Failed reason"`
}

// WriteOffExecutionFileResult is the result of the execution
type WriteOffExecutionFileResult struct {
	CIFNumber        string `column:"CIF Number"`
	LoanAcceleration string `column:"Loan Acceleration"`
	WriteOff         string `column:"Write Off"`
	WaiveOff         string `column:"Waive Off"`
	CloseLOC         string `column:"Close LOC"`
	FailedReason     string `column:"Failed reason"`
}
