package fileprocessor

import (
	"context"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	// TagBulkFileProcessor is the tag for bulk file processor
	TagBulkFileProcessor = "tagBulkFileProcessor"

	cacheBaseKey = "fp"

	// WriteOffFileProcessorUsecase is the usecase for write off file processor
	WriteOffFileProcessorUsecase = "WRITE_OFF"

	// WriteOffFileProcessorUsecaseV2 is the usecase for write off file processor
	WriteOffFileProcessorUsecaseV2 = "WRITE_OFF_V2"

	// WaiveOffProcessorUsecaseV2 is the usecase for waive off file processor
	WaiveOffFileProcessorUsecaseV2 = "WAIVE_OFF_V2"
)

// CoreFileProcessor is the interface for core file processor
type CoreFileProcessor interface {
	IsLocked(ctx context.Context, fileName string) bool
	Lock(ctx context.Context, fileName string) error
	Unlock(ctx context.Context, fileName string) error
	GetStagedFileName(ctx context.Context, fileName string) string
	SetStagedFileName(ctx context.Context, fileName string) error
	MakeCacheKey(args ...string) string
	GetRecordTTL() time.Duration
	GetTimeoutInSec() int
}

//go:generate mockery --name CoreFileProcessor --output ./mocks --filename mock_core_file_processor.go

type core struct {
	Usecase      string
	TimeoutInSec int // Timeout in seconds for the execution
	RecordTTL    time.Duration
	Cache        redis.Client `inject:"client.redisClient"`
	FileName     string
}

// GetRecordTTL returns the TTL for the records
func (c *core) GetRecordTTL() time.Duration {
	if c.RecordTTL == 0 {
		return time.Hour * 24 // default to 24 hours
	}
	return c.RecordTTL
}

// GetTimeoutInSec returns the timeout in seconds for the execution
func (c *core) GetTimeoutInSec() int {
	if c.TimeoutInSec == 0 {
		return 60 * 10 // default to 10 minutes
	}
	return c.TimeoutInSec
}

// IsLocked checks if the bulk file processor is locked
func (c *core) IsLocked(ctx context.Context, fileName string) bool {
	lock, err := c.Cache.GetBool(ctx, c.MakeCacheKey(fileName, suffixLock))
	if err != nil {
		if err == redis.ErrNoData {
			return false
		}
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get lock status:"+err.Error())
		return true // assume locked
	}
	return lock
}

// Lock locks the bulk file processor
func (c *core) Lock(ctx context.Context, fileName string) error {
	// 10 min for compiling file buffer
	defaultLockTTLBuffer := 10 * time.Minute

	// lock the execution
	res, err := c.Cache.SetNX(ctx, c.MakeCacheKey(fileName, suffixLock), true, time.Duration(c.TimeoutInSec)*time.Second+defaultLockTTLBuffer)
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to lock:"+err.Error())
		return errorwrapper.WrapError(err, apiError.Idem, "failed to lock")
	}
	if !res {
		slog.FromContext(ctx).Info(TagBulkFileProcessor, "lock already exists")
		return errorwrapper.Error(apiError.InternalServerError, "lock already exists")
	}
	return nil
}

// Unlock unlocks the bulk file processor
func (c *core) Unlock(ctx context.Context, fileName string) error {
	// unlock the execution
	res, err := c.Cache.Delete(ctx, c.MakeCacheKey(fileName, suffixLock))
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to unlock:"+err.Error())
		return errorwrapper.WrapError(err, apiError.Idem, "failed to unlock")
	}
	if !res {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to unlock")
		return errorwrapper.Error(apiError.InternalServerError, "failed to unlock")
	}
	return nil
}

// GetStagedRecords gets the staged records
func (c *core) GetStagedFileName(ctx context.Context, fileName string) string {
	s, err := c.Cache.GetString(ctx, c.MakeCacheKey(fileName, suffixFileName))
	if err != nil {
		slog.FromContext(ctx).Error(TagBulkFileProcessor, "failed to get staged file name:"+err.Error())
		return ""
	}
	return s
}

// SetStagedFileName sets the staged file name
func (c *core) SetStagedFileName(ctx context.Context, fileName string) error {
	_, err := c.Cache.Set(ctx, c.MakeCacheKey(fileName, suffixFileName), fileName, c.RecordTTL)
	return err
}

// MakeCacheKey makes the cache key separated by colon
func (c *core) MakeCacheKey(args ...string) string {
	base := cacheBaseKey + ":" + c.Usecase
	for _, a := range args {
		base += ":" + a
	}
	return base
}
