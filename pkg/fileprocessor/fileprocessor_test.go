package fileprocessor

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
)

func Test_core_IsLocked(t *testing.T) {
	clientMock := mocks.NewClient(t)
	ctx := context.Background()

	type fields struct {
		Usecase string
		Cache   redis.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		want     bool
		mockFunc func()
	}{
		{
			name: "success - locked",
			fields: fields{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: args{
				ctx: ctx,
			},
			want: true,
			mockFunc: func() {
				clientMock.On("GetBool", ctx, "fp:TEST:lock").Return(true, nil).Once()
			},
		},
		{
			name: "success - not locked",
			fields: fields{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: args{
				ctx: ctx,
			},
			want: false,
			mockFunc: func() {
				clientMock.On("GetBool", ctx, "fp:TEST:lock").Return(false, nil).Once()
			},
		},
		{
			name: "error - no data",
			fields: fields{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: args{
				ctx: ctx,
			},
			want: false,
			mockFunc: func() {
				clientMock.On("GetBool", ctx, "fp:TEST:lock").Return(false, redis.ErrNoData).Once()
			},
		},
		{
			name: "error - other error",
			fields: fields{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: args{
				ctx: ctx,
			},
			want: true,
			mockFunc: func() {
				clientMock.On("GetBool", ctx, "fp:TEST:lock").Return(false, errors.New("some error")).Once()
			},
		},
	}
	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			c := &core{
				Usecase: tt.fields.Usecase,
				Cache:   tt.fields.Cache,
			}
			if tt.mockFunc != nil {
				tt.mockFunc()
			}
			if got := c.IsLocked(tt.args.ctx, tt.name); got != tt.want {
				t.Errorf("core.IsLocked() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_core_Lock(t *testing.T) {
	clientMock := mocks.NewClient(t)
	ctx := context.Background()

	tests := []struct {
		name   string
		fields struct {
			Usecase string
			Cache   redis.Client
		}
		args     struct{ ctx context.Context }
		wantErr  bool
		mockFunc func()
	}{
		{
			name: "success",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct{ ctx context.Context }{ctx: ctx},
			mockFunc: func() {
				clientMock.On("SetNX", ctx, "fp:TEST:lock", true, mock.Anything).Return(true, nil).Once()
			},
		},
		{
			name: "error - failed to set lock",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args:    struct{ ctx context.Context }{ctx: ctx},
			wantErr: true,
			mockFunc: func() {
				clientMock.On("SetNX", ctx, "fp:TEST:lock", true, mock.Anything).Return(false, errors.New("redis error")).Once()
			},
		},
		{
			name: "error - lock already exists",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args:    struct{ ctx context.Context }{ctx: ctx},
			wantErr: true,
			mockFunc: func() {
				clientMock.On("SetNX", ctx, "fp:TEST:lock", true, mock.Anything).Return(false, nil).Once()
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			c := &core{
				Usecase: tt.fields.Usecase,
				Cache:   tt.fields.Cache,
			}
			if tt.mockFunc != nil {
				tt.mockFunc()
			}
			err := c.Lock(tt.args.ctx, tt.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("core.Lock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_core_Unlock(t *testing.T) {
	clientMock := mocks.NewClient(t)
	ctx := context.Background()

	tests := []struct {
		name   string
		fields struct {
			Usecase string
			Cache   redis.Client
		}
		args     struct{ ctx context.Context }
		wantErr  bool
		mockFunc func()
	}{
		{
			name: "success",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct{ ctx context.Context }{ctx: ctx},
			mockFunc: func() {
				clientMock.On("Delete", ctx, "fp:TEST:lock").Return(true, nil).Once()
			},
		},
		{
			name: "error - redis error",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args:    struct{ ctx context.Context }{ctx: ctx},
			wantErr: true,
			mockFunc: func() {
				clientMock.On("Delete", ctx, "fp:TEST:lock").Return(false, errors.New("redis error")).Once()
			},
		},
		{
			name: "error - lock not found",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args:    struct{ ctx context.Context }{ctx: ctx},
			wantErr: true,
			mockFunc: func() {
				clientMock.On("Delete", ctx, "fp:TEST:lock").Return(false, nil).Once()
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			c := &core{
				Usecase: tt.fields.Usecase,
				Cache:   tt.fields.Cache,
			}
			if tt.mockFunc != nil {
				tt.mockFunc()
			}
			err := c.Unlock(tt.args.ctx, tt.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("core.Unlock() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_core_GetStagedFileName(t *testing.T) {
	clientMock := mocks.NewClient(t)
	ctx := context.Background()

	tests := []struct {
		name   string
		fields struct {
			Usecase string
			Cache   redis.Client
		}
		args     struct{ ctx context.Context }
		want     string
		mockFunc func()
	}{
		{
			name: "success",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct{ ctx context.Context }{ctx: ctx},
			want: "test.csv",
			mockFunc: func() {
				clientMock.On("GetString", ctx, "fp:TEST:fileName").Return("test.csv", nil).Once()
			},
		},
		{
			name: "error - redis error",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct{ ctx context.Context }{ctx: ctx},
			want: "",
			mockFunc: func() {
				clientMock.On("GetString", ctx, "fp:TEST:fileName").Return("", errors.New("redis error")).Once()
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			c := &core{
				Usecase: tt.fields.Usecase,
				Cache:   tt.fields.Cache,
			}
			if tt.mockFunc != nil {
				tt.mockFunc()
			}
			got := c.GetStagedFileName(tt.args.ctx, tt.name)
			if got != tt.want {
				t.Errorf("core.GetStagedFileName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_core_SetStagedFileName(t *testing.T) {
	clientMock := mocks.NewClient(t)
	ctx := context.Background()

	tests := []struct {
		name   string
		fields struct {
			Usecase string
			Cache   redis.Client
		}
		args struct {
			ctx      context.Context
			fileName string
		}
		wantErr  bool
		mockFunc func()
	}{
		{
			name: "success",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct {
				ctx      context.Context
				fileName string
			}{
				ctx:      ctx,
				fileName: "test.csv",
			},
			mockFunc: func() {
				clientMock.On("Set", ctx, "fp:TEST:fileName", "test.csv", mock.Anything).Return(true, nil).Once()
			},
		},
		{
			name: "error - redis error",
			fields: struct {
				Usecase string
				Cache   redis.Client
			}{
				Usecase: "TEST",
				Cache:   clientMock,
			},
			args: struct {
				ctx      context.Context
				fileName string
			}{
				ctx:      ctx,
				fileName: "test.csv",
			},
			wantErr: true,
			mockFunc: func() {
				clientMock.On("Set", ctx, "fp:TEST:fileName", "test.csv", mock.Anything).Return(false, errors.New("redis error")).Once()
			},
		},
	}

	for _, test := range tests {
		tt := test
		t.Run(tt.name, func(t *testing.T) {
			c := &core{
				Usecase: tt.fields.Usecase,
				Cache:   tt.fields.Cache,
			}
			if tt.mockFunc != nil {
				tt.mockFunc()
			}
			err := c.SetStagedFileName(tt.args.ctx, tt.args.fileName)
			if (err != nil) != tt.wantErr {
				t.Errorf("core.SetStagedFileName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
