// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// CoreFileProcessor is an autogenerated mock type for the CoreFileProcessor type
type CoreFileProcessor struct {
	mock.Mock
}

// GetRecordTTL provides a mock function with no fields
func (_m *CoreFileProcessor) GetRecordTTL() time.Duration {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRecordTTL")
	}

	var r0 time.Duration
	if rf, ok := ret.Get(0).(func() time.Duration); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(time.Duration)
	}

	return r0
}

// GetStagedFileName provides a mock function with given fields: ctx, fileName
func (_m *CoreFileProcessor) GetStagedFileName(ctx context.Context, fileName string) string {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for GetStagedFileName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// GetTimeoutInSec provides a mock function with no fields
func (_m *CoreFileProcessor) GetTimeoutInSec() int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimeoutInSec")
	}

	var r0 int
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// IsLocked provides a mock function with given fields: ctx, fileName
func (_m *CoreFileProcessor) IsLocked(ctx context.Context, fileName string) bool {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for IsLocked")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Lock provides a mock function with given fields: ctx, fileName
func (_m *CoreFileProcessor) Lock(ctx context.Context, fileName string) error {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for Lock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MakeCacheKey provides a mock function with given fields: args
func (_m *CoreFileProcessor) MakeCacheKey(args ...string) string {
	_va := make([]interface{}, len(args))
	for _i := range args {
		_va[_i] = args[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for MakeCacheKey")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(...string) string); ok {
		r0 = rf(args...)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// SetStagedFileName provides a mock function with given fields: ctx, fileName
func (_m *CoreFileProcessor) SetStagedFileName(ctx context.Context, fileName string) error {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for SetStagedFileName")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Unlock provides a mock function with given fields: ctx, fileName
func (_m *CoreFileProcessor) Unlock(ctx context.Context, fileName string) error {
	ret := _m.Called(ctx, fileName)

	if len(ret) == 0 {
		panic("no return value specified for Unlock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, fileName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewCoreFileProcessor creates a new instance of CoreFileProcessor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCoreFileProcessor(t interface {
	mock.TestingT
	Cleanup(func())
}) *CoreFileProcessor {
	mock := &CoreFileProcessor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
