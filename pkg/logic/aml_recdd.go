package logic

// ReCDDRequest ...
type ReCDDRequest struct {
	TicketDetails   TicketDetail   `json:"ticketDetail"`
	CustomerDetails CustomerDetail `json:"customerDetail"`
}

type CustomerDetail struct {
	CustomerSafeID     string `json:"customerSafeID"`
	CustomerNIK        string `json:"customerNIK"`
	CustomerName       string `json:"customerName"`
	CustomerType       string `json:"customerType"`
	Occupation         string `json:"occupation"`
	Industry           string `json:"industry"`
	JobPosition        string `json:"jobPosition"`
	IncomePerMonth     string `json:"incomePerMonth"`
	SourceOfFunds      string `json:"sourceOfFunds"`
	CustomerRiskRating string `json:"customerRiskRating"`
}

type TicketDetail struct {
	PnTriggerDate            string           `json:"pnTriggerDate"`
	RiskBasedAssessment      []AssessmentData `json:"riskBasedAssessment"`
	ResponseDate             string           `json:"responseDate"`
	PnResponse               bool             `json:"pnResponse"`
	ReviewType               string           `json:"reviewType"`
	CustomerCIF              string           `json:"customerCIF"`
	WaitingPeriod            string           `json:"waitingPeriod"`
	TicketDeadline           string           `json:"ticketDeadline"`
	RiskBasedAssessmentScore string           `json:"riskBasedAssessmentScore"`
}

type AssessmentData struct {
	RBAScore int    `json:"rbaScore"`
	RuleName string `json:"ruleName"`
}
