package logic

// ChangeAccountBlockRequest ...
type ChangeAccountBlockRequest struct {
	Accounts           []ChangeAccountBlock `json:"Accounts" validate:"required"`
	UpdatedBy          string               `json:"UpdatedBy"`
	TicketID           string               `json:"TicketID"`
	SafeID             string               `json:"SafeID"` // Can be removed from the request by get by account id
	IsSendNotification bool                 `json:"IsSendNotification"`
}

type ChangeAccountBlock struct {
	AccountID      string   `json:"AccountID" validate:"account_id"`
	IdempotencyKey string   `json:"IdempotencyKey" validate:"required"`
	HoldCodes      []string `json:"HoldCodes" validate:"block_unblock_hold_codes"`
}
