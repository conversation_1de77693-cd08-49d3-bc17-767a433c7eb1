package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/chatbot"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// Group related types and constants
const (
	chatbotLogTag = "stream.chatbot"
)

// Audit trail related functions
func (p *process) HandleAuditTrail(ctx context.Context, ticketID int64, title, description string) error {
	_, err := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, createAuditTrail(ticketID, title, description))
	if err != nil {
		slog.FromContext(ctx).Error(chatbotLogTag, fmt.Sprintf("error writing audit trail for ticket %d: %v", ticketID, err))
	}
	return err
}

func createAuditTrail(ticketID int64, title, description string) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 1, Valid: true},
		Identifier:     fmt.Sprintf("%d", ticketID),
		IdentifierType: constants.TicketID,
		Title:          title,
		Description:    description,
		ActivityType:   constants.Ticket,
	}
}

// Core operation handler
func (p *process) handleChatbotOperation(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	operation func() error,
	successTitle, successMsg string,
) error {
	// Execute the operation
	if err := operation(); err != nil {
		p.HandleAuditTrail(ctx, ticketDTO.ID, // nolint:errcheck,gosec
			successTitle+" failed",
			fmt.Sprintf("Failed to %s: %s", successMsg, err.Error()))
		slog.FromContext(ctx).Error(chatbotLogTag,
			fmt.Sprintf("error %s: %v", successMsg, err))
		return err
	}

	// Update ticket status
	if err := p.SystemProceedTicketStatus(ctx, ticketDTO.ID); err != nil {
		slog.FromContext(ctx).Error(chatbotLogTag,
			"failed to proceed ticket status", slog.Error(err))
	}

	// Record successful operation
	p.HandleAuditTrail(ctx, ticketDTO.ID, // nolint:errcheck,gosec
		successTitle+" success",
		successMsg+" successfully")
	return nil
}

// Define a type for chatbot operations
type chatbotOperation struct {
	clientFunc   func(context.Context, interface{}) (interface{}, error)
	successTitle string
	successMsg   string
}

// Generic operation executor
func (p *process) executeChatbotOperation(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	req interface{},
	op chatbotOperation,
) error {
	if err := utils.LoadStruct(ticketDTO.Data.Payload, req); err != nil {
		return fmt.Errorf("failed to load payload: %v", err)
	}

	return p.handleChatbotOperation(ctx, ticketDTO,
		func() error {
			_, err := op.clientFunc(ctx, req)
			return err
		},
		op.successTitle,
		op.successMsg)
}

// AddKnowledgeBase adds a new knowledge base entry
func (p *process) AddKnowledgeBase(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.AddKnowledgeBaseRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddKnowledgeBase(ctx, req.(*chatbot.AddKnowledgeBaseRequest))
			},
			successTitle: "Add new knowledge",
			successMsg:   "add new knowledge",
		})
}

// UpdateKnowledgeBase updates an existing knowledge base entry
func (p *process) UpdateKnowledgeBase(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.UpdateKnowledgeBaseRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.UpdateKnowledgeBase(ctx, req.(*chatbot.UpdateKnowledgeBaseRequest))
			},
			successTitle: "Update knowledge",
			successMsg:   "update knowledge",
		})
}

// DeleteKnowledgeBase removes a knowledge base entry from the chatbot system
func (p *process) DeleteKnowledgeBase(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.DeleteKnowledgeBaseRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.DeleteKnowledgeBase(ctx, req.(*chatbot.DeleteKnowledgeBaseRequest))
			},
			successTitle: "Delete knowledge",
			successMsg:   "delete knowledge",
		})
}

// ImportKnowledgeBase imports knowledge base data from a file into the chatbot system
// nolint:dupl
func (p *process) ImportKnowledgeBase(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	if len(ticketDTO.Data.Documents) == 0 {
		return fmt.Errorf("no documents found")
	}
	// get the first document
	document := ticketDTO.Data.Documents[0]

	// download the document
	file, err := p.S3Client.GetObjectWithContext(ctx, p.AppConfig.S3Config.BucketName, "onedash-documents/"+document.Name)
	if err != nil {
		return fmt.Errorf("failed to download document: %v", err)
	}

	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.ImportKnowledgeBaseRequest{
			File:     file,
			FileName: document.Name,
		},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.ImportKnowledgeBase(ctx, req.(*chatbot.ImportKnowledgeBaseRequest))
			},
			successTitle: "Import knowledge",
			successMsg:   "import knowledge",
		})
}

// AddSystemPrompt adds a new system prompt to the chatbot
func (p *process) AddSystemPrompt(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.SystemPromptRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddSystemPrompt(ctx, req.(*chatbot.SystemPromptRequest))
			},
			successTitle: "Add system prompt",
			successMsg:   "add system prompt",
		})
}

// UpdateEscapeKeywords updates the escape keywords used by the chatbot
func (p *process) UpdateEscapeKeywords(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.AddEscapeKeywordsRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddEscapeKeywords(ctx, req.(*chatbot.AddEscapeKeywordsRequest))
			},
			successTitle: "Update escape keywords",
			successMsg:   "update escape keywords",
		})
}

// UpdateChatbotConfig updates the configuration settings for the chatbot system
func (p *process) UpdateChatbotConfig(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.AddChatbotConfigRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddChatbotConfig(ctx, req.(*chatbot.AddChatbotConfigRequest))
			},
			successTitle: "Update chatbot configs",
			successMsg:   "update chatbot configurations",
		})
}

// AddCustomerWhitelist adds a customer to the whitelist
func (p *process) AddCustomerWhitelist(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.AddCustomerWhitelistRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddCustomerWhitelist(ctx, req.(*chatbot.AddCustomerWhitelistRequest))
			},
			successTitle: "Add customer to whitelist",
			successMsg:   "add customer to whitelist",
		})
}

// DeleteCustomerWhitelist removes a customer from the whitelist
func (p *process) DeleteCustomerWhitelist(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.DeleteCustomerWhitelistRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.DeleteCustomerWhitelist(ctx, req.(*chatbot.DeleteCustomerWhitelistRequest))
			},
			successTitle: "Delete customer from whitelist",
			successMsg:   "delete customer from whitelist",
		})
}

// ImportCustomerWhitelist imports customer whitelists from a file into the chatbot system
// nolint:dupl
func (p *process) ImportCustomerWhitelist(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	if len(ticketDTO.Data.Documents) == 0 {
		return fmt.Errorf("no documents found")
	}
	// get the first document
	document := ticketDTO.Data.Documents[0]

	// download the document
	file, err := p.S3Client.GetObjectWithContext(ctx, p.AppConfig.S3Config.BucketName, "onedash-documents/"+document.Name)
	if err != nil {
		return fmt.Errorf("failed to download document: %v", err)
	}

	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.ImportCustomerWhitelistRequest{
			File:     file,
			FileName: document.Name,
		},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.ImportCustomerWhitelist(ctx, req.(*chatbot.ImportCustomerWhitelistRequest))
			},
			successTitle: "Import customer whitelist",
			successMsg:   "import customer whitelist",
		})
}

// AddTag adds a new tag to the chatbot system
func (p *process) AddTag(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.AddTagRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.AddTag(ctx, req.(*chatbot.AddTagRequest))
			},
			successTitle: "Add new tag",
			successMsg:   "add new tag",
		})
}

// UpdateTag updates an existing tag
func (p *process) UpdateTag(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.UpdateTagRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.UpdateTag(ctx, req.(*chatbot.UpdateTagRequest))
			},
			successTitle: "Update tag",
			successMsg:   "update tag",
		})
}

// DeleteTag deletes an existing tag
func (p *process) DeleteTag(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	return p.executeChatbotOperation(ctx, ticketDTO,
		&chatbot.DeleteTagRequest{},
		chatbotOperation{
			clientFunc: func(ctx context.Context, req interface{}) (interface{}, error) {
				return p.ChatbotClient.DeleteTag(ctx, req.(*chatbot.DeleteTagRequest))
			},
			successTitle: "Delete tag",
			successMsg:   "delete tag",
		})
}
