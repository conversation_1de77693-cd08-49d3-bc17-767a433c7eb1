package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	chatbotServiceAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/chatbot"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// structToMap converts a struct to a map[string]string
func structToMap(s interface{}) (map[string]string, error) {
	result := make(map[string]string)
	v := reflect.ValueOf(s)

	if v.Kind() != reflect.Struct {
		return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected a struct, got %s", v.Kind()))
	}

	data, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(data, &result); err != nil {
		return nil, err
	}

	return result, nil
}

// getCurrentAccountHoldCodes is used to get the current account hold codes
func (p *process) getCurrentAccountHoldCodes(ctx context.Context, accountID string) ([]accountServiceAPI.ApplicableHoldcode, error) {
	accountDetails, err := p.AccountServiceClient.GetAccountDetailsByAccountID(ctx, &accountServiceAPI.GetAccountRequest{
		AccountID: accountID,
	})
	if err != nil {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", fmt.Sprintf("error getting account details: %v", err.Error()))
		return nil, errorwrapper.GetHTTPErrorResponse(err, "Failed to get account details")
	}

	str, ok := accountDetails.Account.ProductSpecificParameters[applicableHoldcodesKey]
	if !ok {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", "applicableHoldcodesKey not found")
		return nil, errorwrapper.Error(apiError.InternalServerError, "Failed to get hold codes, invalid response key applicableHoldcodesKey not found")
	}

	var codes []accountServiceAPI.ApplicableHoldcode
	err = json.Unmarshal([]byte(str), &codes)
	if err != nil {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", fmt.Sprintf("error unmarshalling hold codes: %v", err.Error()))
		return nil, errorwrapper.Error(apiError.InternalServerError, "Failed to get hold codes, invalid unmarshall format")
	}

	return codes, nil
}

// QueueFeedback is the feedback for the queue.
// Attach this to the response struct for logic that needs to give feedback to the queue.
type QueueFeedback struct {
	NeedRequeue bool
}

// QueueRequest ...
type QueueRequest struct {
	TicketID string      `json:"ticketID"`
	Payload  interface{} `json:"payload"`
}

// GetQueueLog is to get queue
//
//nolint:dupl
func (p *process) GetQueueLog(ctx context.Context, conditions []commonStorage.QueryCondition) ([]*storage.QueueDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("logic.GetQueueLog", fmt.Sprintf("error getting database handle: %v", err.Error()))
		return nil, err
	}

	queue, err := storage.GetQueue(ctx, db, conditions)
	if err != nil {
		slog.FromContext(ctx).Error("logic.GetQueueLog", fmt.Sprintf("error get queues log: %v", err.Error()))
		return nil, err
	}
	return queue, nil
}

// UpdateQueueLog is to update queue
//
//nolint:dupl
func (p *process) UpdateQueueLog(ctx context.Context, queue *storage.QueueDTO) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("logic.UpdateQueueLog", fmt.Sprintf("error getting database handle: %v", err.Error()))
		return 0, err
	}
	ID, err := storage.UpdateQueue(ctx, db, queue)
	if err != nil {
		slog.FromContext(ctx).Error("logic.UpdateQueueLog", fmt.Sprintf("error update queue log: %v", err.Error()))
		return 0, err
	}
	return ID, err
}

// InsertQueueLog is to insert queue
//
//nolint:dupl
func (p *process) InsertQueueLog(ctx context.Context, queue *storage.QueueDTO) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("logic.InsertQueueLog", fmt.Sprintf("error getting database handle: %v", err.Error()))
		return 0, err
	}
	ID, err := storage.CreateQueue(ctx, db, queue)
	if err != nil {
		slog.FromContext(ctx).Error("logic.InsertQueueLog", fmt.Sprintf("error insert queue log: %v", err.Error()))
		return 0, err
	}
	return ID, err
}

// nolint:unparam
func (p *process) authenticateRequestByPermission(ctx context.Context, perm int) (*permissionManagementStorage.UserDTO, []storage.ElementDTO, error) {
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, nil, err
	}

	// Get the user eligible elements
	elements, err := storage.GetUserEligibleElements(ctx, db, user.UserID, perm)
	if err != nil {
		return nil, nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user eligible elements")
	}

	return user, elements, nil
}

func (p *process) authenticateRequestForTicketAction(ctx context.Context, elementID int64, action string, destinationStatusID int64) (*permissionManagementStorage.UserDTO, error) {
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get db")
	}

	ticketChain, err := storage.GetTicketChainByActionNameAndElementID(ctx, db, action, elementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain")
	} else if ticketChain.NextStatusID != destinationStatusID {
		return nil, errorwrapper.Error(apiError.BadRequest, "invalid status id")
	}

	bitwise, err := permissionManagementStorage.GetUserBitwiseValueForElementID(ctx, db, user.ID, elementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user permission")
	} else if bitwise&ticketChain.BitwiseRequired != ticketChain.BitwiseRequired {
		return nil, errorwrapper.Error(apiError.Forbidden, "user permission not sufficient")
	}

	return user, nil
}

func (p *process) makeTicketPayload(ctx context.Context, data interface{}, elementID int64) (interface{}, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// Get the element by id
	element, err := storage.GetElementByID(ctx, db, elementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element by id")
	}

	caster, ok := castTicketPayload[constants.ElementCodes(element.Code)]
	if !ok {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get cast payload")
	}
	obj, err := caster(data)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "getTicketPayload failed to cast payload")
	}

	ok, err = validations.IsValid(obj)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "getTicketPayload failed to validate payload")
	}

	return obj, nil
}

// castPayload is a generic function that safely casts a payload to the target type
func castPayload[T any](data interface{}) (interface{}, error) {
	var req T
	err := utils.LoadStruct(data, &req)
	if err != nil {
		return nil, errorTypeAssertionFailed
	}
	return &req, nil
}

var (
	errorTypeAssertionFailed = errorwrapper.Error(apiError.BadRequest, "payload ticket type assertion failed")

	// castTicketPayload is a map of element codes to functions that cast the payload to the correct type.
	castTicketPayload = map[constants.ElementCodes]func(interface{}) (interface{}, error){
		constants.BlockAccount:       castPayload[BlockAccountRequest],
		constants.UnblockAccount:     castPayload[UnblockAccountRequest],
		constants.ChangeAccountBlock: castPayload[ChangeAccountBlockRequest],
		constants.ChatbotKnowledgeBase: func(data interface{}) (interface{}, error) {
			dataMap, ok := data.(map[string]interface{})
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			action, ok := dataMap["action"].(string)
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			switch constants.ChatbotAction(action) {
			case constants.ChatbotAdd:
				return castPayload[chatbotServiceAPI.AddKnowledgeBaseRequest](data)
			case constants.ChatbotUpdate:
				return castPayload[chatbotServiceAPI.UpdateKnowledgeBaseRequest](data)
			case constants.ChatbotDelete:
				return castPayload[chatbotServiceAPI.DeleteKnowledgeBaseRequest](data)
			case constants.ChatbotImport:
				return castPayload[chatbotServiceAPI.ImportKnowledgeBaseRequest](data)
			default:
				return nil, errorwrapper.Error(apiError.BadRequest, "invalid action type")
			}
		},
		constants.ChatbotSystemPrompt:   castPayload[chatbotServiceAPI.SystemPromptRequest],
		constants.ChatbotConfig:         castPayload[chatbotServiceAPI.AddChatbotConfigRequest],
		constants.ChatbotEscapeKeywords: castPayload[chatbotServiceAPI.AddEscapeKeywordsRequest],
		// nolint:dupl
		constants.ChatbotWhitelist: func(data interface{}) (interface{}, error) {
			dataMap, ok := data.(map[string]interface{})
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			action, ok := dataMap["action"].(string)
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			switch constants.ChatbotAction(action) {
			case constants.ChatbotAdd:
				return castPayload[chatbotServiceAPI.AddCustomerWhitelistRequest](data)
			case constants.ChatbotDelete:
				return castPayload[chatbotServiceAPI.DeleteCustomerWhitelistRequest](data)
			case constants.ChatbotImport:
				return castPayload[chatbotServiceAPI.ImportCustomerWhitelistRequest](data)
			default:
				return nil, errorwrapper.Error(apiError.BadRequest, "invalid action type")
			}
		},
		// nolint:dupl
		constants.ChatbotTag: func(data interface{}) (interface{}, error) {
			dataMap, ok := data.(map[string]interface{})
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			action, ok := dataMap["action"].(string)
			if !ok {
				return nil, errorTypeAssertionFailed
			}

			switch constants.ChatbotAction(action) {
			case constants.ChatbotAdd:
				return castPayload[chatbotServiceAPI.AddTagRequest](data)
			case constants.ChatbotUpdate:
				return castPayload[chatbotServiceAPI.UpdateTagRequest](data)
			case constants.ChatbotDelete:
				return castPayload[chatbotServiceAPI.DeleteTagRequest](data)
			default:
				return nil, errorwrapper.Error(apiError.BadRequest, "invalid action type")
			}
		},
		constants.ReCDD:              castPayload[ReCDDRequest],
		constants.WriteOff:           castPayload[WriteOffRequest],
		constants.WaiveOff:           castPayload[WaiveOffRequest],
		constants.UpdateCustomerData: castPayload[UpdateCustomerDataRequest],
	}
)

func WriteAuditLogForTicket(ctx context.Context, db *sql.DB, dto *storage.TicketDTO, userID int64, ticketID int64, historyID string, title string, description string, eventType string) {
	dataSnapshot := make(map[string]interface{}, 0)
	if dto != nil {
		// Create data snapshot for audit trail
		dataSnapshot = map[string]interface{}{
			"ticket_id":     ticketID,
			"element_id":    dto.ElementID,
			"priority_id":   dto.PriorityID,
			"status_id":     dto.TicketStatusID,
			"source":        dto.Source,
			"deadline_time": dto.DeadlineTime.Time,
			"assignee_id":   dto.AssigneeUserID.Int64,
			"event_type":    eventType,
		}
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: userID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketID)),
		IdentifierType: constants.TicketID,
		Title:          title,
		Description:    description,
		ActivityType:   constants.Ticket,
		ReferenceID:    historyID,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

}
