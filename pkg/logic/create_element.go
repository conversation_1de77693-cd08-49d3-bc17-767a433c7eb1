package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreateElement creates an element
//
// nolint:funlen
func (p *process) CreateElement(ctx context.Context, req *api.CreateElementRequest) (*api.CreateElementResponse, error) {
	// check permission using common authentication
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Check if element code already exists
	existingElement, err := storage.GetElementByCodeExcludeID(ctx, db, req.Code, 0)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to check element code")
	}
	if existingElement != nil {
		return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("element with code '%s' already exists", req.Code))
	}

	// Create element
	elementDTO := &storage.ElementDTO{
		Name: req.Name,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		UpdatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		ModuleID:                 req.ModuleID,
		DefaultPriorityID:        req.DefaultPriorityID,
		Code:                     req.Code,
		Status:                   req.Status,
		HasTicketing:             req.HasTicketing,
		DefaultCustomerSegmentID: sql.NullInt64{Int64: req.DefaultCustomerSegmentID, Valid: req.DefaultCustomerSegmentID != 0},
		DefaultTicketRequestorID: sql.NullInt64{Int64: req.DefaultTicketRequestorID, Valid: req.DefaultCustomerSegmentID != 0},
	}

	elementID, err := storage.CreateElement(ctx, db, elementDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create element")
	}

	// write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     fmt.Sprint(elementID),
		IdentifierType: constants.ElementID,
		ActivityType:   string(constants.ModuleConfig),
		Title:          fmt.Sprintf("Element %s successfully created", req.Name),
		Description:    fmt.Sprintf("Element %s successfully created", req.Name),
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreateElementLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	// upsert ticket chain
	err = upsertTicketChain(ctx, db, req.TicketChains, req.ModuleID, elementID, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to upsert ticket chain")
	}

	return &api.CreateElementResponse{
		Id: elementID,
	}, nil
}
