package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreateFeatureFlag for create feature flag
// nolint: dupl
func (p *process) CreateFeatureFlag(ctx context.Context, req *api.CreateFeatureFlagRequest) (*api.CreateFeatureFlagResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.FeatureFlag, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// insert feature flag
	featureFlagDTO, err := insertFeatureFlag(ctx, req, db, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create feature flag")
	}

	// set feature flag to redis
	redisObject := make(map[string]interface{})
	redisObject[req.Name] = featureFlagDTO
	err = redis.SetHashRedisValue(ctx, redisObject, constants.FeatureFlagHashRedisKey, constants.CreateFeatureFlagLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to insert into redis")
	}

	// write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     "",
		IdentifierType: req.Name,
		ActivityType:   constants.FeatureFlagConfig,
		Title:          "",
		Description:    "Add New Feature Flag",
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreateFeatureFlagLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.CreateFeatureFlagResponse{
		Status: "success",
	}, nil
}

func insertFeatureFlag(ctx context.Context, req *api.CreateFeatureFlagRequest, db *sql.DB, ID int64) (*storage.FeatureFlagDTO, error) {
	featureFlagDTO := &storage.FeatureFlagDTO{
		Name:        req.Name,
		Value:       req.Value,
		Description: req.Description,
		Status:      constants.StatusActive,
		CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:   sql.NullInt64{Int64: ID, Valid: true},
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: ID, Valid: true},
	}

	// create feature flag
	err := storage.CreateFeatureFlag(ctx, db, featureFlagDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create feature flag")
	}

	return &storage.FeatureFlagDTO{
		Name:        req.Name,
		Value:       req.Value,
		Description: req.Description,
	}, nil
}
