package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreateModule creates a module
func (p *process) CreateModule(ctx context.Context, req *api.CreateModuleRequest) (*api.CreateModuleResponse, error) {
	// Check permission
	user, err := p.checkModuleCreatePermission(ctx)
	if err != nil {
		return nil, err
	}

	// Get database handle and validate
	db, err := p.getAndValidateModuleCreation(ctx, req)
	if err != nil {
		return nil, err
	}

	// Create module
	moduleID, err := p.createModuleInDB(ctx, db, req, user)
	if err != nil {
		return nil, err
	}

	// Write audit trail
	p.writeModuleAuditTrail(ctx, moduleID, req.Name, user.ID)

	return &api.CreateModuleResponse{
		Id: moduleID,
	}, nil
}

func (p *process) checkModuleCreatePermission(ctx context.Context) (*permissionManagementStorage.UserDTO, error) {
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this action")
	}
	return user, nil
}

func (p *process) getAndValidateModuleCreation(ctx context.Context, req *api.CreateModuleRequest) (*sql.DB, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Check if module name already exists
	existingModule, err := storage.GetModuleByName(ctx, db, req.Name)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to check module name")
	}
	if existingModule != nil {
		return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("module with name '%s' already exists", req.Name))
	}

	return db, nil
}

func (p *process) createModuleInDB(ctx context.Context, db *sql.DB, req *api.CreateModuleRequest, user *permissionManagementStorage.UserDTO) (int64, error) {
	moduleDTO := &storage.ModuleDTO{
		Name: req.Name,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		UpdatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		Status: req.Status,
	}

	return storage.CreateModule(ctx, db, moduleDTO)
}

func (p *process) writeModuleAuditTrail(ctx context.Context, moduleID int64, name string, userID int64) {
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: userID, Valid: true},
		Identifier:     fmt.Sprint(moduleID),
		IdentifierType: constants.ModuleID,
		ActivityType:   string(constants.ModuleConfig),
		Title:          fmt.Sprintf("Module %s successfully created", name),
		Description:    fmt.Sprintf("Module %s successfully created", name),
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreateModuleLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}
