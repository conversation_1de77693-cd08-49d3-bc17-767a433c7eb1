package logic

import (
	"context"
	"database/sql"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreatePriority creates a priority
func (p *process) CreatePriority(ctx context.Context, req *api.CreatePriorityRequest) (*api.CreatePriorityResponse, error) {
	// check permission using common authentication
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralCreate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this action")
	}

	// Get database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	priorityDTO := &storage.TicketPriorityDTO{
		Name: req.Name,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		UpdatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		TimeToResolveSec: req.TimeToResolveSec,
		ElementID:        req.ElementID,
	}

	priorityID, err := storage.CreateTicketPriority(ctx, db, priorityDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create priority")
	}

	return &api.CreatePriorityResponse{
		Id: priorityID,
	}, nil
}
