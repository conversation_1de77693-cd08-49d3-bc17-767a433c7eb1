package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	customerExperienceAPI "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	customerExperienceHttpAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

type PaginatedCustomerHistoryLogResponse struct {
	Data           *customerExperienceHttpAPI.GetCustomerHistoryLogResponse `json:"data"`
	PageSize       int                                                      `json:"pageSize"`
	Page           int                                                      `json:"page"`
	TotalPages     int                                                      `json:"totalPages"`
	PaginationType string                                                   `json:"paginationType"`
}

type CaseLink struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

func capitalizeFirstAndLowerRest(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(s[:1]) + strings.ToLower(s[1:])
}

// getUpdatedCustomerDataLog ...
// nolint:funlen,gocognit
func (p process) getUpdatedCustomerDataLog(data map[string]interface{}, ctx context.Context, req *api.CustomerSearchRequest, customerId string) (map[string]interface{}, error) {
	resp, err := p.GetCustomerHistoryLog(ctx, req, customerId)

	if err != nil {
		return nil, err
	}

	var customerHistory []map[string]interface{}
	for _, history := range resp.Data.HistoryLogs {
		var caseLink *CaseLink
		if history.CustomFields != nil && history.CustomFields.CaseID != "" {
			caseLink = &CaseLink{
				Type: history.CustomFields.CaseID,
				URL:  fmt.Sprintf("/tickets/%s", history.CustomFields.CaseID),
			}
		}

		dataType := history.TableName
		switch history.TableName {
		case "CUSTOMER_IDENTITY":
			dataType = "Customer Identity"
		case "EMPLOYMENT":
			dataType = "Employment Details"
		case "RETAIL_CUSTOMER":
			dataType = "Personal Information"
		case "ECDD_INFO":
			dataType = "EDD Information"
		case "CONTACT":
			dataType = "Contact"
		case "ADDRESS":
			var tempNewValues *customerExperienceHttpAPI.HistoryFieldValues
			if unmarshalErr := json.Unmarshal(*history.NewValues, &tempNewValues); unmarshalErr != nil {
				slog.FromContext(ctx).Error(constants.CustomerExperienceHTTPLogTag, "error to json.Unmarshal history new values : "+unmarshalErr.Error(), utils.GetTraceID(ctx))
			} else {
				dataType = capitalizeFirstAndLowerRest(tempNewValues.AddressType) + " " + capitalizeFirstAndLowerRest(history.TableName)
			}
		}

		historyData := map[string]interface{}{
			constants.KeyUpdateCustomerDataLogTimestamp:    history.UpdatedAt,
			constants.KeyUpdateCustomerDataLogSource:       history.Source,
			constants.KeyUpdateCustomerDataLogTicketId:     caseLink,
			constants.KeyUpdateCustomerDataLogDataType:     dataType,
			constants.KeyUpdateCustomerDataLogPreviousData: mapHistoryFields(ctx, history.PreviousValues, history.TableName == "TNC_AGREEMENT"),
			constants.KeyUpdateCustomerDataLogUpdatedData:  mapHistoryFields(ctx, history.NewValues, history.TableName == "TNC_AGREEMENT"),
			constants.KeyUpdateCustomerDataLogStatus:       history.Status,
			constants.KeyUpdateCustomerDataLogDetailStatus: history.DetailStatus,
			constants.KeyUpdateCustomerDataLogUpdatedBy:    history.UpdatedBy,
			constants.KeyUpdateCustomerDataLogApprovedBy:   history.ApprovedBy,
			constants.KeyUpdateCustomerDataLogReason:       history.Remarks,
		}

		customerHistory = append(customerHistory, historyData)
	}
	data[constants.KeyUpdateCustomerDataLog] = composeTableWithPaginationResponse(customerHistory, &PaginatedCustomerHistoryLogResponse{
		Page:           resp.Page,
		PageSize:       resp.PageSize,
		TotalPages:     resp.TotalPages,
		PaginationType: resp.PaginationType,
	})

	return data, err
}

// mapHistoryFields ...
// nolint:funlen,gocognit
func mapHistoryFields(ctx context.Context, h *json.RawMessage, isAgreement bool) []string {
	var result []string

	if h == nil {
		return result
	}

	if isAgreement {
		var previousValues map[string]customerExperienceHttpAPI.HistoryAgreement

		// Assuming `h` is a `*[]byte`
		if err := json.Unmarshal(*h, &previousValues); err != nil {
			slog.FromContext(ctx).Error(constants.CustomerExperienceHTTPLogTag, "error to json.Unmarshal history previous values : "+err.Error(), utils.GetTraceID(ctx))
			return result
		}

		hasAgreement := false
		for _, v := range previousValues {
			if v.AgreementID != "" {
				hasAgreement = true
				break
			}
		}

		if hasAgreement {
			for _, v := range previousValues {
				str := fmt.Sprintf("Agreement:\nID: %s\nType: %s\nStatus: %s", v.AgreementID, v.AgreementType, v.AgreementStatus)
				result = append(result, str)
			}
		}
	} else {
		var previousValues *customerExperienceHttpAPI.HistoryFieldValues
		if err := json.Unmarshal(*h, &previousValues); err != nil {
			slog.FromContext(ctx).Error(constants.CustomerExperienceHTTPLogTag, "error to json.Unmarshal history previous values : "+err.Error(), utils.GetTraceID(ctx))
			return result
		}

		// Identity Mapping
		switch previousValues.IDType {
		case "TAX NUMBER":
			if previousValues.IDNumber != "" {
				result = append(result, fmt.Sprintf("NPWP: %s", previousValues.IDNumber))
			}
		case "CITIZEN":
			if previousValues.IDNumber != "" {
				result = append(result, fmt.Sprintf("NIK: %s", previousValues.IDNumber))
			}
		}

		// Employment-related Mapping
		if previousValues.Industry != "" {
			result = append(result, fmt.Sprintf("Industry: %s", previousValues.Industry))
		}
		if previousValues.JobPosition != "" {
			result = append(result, fmt.Sprintf("Job Position: %s", previousValues.JobPosition))
		}
		if previousValues.MonthlyIncome != "" {
			result = append(result, fmt.Sprintf("Monthly Income: %s", previousValues.MonthlyIncome))
		}
		if previousValues.Occupation != "" {
			result = append(result, fmt.Sprintf("Occupation: %s", previousValues.Occupation))
		}
		if previousValues.SourceOfFunds != "" {
			result = append(result, fmt.Sprintf("Source of Funds: %s", previousValues.SourceOfFunds))
		}

		// Address Formatting (Structured Multiline)
		if previousValues.Street != "" || previousValues.RT != "" || previousValues.RW != "" || previousValues.Subdistrict != "" ||
			previousValues.Village != "" || previousValues.City != "" || previousValues.Province != "" || previousValues.PostalCode != "" {
			var sb strings.Builder

			// Line 1: Street
			if previousValues.Street != "" {
				sb.WriteString(previousValues.Street + "\n")
			}

			// Line 2: RT / RW
			if previousValues.RT != "" || previousValues.RW != "" {
				sb.WriteString(fmt.Sprintf("RT %s / RW %s\n", previousValues.RT, previousValues.RW))
			}

			// Line 3: Subdistrict, Village
			var subVill []string
			if previousValues.Subdistrict != "" {
				subVill = append(subVill, previousValues.Subdistrict)
			}
			if previousValues.Village != "" {
				subVill = append(subVill, previousValues.Village)
			}
			if len(subVill) > 0 {
				sb.WriteString(strings.Join(subVill, ", ") + "\n")
			}

			// Line 4: City, Province
			var cityProv []string
			if previousValues.City != "" {
				cityProv = append(cityProv, previousValues.City)
			}
			if previousValues.Province != "" {
				cityProv = append(cityProv, previousValues.Province)
			}
			if len(cityProv) > 0 {
				sb.WriteString(strings.Join(cityProv, ", ") + "\n")
			}

			// Line 5: Postal Code
			if previousValues.PostalCode != "" {
				sb.WriteString(previousValues.PostalCode)
			}

			result = append(result, fmt.Sprintf("Address:\n%s", sb.String()))
		}
	}

	return result
}

// GetCustomerHistoryLog ...
// nolint:funlen,gocognit
func (p process) GetCustomerHistoryLog(
	ctx context.Context,
	req *api.CustomerSearchRequest,
	customerID string,
) (*PaginatedCustomerHistoryLogResponse, error) {
	startDateStr, ok := req.Payload["startDate"]
	var startDate time.Time
	if ok {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err == nil {
			startDate = parsedStartDate
		}
	}

	endDateStr, ok := req.Payload["endDate"]
	var endDate time.Time
	if ok {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err == nil {
			endDate = parsedEndDate
		} else {
			endDate = time.Now()
		}
	} else {
		endDate = time.Now()
	}

	// Default values
	currentPage := 1
	pageSize := 5

	if pageStr, ok := req.Payload["currentPage"]; ok {
		if parsedPage, err := strconv.Atoi(pageStr); err == nil && parsedPage > 0 {
			currentPage = parsedPage
		}
	}

	if sizeStr, ok := req.Payload["pageSize"]; ok {
		if parsedSize, err := strconv.Atoi(sizeStr); err == nil && parsedSize > 0 {
			pageSize = parsedSize
		}
	}

	offset := (currentPage - 1) * pageSize

	response, err := p.CustomerExperienceHttpClient.GetCustomerHistoryLog(ctx, customerExperienceAPI.GetHistoryLogRequest{
		CustomerID: customerID,
		StartDate:  startDate,
		EndDate:    endDate,
		Offset:     &offset,
		Limit:      &pageSize,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, constants.CustomerExperienceHTTPLogTag+err.Error(), utils.GetTraceID(ctx))
		return nil, err
	}

	totalPages := 1
	if response.Count > 0 && pageSize > 0 {
		totalPages = int(math.Ceil(float64(response.Count) / float64(pageSize)))
	}

	return &PaginatedCustomerHistoryLogResponse{
		Data:           response,
		PageSize:       pageSize,
		Page:           currentPage,
		TotalPages:     totalPages,
		PaginationType: constants.BasicPagination,
	}, nil
}

// mappingBankInitiatedActivity maps the transaction data based on the request key
func (p process) mappingBankInitiatedActivity(ctx context.Context, req *api.CustomerSearchRequest) (map[string]any, error) {
	customerData, err := p.GetCustomerSafeID(ctx, req.Identifier, string(req.IdentifierType), req.Key)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer related data by identifier")
	}

	result := make(map[string]interface{})

	safeIDRaw, ok := customerData[constants.DataTypeSafeID]
	if !ok {
		return nil, errorwrapper.Error(apiError.InternalServerError, "SafeID not found in customer data")
	}
	safeID, ok := safeIDRaw.(string)
	if !ok || safeID == "" {
		return nil, errorwrapper.Error(apiError.InternalServerError, "invalid or empty SafeID in customer data")
	}

	switch req.Key {
	case constants.KeyUpdateCustomerDataLog:
		updatedData, err := p.getUpdatedCustomerDataLog(result, ctx, req, safeID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting transaction list")
		}
		result = updatedData
	}

	return result, nil
}
