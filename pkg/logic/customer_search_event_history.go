package logic

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"strconv"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerExperienceAPI "gitlab.super-id.net/bersama/onboarding/customer-experience/api"

	customerJourneyExperiencePreferenceAPI "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
)

func (p process) mappingEventHistoryRelatedData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var err error
	result, err := p.GetEventHistoryCustomerSearch(ctx, req)

	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to customer search request")
	}

	return result, nil
}

func (p process) overrideIdentifierIfNeeded(ctx context.Context, req *api.CustomerSearchRequest) error {
	if req.Key == constants.KeyMfaLog && req.Payload != nil && req.Payload["isGetByPhoneNumber"] != "" {
		resp, err := p.getCustomerData(ctx, req.Identifier, string(req.IdentifierType), []string{constants.ContactsType})
		if err != nil {
			return err
		}
		if contacts, ok := resp["contacts"].([]interface{}); ok && len(contacts) > 0 {
			if firstContact, ok := contacts[0].(map[string]interface{}); ok {
				if phoneNumber, ok := firstContact["phoneNumber"].(string); ok && phoneNumber != "" {
					req.Identifier = phoneNumber
					req.IdentifierType = api.IdentifierType(customerJournalAPI.Request_IdentifierType_PHONENUMBER)
				}
			}
		}
	}
	return nil
}

// nolint:funlen
func (p process) GetEventHistoryCustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 32)
	if pageSize == 0 {
		pageSize = constants.DefaultPaginationAppActivitySize
	}

	err := p.overrideIdentifierIfNeeded(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to get customer phone number")
	}

	endpoint := ""

	switch req.Key {
	case constants.KeyActivityLog:
		endpoint = constants.CustomerJournalLogTypeActivity
	case constants.KeyMfaLog:
		endpoint = constants.CustomerJournalLogTypeMFA
	case constants.KeyDeviceLoginInfo:
		endpoint = constants.CustomerJournalLogTypeLogin
	case constants.KeyFacialMatchingLivenessCheckLog:
		endpoint = constants.CustomerJournalLogTypeFacialAndLiveness
	case constants.KeyInteractionLog:
		endpoint = constants.CustomerJournalLogTypeInteraction
	case constants.KeyMarketingPreference:
		return nil, nil
	case constants.KeyMarketingPreferenceLog:
		endpoint = constants.CustomerJournalLogTypeMarketingPreference
	case constants.KeyMarketingPreferenceCurrent:
		return p.getMarketingPreferenceCurrent(ctx, safeID)
	default:
		return nil, errorwrapper.Error(apiError.FieldInvalid, "invalid key")
	}

	request := &customerJournalAPI.Request{
		UserSafeID:     safeID,
		PageSize:       int32(pageSize),
		StartDate:      req.Payload["startDate"],
		EndDate:        req.Payload["endDate"],
		StartingBefore: req.Payload["startingBefore"],
		EndingAfter:    req.Payload["endingAfter"],
		Identifier:     req.Identifier,
		IdentifierType: customerJournalAPI.Request_IdentifierType(req.IdentifierType),
		Endpoint:       endpoint,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)

	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to customer search request")
	}

	switch req.Key {
	case constants.KeyActivityLog:
		return composeEventHistoryActivityLog(ctx, resp), nil
	case constants.KeyMfaLog:
		return p.composeEventHistoryMfaLog(ctx, resp, req, safeID), nil
	case constants.KeyDeviceLoginInfo:
		return composeEventHistoryDeviceLoginInfo(ctx, resp), nil
	case constants.KeyFacialMatchingLivenessCheckLog:
		return composeEventHistoryFacialMatchingLivenessCheckLog(ctx, resp), nil
	case constants.KeyInteractionLog:
		return composeEventHistoryInteractionLog(ctx, resp), nil
	case constants.KeyMarketingPreferenceLog:
		return composeEventHistoryMarketingPreferenceLog(ctx, resp), nil
	default:
		return nil, errorwrapper.Error(apiError.FieldInvalid, "invalid key")
	}
}

func composeEventHistoryActivityLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}

			var errorValue string
			if errorStr, ok := metadata["error"].(string); ok {
				errorValue = extractErrorOrFailureMessage(errorStr)
			}

			isFailed := metadata["status"] == constants.LOCDeactivationFailed
			var beforeValue, afterValue interface{}
			if !isFailed {
				beforeValue = metadata["beforeValue"]
				afterValue = metadata["afterValue"]
			} else {
				beforeValue = ""
				afterValue = ""
			}

			currentData := map[string]interface{}{
				constants.KeyActivityLogTriggeredDate: transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyActivityLogEvent:         metadata["activityType"],
				constants.KeyActivityLogActivity:      metadata["actionType"],
				constants.KeyActivityLogBeforeValue:   beforeValue,
				constants.KeyActivityLogAfterValue:    afterValue,
				constants.KeyActivityLogTrigger:       metadata["trigger"],
				constants.KeyActivityLogStatus:        metadata["status"],
				constants.KeyActivityLogFailedReason:  errorValue,
			}
			finalData = append(finalData, currentData)
		}
	}
	result[constants.KeyActivityLog] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

type ItemDataEventHistoryMfa struct {
	TransactionID string
	Metadata      map[string]interface{}
	EventData     map[string]interface{}
	Index         int
}

// nolint:funlen,gocognit
func (p *process) composeEventHistoryMfaLog(ctx context.Context, resp *helper.EventLogResponse, req *api.CustomerSearchRequest, safeID string) map[string]interface{} {
	result := make(map[string]interface{})

	listData, listOk := resp.Data.([]interface{})
	if !listOk {
		result[constants.KeyMfaLog] = composeTableWithPaginationResponse([]map[string]interface{}{}, resp.Pagination)
		return result
	}

	baseSelfieFile := p.getBaseSelfieFile(ctx, req)

	itemsData := make([]ItemDataEventHistoryMfa, 0, len(listData))
	selfieRequests := make([]ItemDataEventHistoryMfa, 0)

	for i, item := range listData {
		itemData := p.processItemEventHistoryMfa(item, i)
		if itemData != nil {
			itemsData = append(itemsData, *itemData)

			if _, hasLatestSelfie := itemData.Metadata["latestSelfie"].(string); hasLatestSelfie && itemData.TransactionID != "" {
				selfieRequests = append(selfieRequests, *itemData)
			}
		}
	}

	selfieResults := p.getSelfiesConcurrently(ctx, safeID, selfieRequests)

	finalData := make([]map[string]interface{}, 0, len(itemsData))

	for _, itemData := range itemsData {
		var failReasonValue string
		if failReasonStr, ok := itemData.Metadata["failReason"].(string); ok {
			failReasonValue = extractErrorOrFailureMessage(failReasonStr)
		}

		selfieFile := selfieResults[itemData.Index]

		var mfaType, trigger string
		if val, ok := itemData.Metadata["mfaType"].(string); ok {
			mfaType = val
		}
		if val, ok := itemData.Metadata["trigger"].(string); ok {
			trigger = val
		}

		eventTimestampStr := transformEpochTimestamp(ctx, itemData.EventData["eventTimestamp"])
		cooldownPeriodStr := transformEpochTimestamp(ctx, itemData.Metadata["cooldownPeriod"])
		var cooldownPeriodValue string
		if cooldownPeriodStr != "" {
			cooldownPeriodValue = fmt.Sprintf("%s - %s", eventTimestampStr, cooldownPeriodStr)
		} else {
			cooldownPeriodValue = cooldownPeriodStr
		}

		formattedMfaType := getMfaTypeDescription(mfaType, trigger)

		var loginSource string
		if formattedMfaType == constants.MFATypeFMLC {
			if val, ok := itemData.Metadata["loginSource"].(string); ok {
				loginSource = getKeyDeviceLoginInfoLoginSource(val)
			}
		} else {
			if deviceInfoRaw, ok := itemData.Metadata["deviceInfo"]; ok {
				if deviceInfoMap, ok := deviceInfoRaw.(map[string]interface{}); ok {
					if loginSourceStr, ok := deviceInfoMap["loginSource"].(string); ok {
						loginSource = getKeyDeviceLoginInfoLoginSource(loginSourceStr)
					}
				}
			}
		}

		currentData := map[string]interface{}{
			constants.KeyMfaLogTimestamp:         eventTimestampStr,
			constants.KeyMfaLogMfaType:           formattedMfaType,
			constants.KeyMfaLogStatus:            itemData.Metadata["status"],
			constants.KeyMfaLogCooldownPeriod:    cooldownPeriodValue,
			constants.KeyMfaLogLoginSource:       loginSource,
			constants.KeyMfaLogFmlcFailedReason:  failReasonValue,
			constants.KeyMfaLogFmlcTransactionID: itemData.TransactionID,
			constants.KeyMfaLogFmlcMatchScore:    itemData.Metadata["fmlcFaceMatchScore"],
			constants.KeyMfaLogControlTriggered:  itemData.Metadata["controlTriggered"],
			constants.KeyMfaLogSelfieImage:       selfieFile,
			constants.KeyMfaLogBaseSelfie:        baseSelfieFile,
		}
		finalData = append(finalData, currentData)
	}

	result[req.Key] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func (p *process) getBaseSelfieFile(ctx context.Context, req *api.CustomerSearchRequest) string {
	requestBaseSelfie, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), 1)
	if err != nil || requestBaseSelfie == nil || len(requestBaseSelfie.Items) == 0 ||
		len(requestBaseSelfie.Items[0].Applications) == 0 || len(requestBaseSelfie.Items[0].Applications[0].SelfieFile) == 0 {
		return ""
	}
	return requestBaseSelfie.Items[0].Applications[0].SelfieFile[0].URL
}

func (p *process) processItemEventHistoryMfa(item interface{}, index int) *ItemDataEventHistoryMfa {
	convData, ok := item.(map[string]interface{})
	if !ok {
		return nil
	}

	metadata, ok := convData["metadata"].(map[string]interface{})
	if !ok {
		metadata = make(map[string]interface{})
	}

	transactionID := ""
	if transactionIdStr, ok := metadata["transactionId"].(string); ok {
		transactionID = transactionIdStr
	}

	return &ItemDataEventHistoryMfa{
		TransactionID: transactionID,
		Metadata:      metadata,
		EventData:     convData,
		Index:         index,
	}
}

func (p *process) getSelfiesConcurrently(ctx context.Context, safeID string, selfieRequests []ItemDataEventHistoryMfa) map[int]string {
	type SelfieResult struct {
		Index     int
		SelfieURL string
		Error     error
	}

	if len(selfieRequests) == 0 {
		return make(map[int]string)
	}

	const maxConcurrentRequests = 5
	semaphore := make(chan struct{}, maxConcurrentRequests)

	resultChan := make(chan SelfieResult, len(selfieRequests))
	var wg sync.WaitGroup

	for _, itemData := range selfieRequests {
		wg.Add(1)
		go func(data ItemDataEventHistoryMfa) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			selfieURL := p.getSingleSelfie(ctx, safeID, data.TransactionID)
			resultChan <- SelfieResult{
				Index:     data.Index,
				SelfieURL: selfieURL,
				Error:     nil,
			}
		}(itemData)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	selfieResults := make(map[int]string)
	for result := range resultChan {
		if result.Error == nil {
			selfieResults[result.Index] = result.SelfieURL
		}
	}

	return selfieResults
}

func (p *process) getSingleSelfie(ctx context.Context, safeID, transactionID string) string {
	request := &customerExperienceAPI.GetSelfieImagePresignedURLRequest{
		CustomerID:    safeID,
		TransactionID: transactionID,
	}

	customerRes, err := p.CustomerExperienceClient.GetSelfieImagePresignedURL(ctx, request)
	if err != nil || customerRes == nil {
		return ""
	}

	return customerRes.Data
}

// nolint: funlen
func getMfaTypeDescription(mfaType, trigger string) string {
	if mfaType == "" {
		return ""
	}

	var pathEnd string
	if trigger != "" {
		parts := strings.Split(trigger, "/")
		if len(parts) > 0 {
			pathEnd = parts[len(parts)-1]
		}
	}

	var mapTriggerDescription map[string]string

	switch mfaType {
	case constants.MFATypeFMLC:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerLogin:      constants.MFADescFMLCLogin,
			constants.MFATriggerPhone:      constants.MFADescFMLCUpdatePhone,
			constants.MFATriggerPinReset:   constants.MFADescFMLCResetPIN,
			constants.MFATriggerOnboarding: constants.MFADescFMLCOnboarding,
			constants.MFATriggerTransfers:  constants.MFADescFMLCPayment,
			constants.MFATriggerDrawdown:   constants.MFADescFMLCDrawdown,
		}
	case constants.MFATypePIN:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerLogin:     constants.MFADescPINLogin,
			constants.MFATriggerEmail:     constants.MFADescPINUpdateEmail,
			constants.MFATriggerPhone:     constants.MFADescPINUpdatePhone,
			constants.MFATriggerPin:       constants.MFADescPINChangePIN,
			constants.MFATriggerTransfers: constants.MFADescPINPayment,
			constants.MFATriggerEnrollMFA: constants.MFADescPINEnrollBiometric,
			constants.MFATriggerDrawdown:  constants.MFADescPINDrawdown,
		}
	case constants.MFATypeOTP:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerPrompt:      constants.MFADescOTPPrompt,
			constants.MFATriggerEmail:       constants.MFADescOTPEmail,
			constants.MFATriggerPhoneNumber: constants.MFADescOTPPhoneNumber,
		}
	case constants.MFATypeOTPRequest:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerEmail:       constants.MFADescOTPRequestEmail,
			constants.MFATriggerPhoneNumber: constants.MFADescOTPRequestSMS,
			constants.MFATriggerPinReset:    constants.MFADescOTPRequestResetPIN,
			constants.MFATriggerProof:       constants.MFADescOTPRequestResetLogin,
			constants.MFATriggerResendOTP:   constants.MFADescOTPRequestResend,
			constants.MFATriggerPrompt:      constants.MFADescOTPRequestPrompt,
		}
	case constants.MFATypeOTPValidation:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerEmail:       constants.MFADescOTPValidationEmail,
			constants.MFATriggerPhoneNumber: constants.MFADescOTPValidationSMS,
			constants.MFATriggerPinReset:    constants.MFADescOTPValidationResetPIN,
			constants.MFATriggerProof:       constants.MFADescOTPValidationResetLogin,
		}
	case constants.MFATypeBiometric:
		mapTriggerDescription = map[string]string{
			constants.MFATriggerVerifyLogin: constants.MFADescBiometricLogin,
		}
	}

	if description, ok := mapTriggerDescription[strings.ToLower(pathEnd)]; ok {
		return description
	}

	return mfaType
}

func composeEventHistoryDeviceLoginInfo(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata, deviceInfo map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			if convDeviceInfo, ok := metadata["deviceInfo"].(map[string]interface{}); ok {
				deviceInfo = convDeviceInfo
			}
			var failReasonValue string
			if failReasonStr, ok := metadata["failReason"].(string); ok {
				failReasonValue = extractErrorOrFailureMessage(failReasonStr)
			}
			var loginSource string
			if val, ok := metadata["loginSource"].(string); ok {
				loginSource = val
			}
			formattedLoginSource := getKeyDeviceLoginInfoLoginSource(loginSource)

			currentData := map[string]interface{}{
				constants.KeyDeviceLoginInfoDeviceID:       deviceInfo["deviceID"],
				constants.KeyDeviceLoginInfoDeviceBrand:    deviceInfo["deviceBrand"],
				constants.KeyDeviceLoginInfoDeviceModel:    deviceInfo["deviceModel"],
				constants.KeyDeviceLoginInfoOsName:         deviceInfo["osName"],
				constants.KeyDeviceLoginInfoOsVersion:      deviceInfo["osVersion"],
				constants.KeyDeviceLoginInfoAppVersion:     deviceInfo["appVersion"],
				constants.KeyDeviceLoginInfoDeliveryStatus: transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyDeviceLoginInfoLoginStatus:    metadata["status"],
				constants.KeyDeviceLoginInfoFailedReason:   failReasonValue,
				constants.KeyDeviceLoginInfoLoginSource:    formattedLoginSource,
			}
			finalData = append(finalData, currentData)
		}
	}
	result[constants.KeyDeviceLoginInfo] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func getKeyDeviceLoginInfoLoginSource(loginSource string) string {
	mapTriggerDescription := map[string]string{
		constants.DeviceLoginSourceKeyNative:        constants.DeviceLoginSourceValueNative,
		constants.DeviceLoginSourceKeyEcosystemGrab: constants.DeviceLoginSourceValueEcosystemGrab,
		constants.DeviceLoginSourceKeyEcosystemOvo:  constants.DeviceLoginSourceValueEcosystemOvo,
	}

	if description, ok := mapTriggerDescription[loginSource]; ok {
		return description
	}

	return loginSource
}

func composeEventHistoryFacialMatchingLivenessCheckLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			var trigger string
			if val, ok := metadata["trigger"].(string); ok {
				trigger = val
			}
			formattedFacialMatchingTrigger := getFacialMatchingDescription(trigger)

			var failReasonValue string
			if failReasonStr, ok := metadata["failReason"].(string); ok {
				failReasonValue = extractErrorOrFailureMessage(failReasonStr)
			}

			currentData := map[string]interface{}{
				constants.KeyFacialMatchingTriggeredDate: transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyFacialMatchingTrigger:       formattedFacialMatchingTrigger,
				constants.KeyFacialMatchingType:          metadata["type"],
				constants.KeyFacialMatchingStatus:        metadata["status"],
				constants.KeyFacialMatchingFailedReason:  failReasonValue,
			}
			finalData = append(finalData, currentData)
		}
	}
	result[constants.KeyFacialMatchingLivenessCheckLog] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func getFacialMatchingDescription(trigger string) string {
	var pathEnd string
	if trigger != "" {
		parts := strings.Split(trigger, "/")
		if len(parts) > 0 {
			pathEnd = parts[len(parts)-1]
		}
	}

	mapTriggerDescription := map[string]string{
		constants.FacialMatchingTriggerLogin:      constants.FacialMatchingDescLogin,
		constants.FacialMatchingTriggerPhone:      constants.FacialMatchingDescUpdatePhoneNumber,
		constants.FacialMatchingTriggerPinReset:   constants.FacialMatchingDescForgotPinFlow,
		constants.FacialMatchingTriggerOnboarding: constants.FacialMatchingDescOnboardingFlow,
		constants.FacialMatchingTriggerTransfers:  constants.FacialMatchingDescPayment,
	}

	if description, ok := mapTriggerDescription[pathEnd]; ok {
		return description
	}

	return pathEnd
}

// nolint:gocognit
func composeEventHistoryInteractionLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata, content map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			if convContent, ok := metadata["content"].(map[string]interface{}); ok {
				content = convContent
			}

			contentValue := "-"
			if title, ok := content["title"]; ok && title != nil {
				contentValue = title.(string)
			} else if value, ok := content["value"]; ok && value != nil {
				contentValue = value.(string)
			}

			var failReasonValue string
			if failReasonStr, ok := metadata["failReason"].(string); ok {
				failReasonValue = extractErrorOrFailureMessage(failReasonStr)
			}

			currentData := map[string]interface{}{
				constants.KeyInteractionLogDeliveryDate:   transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyInteractionLogTrigger:        metadata["trigger"],
				constants.KeyInteractionLogContent:        contentValue,
				constants.KeyInteractionLogType:           metadata["type"],
				constants.KeyInteractionLogChannel:        metadata["channel"],
				constants.KeyInteractionLogDeliveryStatus: metadata["status"],
				constants.KeyInteractionLogFailedReason:   failReasonValue,
			}
			finalData = append(finalData, currentData)
		}
	}
	result[constants.KeyInteractionLog] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func (p process) getMarketingPreferenceCurrent(ctx context.Context, safeID string) (map[string]interface{}, error) {
	request := &customerJourneyExperiencePreferenceAPI.GetConsentInternalRequest{
		UserSafeID: safeID,
	}
	resp, err := helper.GetConsentListInternal(ctx, request, constants.CustomerJourneyExperiencePreferenceLogTag, p.CustomerJourneyExperiencePreferenceClient)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to customer search request")
	}
	return composeEventHistoryMarketingPreferenceCurrent(resp), nil
}

func composeEventHistoryMarketingPreferenceCurrent(resp *customerJourneyExperiencePreferenceAPI.GetUserConsentResponse) map[string]interface{} {
	result := make(map[string]interface{})

	for _, item := range resp.Marketing {
		status := constants.MarketingPreferenceCurrentValueOff
		if item.Value {
			status = constants.MarketingPreferenceCurrentValueOn
		}

		switch item.ConsentType {
		case constants.ConsentTypePush:
			result[constants.KeyMarketingPreferenceCurrentPushNotification] = status
		case constants.ConsentTypeEmail:
			result[constants.KeyMarketingPreferenceCurrentEmail] = status
		case constants.ConsentTypeSms:
			result[constants.KeyMarketingPreferenceCurrentSms] = status
		case constants.ConsentTypeWhatsapp:
			result[constants.KeyMarketingPreferenceCurrentWhatsapp] = status
		}
	}
	return result
}

func composeEventHistoryMarketingPreferenceLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}

			var failReasonValue string
			if failReasonStr, ok := metadata["failReason"].(string); ok {
				failReasonValue = extractErrorOrFailureMessage(failReasonStr)
			}

			var channelValue string
			if channel, ok := metadata["consentType"].(string); ok {
				channelValue = getMarketingChannelDescription(channel)
			}

			preference := constants.MarketingPreferenceCurrentValueOff
			if consentValue, ok := metadata["consentValue"].(bool); ok && consentValue {
				preference = constants.MarketingPreferenceCurrentValueOn
			}

			currentData := map[string]interface{}{
				constants.KeyMarketingPreferenceLogTimestamp:    transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyMarketingPreferenceLogChannel:      channelValue,
				constants.KeyMarketingPreferenceLogPreference:   preference,
				constants.KeyMarketingPreferenceLogStatus:       metadata["status"],
				constants.KeyMarketingPreferenceLogFailedReason: failReasonValue,
			}
			finalData = append(finalData, currentData)
		}
	}
	result[constants.KeyMarketingPreferenceLog] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func getMarketingChannelDescription(channel string) string {
	mapTriggerDescription := map[string]string{
		constants.ConsentTypePush:     constants.ConsentTypePushDescription,
		constants.ConsentTypeEmail:    constants.ConsentTypeEmailDescription,
		constants.ConsentTypeSms:      constants.ConsentTypeSmsDescription,
		constants.ConsentTypeWhatsapp: constants.ConsentTypeWhatsappDescription,
	}

	if description, ok := mapTriggerDescription[channel]; ok {
		return description
	}

	return channel
}

func extractErrorOrFailureMessage(messageStr string) string {
	if messageStr == "" {
		return ""
	}

	lastIndex := strings.LastIndex(messageStr, "]")
	if lastIndex != -1 && lastIndex < len(messageStr)-1 {
		result := messageStr[lastIndex+1:]
		result = strings.TrimLeft(result, ": ")
		return strings.TrimSpace(result)
	}

	parts := strings.Split(messageStr, ", msg: ")
	if len(parts) > 1 {
		return parts[1]
	}

	msgIndex := strings.Index(messageStr, "msg:")
	if msgIndex != -1 {
		return strings.TrimSpace(messageStr[msgIndex+4:])
	}

	return messageStr
}
