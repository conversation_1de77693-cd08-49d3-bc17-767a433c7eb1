package logic

import (
	"context"

	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
)

func (p process) mappingCustomerSummary(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	// get customer data
	customerData, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), 1)
	if err != nil {
		return nil, err
	}

	if customerData == nil || len(customerData.Items) == 0 {
		return nil, nil
	}

	// get customer access
	access, err := helper.CustomerLoginStatus(ctx, p.GrabIDClient, req.Identifier, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	customer := customerData.Items[0]
	customerItem := map[string]interface{}{
		constants.KeyCustomerSummaryCustomerName:   customer.Customer.FullName,
		constants.KeyCustomerSummaryCif:            customer.Customer.CIF,
		constants.KeyCustomerSummaryCustomerStatus: customer.Customer.Status,
		constants.KeyCustomerSummarySafeId:         customer.Customer.ID,
		constants.KeyCustomerSummaryAccessStatus:   access.Status,
	}

	if len(customer.Applications) > 0 {
		customerItem[constants.KeyCustomerSummaryApplicationStatus] = customer.Applications[0].Status
	}

	if len(customer.Customer.Contacts) > 0 {
		customerItem[constants.KeyCustomerSummaryPhoneNumber] = customer.Customer.Contacts[0].PhoneNumber
	}

	// TODO: Add the rest of summary fields

	return map[string]interface{}{
		constants.KeyCustomerSummary: []map[string]interface{}{customerItem},
	}, nil
}
