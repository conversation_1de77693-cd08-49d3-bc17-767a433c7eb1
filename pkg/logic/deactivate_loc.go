// Package logic DeactivateLOC implement deactivation of loc account
package logic

import (
	"context"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// DeactivateLOCResponse ...
type DeactivateLOCResponse struct {
	IdempotencyKey    string
	LoanAccountID     string
	Status            string
	StatusDescription string
	StatusReason      string
	ReasonCode        string
}

// DeactivateLOC is the business logic for closing lending LOC account
func (p *process) DeactivateLOC(ctx context.Context, req *api.DeactivateLOCRequest) (*DeactivateLOCResponse, error) {
	// validating request
	err := validations.ValidateDeactivateLOCRequest(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DeactivateLOCLogTag, "Validation is failed", slog.Error(err))
		return nil, err
	}

	locReq := constructRequest(req)
	res, err := deactivateLoc(ctx, locReq, p.AccountServiceClient)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DeactivateLOCLogTag, constants.AccountServiceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		return nil, err
	}

	response := &DeactivateLOCResponse{
		IdempotencyKey:    res.IdempotencyKey,
		LoanAccountID:     res.ParentAccountID,
		Status:            res.Status,
		StatusDescription: res.StatusDescription,
		StatusReason:      res.StatusReason,
		ReasonCode:        res.ReasonCode,
	}

	return response, nil
}

func deactivateLoc(ctx context.Context, req *accountServiceAPI.LOCAccountDeactivationRequest, a accountServiceAPI.AccountService) (*accountServiceAPI.LOCAccountDeactivationResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, req.SafeID)
	withHTTPHeader = commonCtx.WithHTTPHeader(withHTTPHeader, commonCtx.HeaderXServiceID, constants.DIGIBANK)
	resp, err := a.FlexiLOCDeactivation(withHTTPHeader, req)
	return resp, err
}

func constructRequest(req *api.DeactivateLOCRequest) *accountServiceAPI.LOCAccountDeactivationRequest {
	return &accountServiceAPI.LOCAccountDeactivationRequest{
		SafeID:             req.SafeID,
		IdempotencyKey:     req.IdempotencyKey,
		ProductVariantCode: constants.ProductVariantCodeLOC,
		ParentAccountID:    req.LocAccountID,
		ReasonCode:         req.ReasonCode,
		CreatedBy:          req.CreatedBy,
		Metadata:           req.Metadata,
	}
}
