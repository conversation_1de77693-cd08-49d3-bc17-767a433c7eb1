package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// DeleteDocument apply soft delete to document
func (p *process) DeleteDocument(ctx context.Context, req *api.DeleteDocumentRequest) (*api.DeleteDocumentResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Authenticate the request
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	document, err := storage.GetDocumentByName(ctx, master, req.Name)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get document")
	}

	// if the document is not owned by the user, return unauthorized
	if document.CreatedBy.Int64 != user.ID {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "unauthorized to delete document")
	}

	if document.Count.Valid || document.ValidationStatus.Valid {
		err = storage.DeleteDocumentByParentID(ctx, master, document.ID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to delete document child")
		}
	}

	err = storage.DeleteDocument(ctx, master, document.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to delete document")
	}

	return &api.DeleteDocumentResponse{
		Id: document.ID,
	}, nil
}
