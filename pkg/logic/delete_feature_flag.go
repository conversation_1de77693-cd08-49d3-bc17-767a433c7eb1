package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// DeleteFeatureFlag for soft deleting feature flag
func (p *process) DeleteFeatureFlag(ctx context.Context, req *api.DeleteFeatureFlagRequest) (*api.DeleteFeatureFlagResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.FeatureFlag, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// delete feature flag
	success, err := deleteFeatureFlag(ctx, req, db, user.ID)
	if err != nil || !success {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to delete feature flag")
	}

	// delete feature flag to redis
	err = redis.DeleteHashRedisValue(ctx, []string{req.Name}, constants.FeatureFlagHashRedisKey, constants.DeleteFeatureFlagLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to delete into redis")
	}

	// write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     "",
		IdentifierType: req.Name,
		ActivityType:   constants.FeatureFlagConfig,
		Title:          "",
		Description:    "Delete Feature Flag",
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.CreateFeatureFlagLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.DeleteFeatureFlagResponse{
		Status: "success",
	}, nil
}

func deleteFeatureFlag(ctx context.Context, req *api.DeleteFeatureFlagRequest, db *sql.DB, ID int64) (bool, error) {
	featureFlagDTO := &storage.FeatureFlagDTO{
		Name:      req.Name,
		Status:    constants.StatusInactive,
		UpdatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy: sql.NullInt64{Int64: ID, Valid: true},
	}

	// update feature flag
	err := storage.DeleteFeatureFlag(ctx, db, featureFlagDTO)
	if err != nil {
		return false, errorwrapper.WrapError(err, apiError.Idem, "failed to delete feature flag")
	}

	return true, nil
}
