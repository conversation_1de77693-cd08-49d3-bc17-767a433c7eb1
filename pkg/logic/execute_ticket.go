package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

var (
	executeTicketLogTag = "logic.executeTicket"
)

// CheckTicketExecution checks whether ticket status is ready for execution, if yes will publish message to queue
func (p *process) CheckTicketExecution(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		slog.FromContext(ctx).Error("logic.CheckTicketExecution", fmt.Sprintf("error getting database handle: %v", err.Error()))
		return err
	}

	chains, err := storage.GetTicketChainByElementIDMap(ctx, slave, ticketDTO.ElementID)
	if err != nil {
		slog.FromContext(ctx).Error("logic.CheckTicketExecution", fmt.Sprintf("error getting ticket chain by element id: %v", err.Error()))
		return errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain by element id")
	}
	chain, ok := chains[constants.ActionSystemExecute]
	if !ok {
		return errorwrapper.Error(apiError.ResourceNotFound, "ticket chain not found")
	}

	if chain.CurrentStatusID != ticketDTO.TicketStatusID {
		return errorwrapper.Error(apiError.BadRequest, "ticket status is not valid")
	}

	return p.executeTicket(ctx, ticketDTO)
}

// executeTicket by sending message to queue
func (p *process) executeTicket(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return err
	}

	element, err := storage.GetElementByID(ctx, slave, ticketDTO.ElementID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.Idem, "failed to get element by id")
	}

	slog.FromContext(ctx).Info("logic.executeTicket", fmt.Sprintf("executing ticket: %v, elementCode: %v", ticketDTO.ID, element.Code))

	immediateExecutionFunc := p.getImmediateExecutionFunc(ctx, constants.ElementCodes(element.Code), ticketDTO)
	if immediateExecutionFunc != nil {
		// Create a new background context with longer timeout for S3 operations
		execCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second)

		// Create audit trail for immediate execution queued
		createExecuteTicketSuccessAuditTrail(ctx, ticketDTO)

		go func() {
			defer cancel() // Ensure context is cancelled when goroutine completes
			if err := immediateExecutionFunc(execCtx, ticketDTO); err != nil {
				// Create a logger with background context since parent ctx might be cancelled
				logger := slog.FromContext(execCtx)
				if err == context.DeadlineExceeded {
					logger.Error(executeTicketLogTag,
						fmt.Sprintf("operation timed out after 60 seconds: ticketID: %v, elementCode: %v",
							ticketDTO.ID, element.Code))
				} else {
					logger.Error(executeTicketLogTag,
						fmt.Sprintf("error executing ticket immediately: %v, ticketID: %v, elementCode: %v",
							err.Error(), ticketDTO.ID, element.Code))
				}

				// Create audit trail for immediate execution failure
				createExecuteTicketFailedAuditTrail(execCtx, ticketDTO, err)
			} else {
				// Create audit trail for immediate execution success
				createImmediateExecutionSuccessAuditTrail(execCtx, ticketDTO)

			}
		}()
		return nil
	}

	q, ok := p.QueueByElementCodes[constants.ElementCodes(element.Code)]
	if !ok {
		return errorwrapper.Error(apiError.BadRequest, "element code is not valid")
	}
	if q == nil {
		return errorwrapper.Error(apiError.InternalServerError, "queue is nil")
	}

	message, errMarshal := json.Marshal(QueueRequest{
		TicketID: fmt.Sprint(ticketDTO.ID),
		Payload:  ticketDTO.Data.Payload,
	})
	if errMarshal != nil {
		return errorwrapper.WrapError(errMarshal, apiError.BadRequest, "failed to marshal message")
	}

	err = q.SendMessage(ctx, string(message), nil)
	if err != nil {
		// Create audit trail for system task failure
		createExecuteTicketFailedAuditTrail(ctx, ticketDTO, err)
		return errorwrapper.WrapError(err, apiError.Idem, "failed to send message")
	}

	// Create audit trail for successful execution
	createExecuteTicketSuccessAuditTrail(ctx, ticketDTO)

	return nil
}

// createExecuteTicketFailedAuditTrail creates an audit trail entry for a failed ticket execution
func createExecuteTicketFailedAuditTrail(ctx context.Context, ticketDTO *storage.TicketDTO, err error) {
	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":     ticketDTO.ID,
		"element_id":    ticketDTO.ElementID,
		"priority_id":   ticketDTO.PriorityID,
		"status_id":     ticketDTO.TicketStatusID,
		"source":        ticketDTO.Source,
		"deadline_time": ticketDTO.DeadlineTime.Time,
		"assignee_id":   ticketDTO.AssigneeUserID.Int64,
		"data":          ticketDTO.Data,
		"event_type":    constants.SystemTaskFailed,
		"error_details": err.Error(),
		"error_code":    "EXECUTION_ERROR",
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketDTO.ID)),
		IdentifierType: constants.TicketID,
		Title:          "Ticket Execution Failed",
		Description:    fmt.Sprintf("Execution failed for ticket with id %d: %s", ticketDTO.ID, err.Error()),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(executeTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}

// createExecuteTicketSuccessAuditTrail creates an audit trail entry for a successful ticket execution
func createExecuteTicketSuccessAuditTrail(ctx context.Context, ticketDTO *storage.TicketDTO) {
	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":     ticketDTO.ID,
		"element_id":    ticketDTO.ElementID,
		"priority_id":   ticketDTO.PriorityID,
		"status_id":     ticketDTO.TicketStatusID,
		"source":        ticketDTO.Source,
		"deadline_time": ticketDTO.DeadlineTime.Time,
		"assignee_id":   ticketDTO.AssigneeUserID.Int64,
		"data":          ticketDTO.Data,
		"event_type":    constants.SystemTaskQueued,
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketDTO.ID)),
		IdentifierType: constants.TicketID,
		Title:          "Ticket Execution Queued",
		Description:    fmt.Sprintf("Execution queued for ticket with id %d", ticketDTO.ID),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(executeTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}

// createImmediateExecutionSuccessAuditTrail creates an audit trail entry for a successful immediate execution
func createImmediateExecutionSuccessAuditTrail(ctx context.Context, ticketDTO *storage.TicketDTO) {
	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":     ticketDTO.ID,
		"element_id":    ticketDTO.ElementID,
		"priority_id":   ticketDTO.PriorityID,
		"status_id":     ticketDTO.TicketStatusID,
		"source":        ticketDTO.Source,
		"deadline_time": ticketDTO.DeadlineTime.Time,
		"assignee_id":   ticketDTO.AssigneeUserID.Int64,
		"data":          ticketDTO.Data,
		"event_type":    constants.SystemTaskCompleted,
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketDTO.ID)),
		IdentifierType: constants.TicketID,
		Title:          "Immediate Execution Completed",
		Description:    fmt.Sprintf("Immediate execution completed for ticket with id %d", ticketDTO.ID),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(executeTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}

func (p *process) getImmediateExecutionFunc(ctx context.Context, elementCode constants.ElementCodes, ticketDTO *storage.TicketDTO) func(ctx context.Context, ticketDTO *storage.TicketDTO) error {
	actionMap := map[constants.ElementCodes]func(ctx context.Context, ticketDTO *storage.TicketDTO) error{
		constants.ChatbotSystemPrompt:   p.AddSystemPrompt,
		constants.ChatbotEscapeKeywords: p.UpdateEscapeKeywords,
		constants.ChatbotConfig:         p.UpdateChatbotConfig,
	}

	if execFunc, ok := actionMap[elementCode]; ok {
		return execFunc
	}

	if elementCode == constants.ChatbotKnowledgeBase {
		chatbotKnowledgeBaseMap := map[constants.ChatbotAction]func(ctx context.Context, ticketDTO *storage.TicketDTO) error{
			constants.ChatbotAdd:    p.AddKnowledgeBase,
			constants.ChatbotUpdate: p.UpdateKnowledgeBase,
			constants.ChatbotDelete: p.DeleteKnowledgeBase,
			constants.ChatbotImport: p.ImportKnowledgeBase,
		}

		capture, ok := ticketDTO.Data.Capture.(map[string]interface{})
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("ticketDTO.Data.Capture is not a map[string]interface{}: %v", ticketDTO.Data.Capture))
			return nil
		}
		action, ok := capture["knowledge"].(map[string]interface{})["action"].(string)
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("action is not a string: %v", capture["knowledge"].(map[string]interface{})["action"]))
			return nil
		}

		if execFunc, ok := chatbotKnowledgeBaseMap[constants.ChatbotAction(action)]; ok {
			return execFunc
		}
	}

	if elementCode == constants.ChatbotWhitelist { // nolint:dupl
		chatbotWhitelistMap := map[constants.ChatbotAction]func(ctx context.Context, ticketDTO *storage.TicketDTO) error{
			constants.ChatbotAdd:    p.AddCustomerWhitelist,
			constants.ChatbotDelete: p.DeleteCustomerWhitelist,
			constants.ChatbotImport: p.ImportCustomerWhitelist,
		}

		capture, ok := ticketDTO.Data.Capture.(map[string]interface{})
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("ticketDTO.Data.Capture is not a map[string]interface{}: %v", ticketDTO.Data.Capture))
			return nil
		}
		action, ok := capture["whitelist"].(map[string]interface{})["action"].(string)
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("action is not a string: %v", capture["whitelist"].(map[string]interface{})["action"]))
			return nil
		}

		if execFunc, ok := chatbotWhitelistMap[constants.ChatbotAction(action)]; ok {
			return execFunc
		}
	}

	if elementCode == constants.ChatbotTag { // nolint:dupl
		chatbotTagMap := map[constants.ChatbotAction]func(ctx context.Context, ticketDTO *storage.TicketDTO) error{
			constants.ChatbotAdd:    p.AddTag,
			constants.ChatbotUpdate: p.UpdateTag,
			constants.ChatbotDelete: p.DeleteTag,
		}

		capture, ok := ticketDTO.Data.Capture.(map[string]interface{})
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("ticketDTO.Data.Capture is not a map[string]interface{}: %v", ticketDTO.Data.Capture))
			return nil
		}
		action, ok := capture["tag"].(map[string]interface{})["action"].(string)
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("action is not a string: %v", capture["tag"].(map[string]interface{})["action"]))
			return nil
		}

		if execFunc, ok := chatbotTagMap[constants.ChatbotAction(action)]; ok {
			return execFunc
		}
	}

	if elementCode == constants.WriteOff || elementCode == constants.WaiveOff {
		_, ok := ticketDTO.Data.Capture.(map[string]interface{})
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc",
				fmt.Sprintf("ticketDTO.Data.Capture is not a map[string]interface{}: %v", ticketDTO.Data.Capture))
			return nil
		}

		return func(ctx context.Context, ticketDTO *storage.TicketDTO) error {
			return p.ExecuteFile(ctx, ticketDTO, elementCode)
		}
	}

	if elementCode == constants.UpdateCustomerData {
		payloadMap, ok := ticketDTO.Data.Payload.(UpdateCustomerDataRequest)
		if !ok {
			slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("ticketDTO.Data.Payload is not a UpdateCustomerDataRequest{}: %v", ticketDTO.Data.Payload))
			return nil
		}

		for _, key := range payloadMap.Keys {
			err := p.ExecuteUpdateCustomerData(ctx, ticketDTO, key)
			if err != nil {
				slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("failed to execute update customer data: %v", err.Error()))
			}
		}

		// send notification if required
		if payloadMap.IsSendNotification {
			params, err := structToMap(UpdateCustomerDataNotificationParams{
				UserName:       "", // FIXME
				AccountWording: "", // FIXME
				AccountName:    "", // FIXME
				AccountIDs:     "", // FIXME
				MessageDate:    time.Now().Format("2006-01-02 15:04:05"),
			})
			if err != nil {
				slog.FromContext(ctx).Error("logic.getImmediateExecutionFunc", fmt.Sprintf("failed to execute update customer data: %v", err.Error()))
				return nil
			}

			// FIXME: send notification email
			p.SendNotification(ctx, SendNotificationAction{
				ActionType:       constants.UpdateCustomerDataAction,
				SafeID:           payloadMap.CustomerId,
				TicketID:         fmt.Sprint(ticketDTO.ID),
				NotificationType: api.SendNotificationType_EMAIL,
				Params:           params,
			})

			// FIXME: send notification push inbox, fix params
			p.SendNotification(ctx, SendNotificationAction{
				ActionType:       constants.UpdateCustomerDataAction,
				SafeID:           payloadMap.CustomerId,
				TicketID:         fmt.Sprint(ticketDTO.ID),
				NotificationType: api.SendNotificationType_PUSH_INBOX,
				Params:           params,
			})
		}

		return nil
	}

	return nil
}
