package logic

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/csv"
	"fmt"

	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

type ExportTicket struct {
	TicketID          int64  `column:"Ticket ID"`
	Module            string `column:"Module"`
	TicketType        string `column:"Ticket Type"`
	Priority          string `column:"Prority SLA"`
	SourceSystem      string `column:"Source System"`
	DueDate           string `column:"Due Date"`
	TicketStatus      string `column:"Ticket Status"`
	IntegrationStatus string `column:"Integration Status"`
	AssignTo          string `column:"Assigned To"`
	CreatedAt         string `column:"Create At"`
	SubmittedAt       string `column:"Submitted At"`
	UpdatedAt         string `column:"Updated At"`
}

// ExportTicket a ticket
//
// nolint: funlen, dupl, errcheck
func (p *process) ExportTicket(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketExportResponse, error) {
	ticketResp, err := p.GetTicketList(ctx, req)
	if err != nil {
		return nil, err
	}

	var exportData []map[string]interface{}
	for _, t := range ticketResp.Tickets {
		row := make(map[string]interface{})
		for _, col := range req.Column {
			switch col {
			case "Ticket ID":
				row["Ticket ID"] = t.Id
			case "Module":
				row["Module"] = t.ModuleName
			case "Ticket Type":
				row["Ticket Type"] = t.ElementName
			case "Priority SLA":
				row["Priority SLA"] = t.PriorityName
			case "Source System":
				row["Source System"] = string(t.Source)
			case "Due Date":
				row["Due Date"] = t.DeadlineTime
			case "Ticket Status":
				row["Ticket Status"] = t.StatusName
			case "Integration Status":
				row["Integration Status"] = ""
			case "Assigned To":
				row["Assigned To"] = t.AssigneeUserName
			case "Created At":
				row["Created At"] = t.CreatedAt
			case "Submitted At":
				row["Submitted At"] = ""
			case "Updated At":
				row["Updated At"] = t.UpdatedAt
			}
		}

		exportData = append(exportData, row)
	}

	csvBytes, err := ExportMapsToCSV(req.Column, exportData)
	if err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	csv := base64.StdEncoding.EncodeToString(csvBytes)

	return &api.GetTicketExportResponse{
		DataExport: csv,
	}, nil
}

func ExportMapsToCSV(columns []string, data []map[string]interface{}) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// Write header
	if err := writer.Write(columns); err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	// Write rows
	for _, row := range data {
		var record []string
		for _, col := range columns {
			val, ok := row[col]
			if !ok {
				record = append(record, "")
			} else {
				record = append(record, fmt.Sprintf("%v", val))
			}
		}
		if err := writer.Write(record); err != nil {
			return nil, fmt.Errorf("failed to write CSV record: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("CSV writer error: %w", err)
	}

	return buf.Bytes(), nil
}
