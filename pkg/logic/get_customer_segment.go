package logic

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *process) GetCustomerSegmentList(ctx context.Context) (*api.GetCustomerSegmentsResponse, error) {
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}
	customerSegments, err := storage.GetCustomerSegmentList(ctx, slave, []commonStorage.QueryCondition{})
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get customer segments")
	}

	return &api.GetCustomerSegmentsResponse{
		CustomerSegments: makeCustomerSegmentListAPIStruct(customerSegments),
	}, nil

}

func makeCustomerSegmentListAPIStruct(customerSegment []storage.CustomerSegmentDTO) []api.CustomerSegment {
	var res []api.CustomerSegment
	for _, c := range customerSegment {
		res = append(res, api.CustomerSegment{
			Id:        c.ID,
			CreatedAt: utils.DateAsString(c.CreatedAt.Time),
			UpdatedAt: utils.DateAsString(c.UpdatedAt.Time),
			CreatedBy: fmt.Sprint(c.CreatedBy.Int64),
			UpdatedBy: fmt.Sprint(c.UpdatedBy.Int64),
			Name:      c.Name,
			IsActive:  fmt.Sprint(c.IsActive),
		})
	}
	return res
}
