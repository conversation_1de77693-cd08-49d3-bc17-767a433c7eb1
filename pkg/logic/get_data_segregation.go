package logic

import (
	"context"
	"database/sql"
	"fmt"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// GetDataSegregation ...
func (p *process) GetDataSegregation(ctx context.Context, req *api.GetDataSegregationRequest) (*api.GetDataSegregationResponse, error) {
	// check validity of request
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return &api.GetDataSegregationResponse{}, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return &api.GetDataSegregationResponse{}, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	curReq := constructDefaultValue(req)

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return &api.GetDataSegregationResponse{}, err
	}

	data, err := GetDataSegregationByRoleID(ctx, curReq, db)
	if err != nil {
		return &api.GetDataSegregationResponse{}, err
	}

	return constructDataToResponse(curReq, data), nil
}

func constructDefaultValue(req *api.GetDataSegregationRequest) *api.GetDataSegregationRequest {
	isSearchByKey := req.SearchKey != ""

	if isSearchByKey && req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}

	return req
}

func constructDataToResponse(req *api.GetDataSegregationRequest, data []*storage.DataSegregationListDTO) *api.GetDataSegregationResponse {
	response := &api.GetDataSegregationResponse{}

	response.Offset = req.Offset
	if req.Limit != 0 {
		isLastPage := len(data) <= int(req.Limit)
		response.IsLastPage = isLastPage
		if !isLastPage {
			data = data[:req.Limit]
		}
	}

	finalData := make([]api.DataSegregation, 0)
	if len(data) == 0 {
		response.Data = finalData
		return response
	}

	for _, seg := range data {
		finalData = append(finalData, api.DataSegregation{
			Id:         seg.ID,
			Name:       seg.Name,
			Status:     seg.Status,
			ParentID:   seg.ParentSegregationID.Int64,
			ParentName: seg.ParentName.String,
			HasChild:   seg.HasChild,
		})
	}
	response.Data = finalData

	return response
}

func GetDataSegregationByRoleID(ctx context.Context, req *api.GetDataSegregationRequest, db *sql.DB) ([]*storage.DataSegregationListDTO, error) {
	isSearchByKey := req.SearchKey != ""
	var filters []commonStorage.QueryCondition
	var data []*storage.DataSegregationListDTO
	var err error

	if isSearchByKey {
		filters = constructFilterGetSegregationBySearchKey(req)
		data, err = storage.GetRoleSegregationBySearch(ctx, db, req.RoleID, filters)
	} else {
		filters = constructFilterGetSegregationByParent(req)
		data, err = storage.GetRoleSegregationByParentID(ctx, db, req.RoleID, filters)
	}

	if err != nil {
		slog.FromContext(ctx).Error(constants.GetDataSegregationLogTag, "error fetching list data from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	return data, nil
}

func constructFilterGetSegregationBySearchKey(req *api.GetDataSegregationRequest) []commonStorage.QueryCondition {
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}
	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit + 1)),
		commonStorage.Offset(int(req.Offset))}

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("s.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	filters = append(filters, buildSortOrderDataSegregation(req.SortBy))

	return filters
}

func constructFilterGetSegregationByParent(req *api.GetDataSegregationRequest) []commonStorage.QueryCondition {
	filters := []commonStorage.QueryCondition{
		commonStorage.EqualTo("s.status", 1),
	}

	if req.ParentID == 0 {
		filters = append(filters, commonStorage.IsNull("s.parent_segregation_id"))
	} else {
		filters = append(filters, commonStorage.EqualTo("s.parent_segregation_id", req.ParentID))
	}

	if req.Limit != 0 || req.Offset != 0 {
		filters = append(filters, commonStorage.Limit(int(req.Limit+1)), commonStorage.Offset(int(req.Offset)))
	}

	filters = append(filters, buildSortOrderDataSegregation(req.SortBy))

	return filters
}

func buildSortOrderDataSegregation(sortBy *api.Sort) commonStorage.QueryCondition {
	if sortBy != nil && sortBy.Column != "" {
		if sortBy.Sort == api.SortOrder_DESC {
			return commonStorage.DescendingOrder(fmt.Sprintf("s.%s", sortBy.Column))
		}
		return commonStorage.AscendingOrder(fmt.Sprintf("s.%s", sortBy.Column))
	}
	return commonStorage.AscendingOrder("s.id")
}
