package logic

import (
	"context"
	"database/sql"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// GetDataSegregationRoles ...
func (p *process) GetDataSegregationRoles(ctx context.Context, req *api.GetDataSegregationRoleListRequest) (*api.GetDataSegregationRoleListResponse, error) {
	// check validity of request
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return &api.GetDataSegregationRoleListResponse{}, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return &api.GetDataSegregationRoleListResponse{}, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	//get count
	count, err := GetCountRolesDataSegregation(ctx, req, db)
	if err != nil {
		return &api.GetDataSegregationRoleListResponse{}, err
	}

	// check if offset more than count
	if req.Offset > count {
		return &api.GetDataSegregationRoleListResponse{}, errorwrapper.Error(apiError.BadRequest, "offset is more than total data")
	}

	data, err := GetDataSegregationRolesList(ctx, req, db)
	if err != nil {
		return &api.GetDataSegregationRoleListResponse{}, err
	}
	return &api.GetDataSegregationRoleListResponse{
		Count:  count,
		Offset: req.Offset,
		Data:   data,
	}, nil
}

func GetCountRolesDataSegregation(ctx context.Context, req *api.GetDataSegregationRoleListRequest, db *sql.DB) (int64, error) {
	var filters []commonStorage.QueryCondition

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	count, err := storage.GetCountRolesByElementCode(ctx, db, string(constants.CustomerSearch), filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetDataSegregationRolesLogTag, "error fetching count data for data segregation from DB", slog.Error(err))
		return 0, errorwrapper.Error(apiError.InternalServerError, "failed to fetch count data")
	}
	return count, nil
}

func GetDataSegregationRolesList(ctx context.Context, req *api.GetDataSegregationRoleListRequest, db *sql.DB) ([]api.DataSegregationRole, error) {
	filters := constructGetDataSegRolesFilter(req)
	data, err := storage.GetRolesByElementCode(ctx, db, string(constants.CustomerSearch), filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetDataSegregationRolesLogTag, "error fetching list roles for data segregation from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	finalData := make([]api.DataSegregationRole, 0)
	if len(data) == 0 {
		return finalData, nil
	}

	for _, role := range data {
		finalData = append(finalData, api.DataSegregationRole{
			Id:   role.ID,
			Name: role.Name,
		})
	}
	return finalData, nil
}

func constructGetDataSegRolesFilter(req *api.GetDataSegregationRoleListRequest) []commonStorage.QueryCondition {
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}

	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit)),
		commonStorage.Offset(int(req.Offset))}

	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	sortValue := ""
	if req.SortBy != nil && req.SortBy.Sort != "" {
		sortValue = string(req.SortBy.Sort)
	}

	column := ""
	if req.SortBy != nil && req.SortBy.Column != "" {
		column = req.SortBy.Column
	}

	filters = append(filters, commonStorage.BuildSortOrder(column, sortValue, "r"))
	return filters
}
