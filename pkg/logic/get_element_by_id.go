package logic

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetElementByID ...
func (p *process) GetElementByID(ctx context.Context, req *api.GetElementByIDRequest) (*api.GetElementByIDResponse, error) {
	// Get database handle
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get element by ID
	element, err := storage.GetElementByID(ctx, slave, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element by ID")
	}

	// Return the element
	return &api.GetElementByIDResponse{
		Element: &api.Element{
			Id:                       element.ID,
			Name:                     element.Name,
			Code:                     element.Code,
			ModuleID:                 element.ModuleID,
			DefaultPriorityID:        element.DefaultPriorityID,
			CreatedAt:                element.CreatedAt.Time.Local().String(),
			UpdatedAt:                element.UpdatedAt.Time.Local().String(),
			CreatedBy:                fmt.Sprintf("%d", element.CreatedBy.Int64),
			UpdatedBy:                fmt.Sprintf("%d", element.UpdatedBy.Int64),
			Status:                   element.Status,
			HasTicketing:             element.HasTicketing,
			DefaultCustomerSegmentID: element.DefaultCustomerSegmentID.Int64,
			DefaultTicketRequestorID: element.DefaultTicketRequestorID.Int64,
		},
	}, nil
}
