package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetElements ...
func (p *process) GetElements(ctx context.Context, req *api.GetElementsRequest) (*api.GetElementsResponse, error) {
	// get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	conditions := buildElementListQueryConditions(req, false)
	elements, err := storage.GetElements(ctx, db, conditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get elements")
	}

	conditions = buildElementListQueryConditions(req, true)
	count, err := storage.GetElementsCount(ctx, db, conditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get elements")
	}

	var resElements = make([]api.Element, 0)
	for _, element := range elements {
		resElements = append(resElements, api.Element{
			Id:                       element.ID,
			Name:                     element.Name,
			Code:                     element.Code,
			ModuleID:                 element.ModuleID,
			DefaultPriorityID:        element.DefaultPriorityID,
			CreatedAt:                utils.DateAsString(element.CreatedAt.Time),
			UpdatedAt:                utils.DateAsString(element.UpdatedAt.Time),
			CreatedBy:                element.CreatedBy.String,
			UpdatedBy:                element.UpdatedBy.String,
			Status:                   element.Status,
			HasTicketing:             element.HasTicketing,
			DefaultTicketRequestorID: element.DefaultTicketRequestorID.Int64,
			DefaultCustomerSegmentID: element.DefaultCustomerSegmentID.Int64,
		})
	}

	return &api.GetElementsResponse{
		Elements: resElements,
		Count:    count,
		Offset:   req.Offset,
	}, nil
}

func buildElementListQueryConditions(req *api.GetElementsRequest, isCount bool) []commonStorage.QueryCondition {
	var conditions = []commonStorage.QueryCondition{}
	if !isCount {
		conditions = append(conditions, commonStorage.Limit(int(req.Limit)), commonStorage.Offset(int(req.Offset)))
		conditions = append(conditions, commonStorage.DescendingOrder("e.id"))
	}

	if req.SearchKey != "" {
		conditions = append(conditions, commonStorage.Like("e.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.ModuleID != 0 {
		conditions = append(conditions, commonStorage.EqualTo("e.module_id", req.ModuleID))
	}

	if req.HasTicketing {
		conditions = append(conditions, commonStorage.EqualTo("e.has_ticketing", req.HasTicketing))
	}

	return conditions
}
