package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetElementPriorities ...
func (p *process) GetElementPriorities(ctx context.Context, req *api.GetElementPrioritiesRequest) (*api.GetElementPrioritiesResponse, error) {
	// Get database handle
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get element priorities
	priorities, err := storage.GetElementPriorities(ctx, slave, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element priorities DB")
	}

	// Return the element priorities
	return &api.GetElementPrioritiesResponse{
		Priorities: makePriorityListFromDTO(priorities),
	}, nil
}

func makePriorityListFromDTO(priorities []*storage.TicketPriorityDTO) []api.Priority {
	var res []api.Priority
	for _, p := range priorities {
		res = append(res, api.Priority{
			Id:               p.ID,
			Name:             p.Name,
			ElementID:        p.ElementID,
			TimeToResolveSec: p.TimeToResolveSec,
			CreatedAt:        p.CreatedAt.Time.Local().String(),
			UpdatedAt:        p.UpdatedAt.Time.Local().String(),
			CreatedBy:        p.CreatedBy.Int64,
			UpdatedBy:        p.UpdatedBy.Int64,
		})
	}
	return res
}
