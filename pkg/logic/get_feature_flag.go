package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetFeatureFlag for getting requested feature flag data
func (p *process) GetFeatureFlag(ctx context.Context, req *api.GetFeatureFlagRequest) (*api.GetFeatureFlagResponse, error) {
	// check permission
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.FeatureFlag, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// get feature flag from redis
	featureFlags, err := p.getFeatureFlag(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get feature flag ")
	}

	return &api.GetFeatureFlagResponse{
		Data: featureFlags,
	}, nil
}

func (p *process) getFeatureFlag(ctx context.Context, req *api.GetFeatureFlagRequest) ([]api.FeatureFlag, error) {
	var featureFlags []api.FeatureFlag

	// get from redis
	dataRedis, err := redis.GetRedisValues(ctx, req.Name, constants.FeatureFlagHashRedisKey, constants.GetFeatureFlagLogTag)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetFeatureFlagLogTag, fmt.Sprintf("error get redis value for feature flag %s with error: %s", req.Name, err.Error()), utils.GetTraceID(ctx))
	}

	// unmarshal redis data
	for _, value := range dataRedis {
		if value == nil {
			break
		}
		if featureFlag, redisErr := unmarshalFeatureFlag(value); redisErr == nil {
			featureFlags = append(featureFlags, featureFlag)
		} else {
			slog.FromContext(ctx).Warn(constants.GetFeatureFlagLogTag, fmt.Sprintf("Error unmarshalling redis value: %s", err.Error()), utils.GetTraceID(ctx))
			break
		}
	}

	if len(featureFlags) == len(req.Name) {
		return featureFlags, nil
	}
	featureFlags = nil

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// get from db
	dataDB, err := storage.GetFeatureFlagByName(ctx, db, req.Name)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetFeatureFlagLogTag, "error getting feature flag from DB", slog.Error(err))
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get feature flag")
	}

	// set feature flag to redis
	for _, values := range dataDB {
		// set feature flag to redis
		redisObject := make(map[string]interface{})
		redisObject[values.Name] = values
		err = redis.SetHashRedisValue(ctx, redisObject, constants.FeatureFlagHashRedisKey, constants.GetFeatureFlagLogTag)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to insert into redis")
		}
		featureFlags = append(featureFlags, api.FeatureFlag{
			Name:        values.Name,
			Description: values.Description,
			Value:       values.Value,
		})
	}

	return featureFlags, nil
}

// unmarshalFeatureFlag is a function to unmarshal feature flag data
func unmarshalFeatureFlag(value interface{}) (api.FeatureFlag, error) {
	var featureFlag api.FeatureFlag
	jsonValue, ok := value.(string)
	if !ok {
		return featureFlag, errorwrapper.Error(apiError.InternalServerError, "value is not string")
	}

	var dto storage.FeatureFlagDTO
	if err := json.Unmarshal([]byte(jsonValue), &dto); err != nil {
		return featureFlag, err
	}

	featureFlag = api.FeatureFlag{
		Name:        dto.Name,
		Description: dto.Description,
		Value:       dto.Value,
	}

	return featureFlag, nil
}

// GetFeatureFlagList for getting requested feature flag data
func (p *process) GetFeatureFlagList(ctx context.Context, req *api.GetFeatureFlagListRequest) (*api.GetFeatureFlagListResponse, error) {
	// check permission
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.FeatureFlag, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}
	if req.Limit == 0 {
		req.Limit = commonConstants.DefaultLimitValue
	}
	// Get the database slave handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	//get count
	count, err := getCountFeatureFlags(ctx, req, db)
	if err != nil {
		return &api.GetFeatureFlagListResponse{}, err
	}

	// check if offset more than count
	if req.Offset > count {
		return &api.GetFeatureFlagListResponse{}, errorwrapper.Error(apiError.BadRequest, "offset is more than total data")
	}

	// Get feature flag
	featureFlags, err := getFeatureFlagList(ctx, req, db)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get feature flag list")
	}

	return &api.GetFeatureFlagListResponse{
		Count:  count,
		Offset: req.Offset,
		Data:   featureFlags,
	}, nil
}

// getFeatureFlagList ...
func getFeatureFlagList(ctx context.Context, req *api.GetFeatureFlagListRequest, db *sql.DB) ([]api.FeatureFlag, error) {
	filters := constructGetFeatureFlagsDBFilters(req)
	data, err := storage.GetFeatureFlagList(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetFeatureFlagListLogTag, "error fetching list data from DB", slog.Error(err))
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to fetch list data")
	}

	finalData := make([]api.FeatureFlag, 0)
	if data == nil {
		return finalData, nil
	}

	for _, ff := range data {
		finalData = append(finalData, api.FeatureFlag{
			Name:        ff.Name,
			Value:       ff.Value,
			Description: ff.Description,
			CreatedAt:   ff.CreatedAt.Time.String(),
			UpdatedAt:   ff.UpdatedAt.Time.String(),
			CreatedBy:   ff.CreatedBy.String,
			UpdatedBy:   ff.UpdatedBy.String,
		})
	}
	return finalData, nil
}

// constructGetFeatureFlagsDBFilters
func constructGetFeatureFlagsDBFilters(req *api.GetFeatureFlagListRequest) []commonStorage.QueryCondition {
	filters := []commonStorage.QueryCondition{
		commonStorage.Limit(int(req.Limit)),
		commonStorage.Offset(int(req.Offset))}
	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.Filter != nil {
		for _, v := range req.Filter {
			if v.Column == "" && v.Value == nil {
				continue
			}
			filters = append(filters, commonStorage.In("r."+v.Column, v.Value...))
		}
	}

	sortValue := ""
	if req.SortBy != nil && req.SortBy.Sort != "" {
		sortValue = string(req.SortBy.Sort)
	}

	column := ""
	if req.SortBy != nil && req.SortBy.Column != "" {
		column = req.SortBy.Column
	}

	// append sorting
	filters = append(filters, commonStorage.BuildSortOrder(column, sortValue, "r"))

	return filters
}

// constructCountGetFeatureFlagsDBFilters
func constructCountGetFeatureFlagsDBFilters(req *api.GetFeatureFlagListRequest) []commonStorage.QueryCondition {
	var filters []commonStorage.QueryCondition
	if req.SearchKey != "" {
		filters = append(filters, commonStorage.Like("r.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.Filter != nil {
		for _, v := range req.Filter {
			if v.Column == "" && v.Value == nil {
				continue
			}
			filters = append(filters, commonStorage.In("r."+v.Column, v.Value...))
		}
	}

	return filters
}

// getCountFeatureFlags ...
func getCountFeatureFlags(ctx context.Context, req *api.GetFeatureFlagListRequest, db *sql.DB) (int64, error) {
	filters := constructCountGetFeatureFlagsDBFilters(req)
	count, err := storage.GetCountFeatureFlags(ctx, db, filters)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetFeatureFlagListLogTag, "error fetching count data from DB", slog.Error(err))
		return 0, errorwrapper.Error(apiError.InternalServerError, "failed to fetch count data")
	}
	return count, nil
}
