package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetModules ...
func (p *process) GetModules(ctx context.Context, req *api.GetModulesRequest) (*api.GetModulesResponse, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	conditions := buildModuleListQueryConditions(req, false)
	moduleList, err := storage.GetModules(ctx, db, req.HasTicketing, conditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get modules")
	}

	countConditions := buildModuleListQueryConditions(req, true)
	count, err := storage.GetModulesCount(ctx, db, req.HasTicketing, countConditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get modules count")
	}

	var resModules = make([]api.Module, 0)
	for _, module := range moduleList {
		resModules = append(resModules, api.Module{
			Id:           module.ID,
			Name:         module.Name,
			CreatedAt:    utils.DateAsString(module.CreatedAt.Time),
			UpdatedAt:    utils.DateAsString(module.UpdatedAt.Time),
			CreatedBy:    module.CreatedBy.String,
			UpdatedBy:    module.UpdatedBy.String,
			Status:       module.Status,
			HasTicketing: module.HasTicketing,
		})
	}

	return &api.GetModulesResponse{
		Modules: resModules,
		Count:   count,
		Offset:  req.Offset,
	}, nil
}

func buildModuleListQueryConditions(req *api.GetModulesRequest, isCount bool) []commonStorage.QueryCondition {
	var conditions = []commonStorage.QueryCondition{}

	if !isCount {
		conditions = append(conditions, commonStorage.Limit(int(req.Limit)), commonStorage.Offset(int(req.Offset)))
	}

	if req.SearchKey != "" {
		conditions = append(conditions, commonStorage.Like("m.name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	if req.Name != "" {
		conditions = append(conditions, commonStorage.EqualTo("m.name", req.Name))
	}

	return conditions
}
