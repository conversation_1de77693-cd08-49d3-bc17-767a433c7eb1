package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetPriorities ...
func (p *process) GetPriorities(ctx context.Context, req *api.GetPrioritiesRequest) (*api.GetPrioritiesResponse, error) {
	// Get database handle
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get element priorities
	priorities, err := storage.GetPriorities(ctx, slave, buildPriorityListQueryConditions(req))
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get priorities DB")
	}

	// Return the element priorities
	return &api.GetPrioritiesResponse{
		Priorities: makePriorityListFromDTO(priorities),
	}, nil
}

func buildPriorityListQueryConditions(req *api.GetPrioritiesRequest) []commonStorage.QueryCondition {
	var conditions = []commonStorage.QueryCondition{}

	if req.ElementID != 0 {
		conditions = append(conditions, commonStorage.EqualTo("element_id", req.ElementID))
	}

	return conditions
}
