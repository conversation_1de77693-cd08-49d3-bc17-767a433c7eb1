package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetStatuses ...
func (p *process) GetStatuses(ctx context.Context) (*api.GetStatusesResponse, error) {
	// Get database handle
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get statuses
	statuses, err := storage.GetStatuses(ctx, slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get statuses")
	}

	// Return the statuses
	return &api.GetStatusesResponse{
		Statuses: makeStatusListFromDTO(statuses),
	}, nil
}

func makeStatusListFromDTO(statuses []*storage.TicketStatusDTO) []api.Status {
	var res []api.Status
	for _, s := range statuses {
		res = append(res, api.Status{
			Id:   s.ID,
			Name: s.Name,
		})
	}
	return res
}
