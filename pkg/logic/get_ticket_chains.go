package logic

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetTicketChains ...
func (p *process) GetTicketChains(ctx context.Context, req *api.GetTicketChainsRequest) (*api.GetTicketChainsResponse, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	chains, err := storage.GetTicketChainByElementID(ctx, db, req.ElementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chains")
	}

	element, err := storage.GetElementByID(ctx, db, req.ElementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element")
	}

	perms, err := permissionManagementStorage.GetPermissionByModuleID(ctx, db, element.ModuleID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get permissions")
	}

	var resChains = make([]api.TicketChain, 0)
	for _, chain := range chains {
		var permIDs []int64
		for _, perm := range perms {
			if perm.BitwiseValue&chain.BitwiseRequired == perm.BitwiseValue {
				permIDs = append(permIDs, perm.ID)
			}
		}

		resChains = append(resChains, api.TicketChain{
			Id:             chain.ID,
			PrevStatusID:   chain.CurrentStatusID,
			NextStatusID:   chain.NextStatusID,
			ElementID:      chain.ElementID,
			ActionName:     chain.ActionName,
			PermissionsIDs: permIDs,
			CreatedAt:      utils.DateAsString(chain.CreatedAt.Time),
			UpdatedAt:      utils.DateAsString(chain.UpdatedAt.Time),
		})
	}

	return &api.GetTicketChainsResponse{
		Chains: resChains,
	}, nil
}
