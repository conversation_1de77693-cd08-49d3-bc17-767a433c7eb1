package logic

import (
	"context"
	"database/sql"
	"fmt"
	"slices"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// GetTicketList gets ticket list with pagination and search
func (p *process) GetTicketList(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketListResponse, error) {
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	_, elements, err := p.authenticateRequestByPermission(ctx, int(constants.BitwiseValueGeneralRead))
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get elements by module ID if specified
	if req.ModuleID != 0 {
		moduleElementsResp, elementErr := p.GetElements(ctx, &api.GetElementsRequest{
			ModuleID: req.ModuleID,
			Limit:    200,
		})
		if elementErr != nil {
			return nil, errorwrapper.WrapError(elementErr, apiError.InternalServerError, "failed to get elements by module id")
		}

		var moduleElements []storage.ElementDTO
		for _, e := range moduleElementsResp.Elements {
			moduleElements = append(moduleElements, storage.ElementDTO{
				ID: e.Id,
			})
		}
		elements = filterElementsByPermission(elements, moduleElements)
	}

	var user = &permissionManagementStorage.UserDTO{}
	if req.AssigneeUserID != "" {
		// get assignee user id filter
		user, err = permissionManagementStorage.GetUserByUserID(ctx, slave, req.AssigneeUserID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user by user id")
		}
	}

	conditions := buildTicketQueryConditions(req, elements, user.ID, true)

	total, err := storage.GetTicketCount(ctx, slave, conditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket count")
	}

	conditions = buildTicketQueryConditions(req, elements, user.ID, false)

	tickets, err := storage.GetTicketList(ctx, slave, conditions)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket list")
	}

	actionUsersByTicketID, err := getActionedUsers(ctx, slave, tickets)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get actioned users")
	}

	return &api.GetTicketListResponse{
		Tickets: makeTicketListAPIStruct(tickets, actionUsersByTicketID),
		Count:   total,
	}, nil
}

func buildTicketQueryConditions(req *api.GetTicketListRequest, elements []storage.ElementDTO, assigneeUserID int64, isCount bool) []commonStorage.QueryCondition {
	qb := commonStorage.NewQueryBuilder(20)

	// Add sorting and pagination separately
	if !isCount {
		// Handle the case when req.SortBy is nil
		column := ""
		sortOrder := ""
		if req.SortBy != nil && req.SortBy.Column != "" {
			column = req.SortBy.Column
		}
		if req.SortBy != nil && req.SortBy.Sort != "" {
			sortOrder = string(req.SortBy.Sort)
		}

		qb.AddSortWithDefaults(column, sortOrder, getSortColumn, string(api.SortOrder_DESC))
		qb.AddPagination(req.Limit, req.Offset)
	}

	addBasicFilters(qb, req, assigneeUserID)
	addSearchFilter(qb, req.SearchKey)
	addElementFilter(qb, elements)
	addDateRangeFilters(qb, req)

	return qb.Build()
}

// getSortColumn maps the API sort field to the database column
func getSortColumn(sortBy string) string {
	switch sortBy {
	case "id":
		return "t.id"
	case "caseType":
		return "t.case_category"
	case "caseSubcategory":
		return "t.case_subcategory"
	case "customerSegmentName":
		return "cs.name"
	case "priorityName":
		return "p.name"
	case "cif":
		return "t.domain_id"
	case "status":
		return "t.ticket_status_id"
	case "createdAt":
		return "t.created_at"
	case "dueOn":
		return "t.deadline_time"
	case "closedOn":
		return "t.ticket_close_datetime"
	case "assigneeUsername":
		return "u.name"
	case "source":
		return "t.source"
	case "channel":
		return "t.channel"
	default:
		return "t.created_at" // Default sort by created_at
	}
}

func makeElementIDList(els []storage.ElementDTO) []any {
	var ids []any
	for _, el := range els {
		ids = append(ids, el.ID)
	}
	return ids
}

func makeTicketListAPIStruct(tickets []storage.TicketDTO, actionUsers map[int64][]string) []api.Ticket {
	var res []api.Ticket

	for _, t := range tickets {
		users := mergeAssigneeWithActionedUsers(actionUsers[t.ID], t.AssigneeUserName)

		res = append(res, api.Ticket{
			Id:                  t.ID,
			ElementID:           t.ElementID,
			PriorityID:          t.PriorityID,
			StatusID:            t.TicketStatusID,
			Data:                t.Data,
			CreatedAt:           utils.DateAsString(t.CreatedAt.Time),
			UpdatedAt:           utils.DateAsString(t.UpdatedAt.Time),
			CreatedBy:           fmt.Sprint(t.CreatedBy.Int64),
			UpdatedBy:           fmt.Sprint(t.UpdatedBy.Int64),
			Source:              api.TicketSource(t.Source),
			DeadlineTime:        utils.DateAsString(t.DeadlineTime.Time),
			AssigneeUserName:    t.AssigneeUserName.String,
			ElementName:         t.ElementName,
			PriorityName:        t.PriorityName,
			TicketRequestorName: t.TicketRequestorName.String,
			CaseCategory:        t.CaseCategory.String,
			CaseSubcategory:     t.CaseSubcategory.String,
			DomainID:            t.DomainID.String,
			Channel:             t.Channel.String,
			CustomerSegmentName: t.CustomerSegmentName.String,
			TicketCloseDatetime: utils.DateAsString(t.TicketCloseDatetime.Time),
			ActionedUsers:       users,
			StatusName:          t.StatusName,
			ModuleName:          t.ModuleName,
		})
	}
	return res
}

func filterElementsByPermission(permittedElements, moduleElements []storage.ElementDTO) []storage.ElementDTO {
	if len(permittedElements) == 0 {
		return nil
	}

	permittedMap := make(map[int64]bool)
	for _, el := range permittedElements {
		permittedMap[el.ID] = true
	}

	var filteredElements []storage.ElementDTO
	for _, el := range moduleElements {
		if permittedMap[el.ID] {
			filteredElements = append(filteredElements, el)
		}
	}

	return filteredElements
}

func extractTicketIDs(tickets []storage.TicketDTO) []interface{} {
	if len(tickets) == 0 {
		return nil
	}

	ids := make([]interface{}, len(tickets))
	for i, ticket := range tickets {
		ids[i] = ticket.ID
	}
	return ids
}

func getActionedUsers(ctx context.Context, db *sql.DB, tickets []storage.TicketDTO) (map[int64][]string, error) {
	ticketIDs := extractTicketIDs(tickets)
	if ticketIDs == nil || len(ticketIDs) == 0 {
		return nil, nil
	}

	actionUsers, err := storage.GetActionedUsersFromTicketHistories(ctx, db, ticketIDs)
	if err != nil {
		return nil, err
	}
	return actionUsers, nil
}

// mergeAssigneeWithActionedUsers merges the assignee user with the list of actioned users,
// ensuring the assignee is included if they're not already in the actioned users list.
func mergeAssigneeWithActionedUsers(actionedUsers []string, assigneeUserName sql.NullString) []string {
	// If no assignee or assignee name is empty, return actioned users as is
	if !assigneeUserName.Valid || assigneeUserName.String == "" {
		return actionedUsers
	}

	// Check if assignee is already in the actioned users list
	if slices.Contains(actionedUsers, assigneeUserName.String) {
		return actionedUsers
	}

	// Assignee not found, append to the list
	return append(actionedUsers, assigneeUserName.String)
}

func addBasicFilters(qb *commonStorage.QueryBuilder, req *api.GetTicketListRequest, assigneeUserID int64) {
	intFilters := map[string]int64{
		"t.ticket_status_id": req.StatusID,
		"t.element_id":       req.ElementID,
		"t.priority_id":      req.PriorityID,
		"t.assignee_user_id": assigneeUserID,
	}

	for field, value := range intFilters {
		if value != 0 {
			qb.AddCondition(commonStorage.EqualTo(field, value))
		}
	}

	stringFilters := map[string]string{
		"t.case_category":    req.CaseType,
		"t.case_subcategory": req.CaseSubcategory,
		"t.source":           req.SourceSystem,
		"t.channel":          req.Channel,
		"cs.name":            req.CustomerSegmentName,
	}

	for field, value := range stringFilters {
		if value != "" {
			qb.AddCondition(commonStorage.EqualTo(field, value))
		}
	}
}

func addElementFilter(qb *commonStorage.QueryBuilder, elements []storage.ElementDTO) {
	if len(elements) == 0 {
		return
	}
	qb.AddCondition(commonStorage.In("t.element_id", makeElementIDList(elements)...))
}

func addSearchFilter(qb *commonStorage.QueryBuilder, searchKey string) {
	if searchKey == "" {
		return
	}

	searchPattern := "%" + strings.TrimSpace(searchKey) + "%"
	searchFields := []string{
		"e.name",
		"t.id",
		"t.case_category",
		"t.case_subcategory",
	}

	for _, field := range searchFields {
		qb.AddCondition(commonStorage.Like(field, searchPattern, commonStorage.QueryConditionTypeOR))
	}
}

func addDateRangeFilters(qb *commonStorage.QueryBuilder, req *api.GetTicketListRequest) {
	dateRanges := []struct {
		field string
		start string
		end   string
	}{
		{"t.created_at", req.CreatedDateStart, req.CreatedDateEnd},
		{"t.deadline_time", req.DueDateStart, req.DueDateEnd},
		{"t.ticket_close_datetime", req.ClosedDateStart, req.ClosedDateEnd},
	}

	for _, dr := range dateRanges {
		addDateRange(qb, dr.field, dr.start, dr.end)
	}
}

func addDateRange(qb *commonStorage.QueryBuilder, field, start, end string) {
	if start != "" {
		qb.AddCondition(commonStorage.GreaterThanOrEqualTo(field, start))
	}
	if end != "" {
		qb.AddCondition(commonStorage.LessThanOrEqualTo(field, end))
	}
}
