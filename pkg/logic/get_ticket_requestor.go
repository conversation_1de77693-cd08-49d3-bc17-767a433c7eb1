package logic

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p *process) GetTicketRequestorList(ctx context.Context) (*api.GetTicketRequestorsResponse, error) {
	slave, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}
	ticketRequestors, err := storage.GetTicketRequestorList(ctx, slave, []commonStorage.QueryCondition{})
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket requestors")
	}

	return &api.GetTicketRequestorsResponse{
		TicketRequestors: makeTicketRequestorListAPIStruct(ticketRequestors),
	}, nil

}

func makeTicketRequestorListAPIStruct(ticketRequestors []storage.TicketRequestorDTO) []api.TicketRequestor {
	var res []api.TicketRequestor
	for _, t := range ticketRequestors {
		res = append(res, api.TicketRequestor{
			Id:        t.ID,
			CreatedAt: utils.DateAsString(t.CreatedAt.Time),
			UpdatedAt: utils.DateAsString(t.UpdatedAt.Time),
			CreatedBy: fmt.Sprint(t.CreatedBy.Int64),
			UpdatedBy: fmt.Sprint(t.UpdatedBy.Int64),
			Name:      t.Name,
			IsActive:  fmt.Sprint(t.IsActive),
		})
	}
	return res
}
