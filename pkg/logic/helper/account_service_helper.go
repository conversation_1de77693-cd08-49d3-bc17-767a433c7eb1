// Package helper is for identify API that hit from onedash to upstream
package helper

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountService "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func logErrorAccountService(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.AccountServiceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetAccountList ...
func GetAccountList(ctx context.Context, request *accountService.ListCASAAccountsForCustomerDetailRequest, a accountService.AccountService, logTag string) (*accountService.ListCASAAccountsForCustomerDetailResponse, error) {
	response, err := a.ListCASAAccountsForCustomerDetail(ctx, request)
	if err != nil {
		logErrorAccountService(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetAccountDetail ...
func GetAccountDetail(ctx context.Context, request *accountService.GetAccountRequest, a accountService.AccountService, logTag string) (*accountService.GetAccountResponse, error) {
	response, err := a.GetAccountDetailsByAccountID(ctx, request)
	if err != nil {
		logErrorAccountService(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetDepositParameterHistory ...
func GetDepositParameterHistory(ctx context.Context, request *accountService.GetDepositsParameterHistoryRequest, a accountService.AccountService, logTag string) (*accountService.GetDepositsParameterHistoryResponse, error) {
	response, err := a.GetDepositsParameterHistory(ctx, request)
	if err != nil {
		logErrorAccountService(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetRenewalHistory ...
func GetRenewalHistory(ctx context.Context, request *accountService.DepositsParameterRenewalHistoryRequest, a accountService.AccountService, logTag string) (*accountService.DepositsParameterRenewalHistoryResponse, error) {
	response, err := a.GetDepositsParameterRenewalHistory(ctx, request)
	if err != nil {
		logErrorAccountService(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}
