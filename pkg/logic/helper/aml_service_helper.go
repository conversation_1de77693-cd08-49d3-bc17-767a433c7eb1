package helper

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	amlService "gitlab.super-id.net/bersama/fintrust/aml-service/api"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func logErrorAMLService(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.AmlServiceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetCustomerData ...
func GetCustomerData(ctx context.Context, request *amlService.GetCustomerRequest, logTag string, c amlService.Customer) (*amlService.GetCustomerResponse, error) {
	response, err := c.GetCustomerData(ctx, request)
	if err != nil {
		logErrorAMLService(ctx, err, logTag)
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to aml service request")
	}
	return response, nil
}

// GetCustomerFlag ...
func GetCustomerFlag(ctx context.Context, request *amlService.GetCustomerFlagRequest, logTag string, c amlService.Customer) (*amlService.GetCustomerFlagResponse, error) {
	response, err := c.GetCustomerFlag(ctx, request)
	if err != nil {
		logErrorAMLService(ctx, err, logTag)
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to aml service request")
	}
	return response, nil
}
