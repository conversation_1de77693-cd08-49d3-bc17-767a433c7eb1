package helper

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	journalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// EventLogResponse ...
type EventLogResponse struct {
	Pagination *Pagination `json:"pagination,omitempty"`
	Data       interface{} `json:"data,omitempty"`
}

// Pagination ...
type Pagination struct {
	NextCursorID string `json:"nextCursorID,omitempty"`
	PrevCursorID string `json:"prevCursorID,omitempty"`
}

func logErrorCustomerJournal(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.CustomerJournalLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetEventLog ...
func GetEventLog(ctx context.Context, req *journalAPI.Request, logTag string, f func(ctx context.Context, req *journalAPI.Request) (*journalAPI.Response, error)) (*EventLogResponse, error) {
	res, err := f(ctx, req)

	if err != nil {
		logErrorCustomerJournal(ctx, err, logTag)
		return nil, err
	}

	return composeEventLogResponse(res), nil
}

// composeEventLogResponse ...
func composeEventLogResponse(res *journalAPI.Response) *EventLogResponse {
	pagination := &Pagination{}
	if res.Links != nil {
		pagination.NextCursorID = res.Links.NextCursorID
		pagination.PrevCursorID = res.Links.PrevCursorID
	}
	return &EventLogResponse{
		Pagination: pagination,
		Data:       res.Data,
	}
}
