package helper

import (
	"context"
	"errors"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"

	loanAppServiceAPI "gitlab.myteksi.net/dakota/lending/loan-app/api"
	loanExpServiceAPI "gitlab.myteksi.net/dakota/lending/loan-exp/api"
)

func logErrorLoanAppService(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.LoanAppLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

func logErrorLoanExpService(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.LoanExpLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetLoanAppByIdentifier ...
func GetLoanAppByIdentifier(ctx context.Context, identifierValue string, identifierType string, loanApp loanAppServiceAPI.LoanApp, logTag string) (*loanAppServiceAPI.GetFlexiTermLoanApplicationByIdentifierResponse, error) {
	getLoanAppReq := &loanAppServiceAPI.GetFlexiTermLoanApplicationByIdentifierRequest{
		IdentifierValue: identifierValue,
		IdentifierType:  loanAppServiceAPI.IDType(identifierType),
	}

	if getLoanAppReq.IdentifierType == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "invalid id type")
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	loanApplications, err := loanApp.GetFlexiTermLoanApplicationByIdentifier(withHTTPHeader, getLoanAppReq)
	if err != nil {
		logErrorLoanAppService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
	}
	return loanApplications, nil
}

// GetApplicantCoolingPeriod ...
func GetApplicantCoolingPeriod(ctx context.Context, cif, productVariantCode string, loanApp loanAppServiceAPI.LoanApp, logTag string) (*loanAppServiceAPI.GetApplicantCoolingPeriodDetailsResponse, error) {
	if cif == "" || productVariantCode == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "invalid input")
	}

	getCoolingAppReq := &loanAppServiceAPI.GetApplicantCoolingPeriodDetailsRequest{
		ApplicantID:        cif,
		ProductVariantCode: productVariantCode,
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	coolingPeriod, err := loanApp.GetApplicantCoolingPeriodDetails(withHTTPHeader, getCoolingAppReq)
	if err != nil {
		logErrorLoanAppService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return coolingPeriod, nil
}

// GetWhitelistApplicantList ...
func GetWhitelistApplicantList(ctx context.Context, req loanAppServiceAPI.GetWhitelistApplicantListRequest, loanApp loanAppServiceAPI.LoanApp, logTag string) (*loanAppServiceAPI.GetWhitelistApplicantListResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	list, err := loanApp.GetWhitelistApplicantList(withHTTPHeader, &req)
	if err != nil {
		logErrorLoanAppService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
	}
	return list, nil
}

// GetWhitelistApplicantHistory ...
func GetWhitelistApplicantHistory(ctx context.Context, applicantWhitelistID int64, loanApp loanAppServiceAPI.LoanApp, logTag string) (*loanAppServiceAPI.GetWhitelistHistoryApplicantResponse, error) {
	whitelistHistoryReq := &loanAppServiceAPI.GetWhitelistHistoryApplicantRequest{
		ApplicantWhitelistID: uint64(applicantWhitelistID),
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	list, err := loanApp.GetWhitelistHistoryApplicant(withHTTPHeader, whitelistHistoryReq)
	if err != nil {
		logErrorLoanAppService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
	}
	return list, nil
}

// Update the getWhitelistByIdentifier function
func getWhitelistByIdentifier(ctx context.Context, identifierType, value string, loanApp loanAppServiceAPI.LoanApp, logTag string) []loanAppServiceAPI.WhitelistApplicantData {
	if value == "" {
		return nil
	}

	req := loanAppServiceAPI.GetWhitelistApplicantListRequest{
		Key:   loanAppServiceAPI.WhitelistKey(identifierType),
		Value: value,
	}

	response, err := GetWhitelistApplicantList(ctx, req, loanApp, logTag)
	if err != nil {
		return nil
	}

	// Check if we have results
	if len(response.Results) > 0 {
		return response.Results
	}

	return nil
}

// GetWhitelistApplicant ...
func GetWhitelistApplicant(ctx context.Context, cif, phoneNumber, nik string, loanApp loanAppServiceAPI.LoanApp, logTag string) (*loanAppServiceAPI.WhitelistApplicantData, error) {
	isNotEmpty := func(s string) bool {
		return len(strings.TrimSpace(s)) > 0
	}

	var number string
	if isNotEmpty(phoneNumber) {
		parts := strings.Split(phoneNumber, "+62")
		if len(parts) >= 2 {
			number = parts[1]
		}
	}

	withPlus := phoneNumber
	withoutPlus := "62" + number
	withZero := "0" + number

	var whitelistCIF, whitelistWithPlus, whitelistWithoutPlus, whitelistWithZero, whitelistNIK []loanAppServiceAPI.WhitelistApplicantData

	if cif != "" {
		whitelistCIF = getWhitelistByIdentifier(ctx, "CIFNumber", cif, loanApp, logTag)
	}

	if phoneNumber != "" {
		// Get whitelist for original format with plus
		if withPlus != "" {
			whitelistWithPlus = getWhitelistByIdentifier(ctx, "ContactNumber", withPlus, loanApp, logTag)
		}

		// Get whitelist for format without plus
		if withoutPlus != "" {
			whitelistWithoutPlus = getWhitelistByIdentifier(ctx, "ContactNumber", withoutPlus, loanApp, logTag)
		}

		// Get whitelist for format with zero
		if withZero != "" {
			whitelistWithZero = getWhitelistByIdentifier(ctx, "ContactNumber", withZero, loanApp, logTag)
		}
	}

	if nik != "" {
		whitelistNIK = getWhitelistByIdentifier(ctx, "IDNumber", nik, loanApp, logTag)
	}

	phoneData := filterNonEmptyData([][]loanAppServiceAPI.WhitelistApplicantData{
		whitelistWithPlus,
		whitelistWithoutPlus,
		whitelistWithZero,
	})

	whitelistPhoneNumber := processPhoneWhitelist(phoneData)

	allData := filterNonEmptyData([][]loanAppServiceAPI.WhitelistApplicantData{
		whitelistCIF,
		whitelistNIK,
		whitelistPhoneNumber,
	})

	return findOldestWhitelistData(allData)
}

func processPhoneWhitelist(phoneData []loanAppServiceAPI.WhitelistApplicantData) []loanAppServiceAPI.WhitelistApplicantData {
	switch len(phoneData) {
	case 0:
		return []loanAppServiceAPI.WhitelistApplicantData{}
	case 1:
		return phoneData
	case 2:
		if phoneData[0].CreatedAt.After(phoneData[1].CreatedAt) {
			return []loanAppServiceAPI.WhitelistApplicantData{phoneData[1]}
		}
		return []loanAppServiceAPI.WhitelistApplicantData{phoneData[0]}
	case 3:
		return []loanAppServiceAPI.WhitelistApplicantData{findOldestOfThree(phoneData)}
	default:
		return []loanAppServiceAPI.WhitelistApplicantData{}
	}
}

func findOldestOfThree(data []loanAppServiceAPI.WhitelistApplicantData) loanAppServiceAPI.WhitelistApplicantData {
	oldest := data[0]
	for _, d := range data[1:] {
		if d.CreatedAt.Before(oldest.CreatedAt) {
			oldest = d
		}
	}
	return oldest
}

func findOldestWhitelistData(data []loanAppServiceAPI.WhitelistApplicantData) (*loanAppServiceAPI.WhitelistApplicantData, error) {
	if len(data) == 0 {
		return nil, errors.New("no whitelist data available")
	}

	oldest := data[0]
	for _, d := range data[1:] {
		if d.CreatedAt.Before(oldest.CreatedAt) {
			oldest = d
		}
	}
	return &oldest, nil
}

func filterNonEmptyData(data [][]loanAppServiceAPI.WhitelistApplicantData) []loanAppServiceAPI.WhitelistApplicantData {
	var filtered []loanAppServiceAPI.WhitelistApplicantData
	for _, slice := range data {
		if len(slice) > 0 {
			filtered = append(filtered, slice...)
		}
	}
	return filtered
}

// FetchAccountDetails ...
// nolint: dupl
func FetchAccountDetails(ctx context.Context, accountID, productVariantCode, safeID string, loanExp loanExpServiceAPI.LoanExp, logTag string) (*loanExpServiceAPI.AccountDetailsResponse, error) {
	getAccountDetailsReq := &loanExpServiceAPI.AccountDetailsRequestForCRM{
		AccountID:          accountID,
		ProductVariantCode: productVariantCode,
		SafeID:             safeID,
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	accountDetails, err := loanExp.FetchAccountDetailsForCRM(withHTTPHeader, getAccountDetailsReq)
	if err != nil {
		logErrorLoanExpService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return accountDetails, nil
}

// GetCustomerInsights ...
// nolint: dupl
func GetCustomerInsights(ctx context.Context, accountID, productVariantCode, safeID string, loanExp loanExpServiceAPI.LoanExp, logTag string) (*loanExpServiceAPI.FetchCustomerInsightsForCRMResponse, error) {
	getCustomerInsightReq := &loanExpServiceAPI.FetchCustomerInsightsForCRMRequest{
		AccountID:          accountID,
		ProductVariantCode: productVariantCode,
		SafeID:             safeID,
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	insight, err := loanExp.FetchCustomerInsightsForCRM(withHTTPHeader, getCustomerInsightReq)
	if err != nil {
		logErrorLoanExpService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return insight, nil
}

// GetLOCHoldCode ...
// nolint: dupl
func GetLOCHoldCode(ctx context.Context, accountID, productVariantCode, safeID string, loanExp loanExpServiceAPI.LoanExp, logTag string) (*loanExpServiceAPI.GetHoldCodesResponse, error) {
	getLOCReq := &loanExpServiceAPI.GetHoldCodesRequest{
		AccountID:          accountID,
		ProductVariantCode: productVariantCode,
		SafeID:             safeID,
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	LOC, err := loanExp.GetHoldCode(withHTTPHeader, getLOCReq)
	if err != nil {
		logErrorLoanExpService(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return LOC, nil
}
