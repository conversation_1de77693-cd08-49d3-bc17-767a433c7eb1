package helper

import (
	"context"

	PayAuthZApi "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func logErrorPayAuthz(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.PayAuthzLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetListPartner ...
func GetListPartner(ctx context.Context, safeID string, p PayAuthZApi.PayAuthz, logTag string) (*PayAuthZApi.ListPartnerResponse, error) {
	req := &PayAuthZApi.ListPartnerRequest{SafeID: safeID}
	listPartnerEndpoint, err := p.ListPartnerEndpoint(ctx, req)
	if err != nil {
		logErrorPayAuthz(ctx, err, logTag)
		return nil, err
	}
	return listPartnerEndpoint, nil
}
