package helper

import (
	"context"
	"time"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	transactionHistory "gitlab.super-id.net/bersama/deposit/transaction-history/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

const (
	trxSubtypeSavingsOvo = "SAVINGS_OVO"
	trxSubtypeQRISAlto   = "QRIS_ALTO"
	trxSubtypeGrab       = "GRAB"
	trxSubTypeBiFast     = "BI_FAST"
	trxSubTypeIntrabank  = "INTRABANK"

	trxTypeSpendMoneyReversal = "SPEND_MONEY_REVERSAL"
	trxTypeSpendMoney         = "SPEND_MONEY"
	trxTypeReceiveMoney       = "RECEIVE_MONEY"

	activityTypeFees               = "fees"
	activityTypeTransferOut        = "transfer_out"
	activityTypeP2P                = "p2p"
	activityTypeQRISCPM            = "cpm"
	activityTypePartnerAPIP2M      = "partnerapi_p2m"
	activityTypeAdhocDebitTopup    = "adhoc_debit_topup"
	activityTypeOFBTopup           = "ofb_topup"
	activityTypeVoucherP2M         = "voucher_p2m"
	activityTypeWithdrawal         = "withdrawal"
	activityTypeMerchantWithdrawal = "merchant_withdrawal"
	activityTypeOPLRepayment       = "opl_repayment"
	activityTypeP2M                = "p2m"
	activityTypeTopup              = "topup"
	activityTypeCashTransfer       = "cash_transfer"
	activityTypeBillPayment        = "bill_payment"
	activityTypeSecureParking      = "secure_parking"
	activityTypeSkyParking         = "sky_parking"
	activityTypePTP                = "ptp"

	additionalFieldKeyPurposeOfTransfer = "transferPurpose"
	additionalFieldKeyMerchantLocation  = "merchantLocation"
	additionalFieldKeyCustomerPAN       = "customerPAN"
	additionalFieldKeyTerminalID        = "terminalID"
	additionalFieldKeyQRISType          = "qrisType"
	additionalFieldKeyRRN               = "RRN"
)

func logErrorTransactionHistory(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.TransactionHistoryLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetTransactionList ...
func GetTransactionList(ctx context.Context, safeID string, request *transactionHistory.GetTransactionsHistoryRequest, t transactionHistory.TxHistory, logTag string) (*transactionHistory.GetTransactionsHistoryResponse, error) {
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, safeID)
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	response, err := t.ListAccountTransactionsSearch(ctx, request)
	if err != nil {
		logErrorTransactionHistory(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetTransactionListMapped retrieves a list of transactions based on the provided request
func GetTransactionListMapped(ctx context.Context, req GetTransactionListRequest, t transactionHistory.TxHistory) ([]map[string]any, map[string]any, error) {
	transactionHistoryBersamaResponse, err := GetTransactionList(ctx, req.SafeID, &transactionHistory.GetTransactionsHistoryRequest{
		AccountID:      req.AccountID,
		EndDate:        req.EndDate,
		StartDate:      req.StartDate,
		PageSize:       req.PageSize,
		EndingAfter:    req.EndingAfter,
		StartingBefore: req.StartingBefore,
		ClientBatchIDs: req.ClientBatchIDs,
	}, t, constants.LogAuditTrailsLogTag)
	if err != nil {
		return nil, nil, err
	}

	var pagination = make(map[string]any)
	pagination["nextCursorID"] = transactionHistoryBersamaResponse.Links["nextCursorID"]
	pagination["prevCursorID"] = transactionHistoryBersamaResponse.Links["prevCursorID"]

	var transactionHistoryResponse = make([]map[string]any, 0)
	for _, transaction := range transactionHistoryBersamaResponse.Data {
		counterpartyName, counterpartyAccountName, counterpartyAccountID := processTransactionCounterParty(transaction.CounterParty, transaction.TransactionMetadata, transaction.TransactionSubtype, transaction.TransactionType)
		referenceID := ProcessTransactionReferenceID(transaction.TransactionSubtype, transaction.OriginalReferenceID, transaction.ReferenceID)

		processedTransaction := map[string]any{
			constants.KeyTransactionID:           transaction.TransactionID,
			constants.KeyPartnerReferenceID:      referenceID,
			constants.KeyOriginalReferenceID:     transaction.OriginalReferenceID,
			constants.KeyErrorDescription:        transaction.TransactionDescription,
			constants.KeyTransactionType:         transaction.TransactionType,
			constants.KeyTransactionSubType:      transaction.TransactionSubtype,
			constants.KeyTransactionBatchID:      transaction.BatchID,
			constants.KeyCreationDate:            utils.ParsingAndFormatTime(transaction.CreationTimestamp.Format(time.DateTime), time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyPostingDate:             utils.ParsingAndFormatTime(transaction.UpdateTimestamp.Format(time.DateTime), time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyTransactionAmount:       utils.HumanizeBalance(transaction.Amount, true),
			constants.KeyTransactionStatus:       transaction.Status,
			constants.KeyCounterpartyName:        counterpartyName,
			constants.KeyCounterpartyAccountName: counterpartyAccountName,
			constants.KeyCounterpartyAccountID:   counterpartyAccountID,
			constants.KeyEndingBalance:           utils.HumanizeBalance(transaction.EndingBalance, true),
		}

		transactionHistoryResponse = append(transactionHistoryResponse, processedTransaction)
	}

	return transactionHistoryResponse, pagination, nil
}

// GetTransactionDetails retrieves transaction details for a specific transaction
func GetTransactionDetails(ctx context.Context, safeID string, req *transactionHistory.GetTransactionDetailRequest, t transactionHistory.TxHistory, logTag string) (*transactionHistory.GetTransactionDetailResponse, error) {
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, safeID)
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	res, err := t.GetTransactionDetail(ctx, req)
	if err != nil {
		logErrorTransactionHistory(ctx, err, logTag)
		return nil, err
	}

	return res, nil
}

type (
	// GetTransactionDetailsRequest is the request structure for getting transaction details
	GetTransactionDetailsRequest struct {
		AccountID     string
		SafeID        string
		TransactionID string
	}

	// GetTransactionListRequest is the request structure for getting transaction list
	GetTransactionListRequest struct {
		AccountID      string
		SafeID         string
		EndDate        string
		StartDate      string
		PageSize       int64
		EndingAfter    string
		StartingBefore string
		ClientBatchIDs []string
	}
)

// GetTransactionDetailsMapped retrieves transaction details for a specific transaction
// and maps the response to a more user-friendly format.
// It processes the transaction details and returns a map with relevant information.
func GetTransactionDetailsMapped(ctx context.Context, req GetTransactionDetailsRequest, t transactionHistory.TxHistory) (map[string]any, error) {
	response, err := GetTransactionDetails(ctx, req.SafeID, &transactionHistory.GetTransactionDetailRequest{
		AccountID:     req.AccountID,
		TransactionID: req.TransactionID,
	}, t, constants.TransactionHistoryLogTag)
	if err != nil {
		return nil, err
	}
	if response.TransactionID == "" {
		return map[string]any{}, nil
	}

	counterpartyName, counterpartyAccountName, counterpartyAccountID := processTransactionCounterParty(response.CounterParty, response.TransactionMetadata, response.TransactionSubtype, response.TransactionType)
	referenceID := ProcessTransactionReferenceID(response.TransactionSubtype, response.OriginalReferenceID, response.ReferenceID)
	externalID := ProcessTransactionExternalID(response.TransactionType, response.TransactionSubtype, response.ReferenceID, response.ExternalID, response.OriginalReferenceID, response.AdditionalData)

	return map[string]any{
		constants.KeyTransactionID:           response.TransactionID,
		constants.KeyPartnerReferenceID:      referenceID,
		constants.KeyTransactionDescription:  response.TransactionDescription,
		constants.KeyTransactionType:         response.TransactionType,
		constants.KeyTransactionSubType:      response.TransactionSubtype,
		constants.KeyTransactionAmount:       utils.HumanizeBalance(response.Amount, true),
		constants.KeyTransactionStatus:       response.Status,
		constants.KeyCounterpartyName:        counterpartyName,
		constants.KeyCounterpartyAccountName: counterpartyAccountName,
		constants.KeyCounterpartyAccountID:   counterpartyAccountID,
		constants.KeyExternalID:              externalID,
		constants.KeyOriginalReferenceID:     response.OriginalReferenceID,
		constants.KeyRemarks:                 response.TransactionRemarks,
		constants.KeyErrorDescription:        response.StatusDescription,
		constants.KeyPurposeOfTransaction:    response.AdditionalData[additionalFieldKeyPurposeOfTransfer],
		constants.KeyMerchantLocation:        response.AdditionalData[additionalFieldKeyMerchantLocation],
		constants.KeyCPAN:                    processTransactionCustomerPAN(response.TransactionType, response.TransactionSubtype, response.CounterParty, response.AdditionalData),
		constants.KeyTerminalID:              response.AdditionalData[additionalFieldKeyTerminalID],
		constants.KeyQRISType:                response.AdditionalData[additionalFieldKeyQRISType],
		constants.KeyActivityType:            response.CounterParty.ActivityType,
		constants.KeyOriginalExternalID:      processTransactionOriginalExternalID(response),
	}, nil
}

// nolint:funlen,gocognit
func processTransactionCounterParty(counterparty *transactionHistory.CounterParty, metadata *transactionHistory.TransactionMetadata, transactionSubType string, transactionType string) (counterpartyName string, counterpartyAccountName string, counterpartyAccountID string) {
	if counterparty == nil {
		return "", "", ""
	}
	if metadata == nil {
		metadata = &transactionHistory.TransactionMetadata{
			SenderDetails:      &transactionHistory.SenderDetails{},
			BeneficiaryDetails: &transactionHistory.BeneficiaryDetails{},
		}
	}

	//set default
	counterpartyName = counterparty.BankName
	counterpartyAccountName = counterparty.AccountName
	counterpartyAccountID = counterparty.AccountID

	switch transactionSubType {
	case trxSubtypeSavingsOvo:
		counterpartyName = counterparty.MerchantName

		switch counterparty.ActivityType {
		case activityTypeTransferOut,
			activityTypeFees:
			counterpartyName = counterparty.MerchantName
			counterpartyAccountName = counterparty.FullName
			counterpartyAccountID = counterparty.AccountID
		case activityTypeP2P:
			counterpartyName = counterparty.PartnerName
			if transactionType == trxTypeReceiveMoney {
				counterpartyAccountName = metadata.SenderDetails.AccountName
				counterpartyAccountID = metadata.SenderDetails.AccountNo
			} else if transactionType == trxTypeSpendMoney {
				counterpartyAccountName = metadata.BeneficiaryDetails.AccountName
				counterpartyAccountID = metadata.BeneficiaryDetails.AccountNo
			}
		case activityTypePartnerAPIP2M,
			activityTypeVoucherP2M:
			counterpartyName = counterparty.PartnerName
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantID
		case activityTypeQRISCPM:
			counterpartyName = counterparty.Acquirer
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantID
		case activityTypeOPLRepayment:
			counterpartyName = counterparty.PartnerName
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = metadata.BeneficiaryDetails.AccountNo
		case activityTypeWithdrawal,
			activityTypeMerchantWithdrawal:
			counterpartyName = counterparty.MerchantName
			counterpartyAccountName = counterparty.AccountName
			counterpartyAccountID = counterparty.AccountID
		case activityTypeAdhocDebitTopup:
			counterpartyName = ""
			counterpartyAccountName = metadata.SenderDetails.AccountName
			counterpartyAccountID = metadata.SenderDetails.AccountNo
		case activityTypeOFBTopup:
			counterpartyName = counterparty.FullName
			counterpartyAccountName = counterparty.AccountName
			counterpartyAccountID = counterparty.AccountID
		case activityTypeP2M:
			counterpartyName = counterparty.Acquirer
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantPAN
		case activityTypeTopup:
			counterpartyName = counterparty.PartnerName
			counterpartyAccountName = counterparty.AccountName
			counterpartyAccountID = counterparty.AccountID
		case activityTypeCashTransfer:
			counterpartyName = counterparty.PartnerName
			counterpartyAccountName = counterparty.AccountName
			counterpartyAccountID = counterparty.AccountID
		case activityTypeBillPayment:
			counterpartyName = ""
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.DestinationID
		case activityTypeSecureParking, activityTypeSkyParking:
			counterpartyName = ""
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantID
		case activityTypePTP:
			counterpartyName = ""
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantID
		default:
			switch transactionType {
			case trxTypeSpendMoney:
				counterpartyName = counterparty.BankName
				counterpartyAccountName = counterparty.MerchantName
			case trxTypeSpendMoneyReversal:
				counterpartyName = metadata.SenderDetails.AccountName
				counterpartyAccountName = counterparty.MerchantName
			}
		}
	case trxSubtypeQRISAlto:
		counterpartyAccountName = counterparty.MerchantName
	case trxSubtypeGrab:
		if counterparty.ActivityType == activityTypeBillPayment {
			counterpartyName = counterparty.PartnerName
			counterpartyAccountName = counterparty.MerchantName
			counterpartyAccountID = counterparty.MerchantID
		}
	}
	return counterpartyName, counterpartyAccountName, counterpartyAccountID
}

func processTransactionCustomerPAN(transactionType string, transactionSubType string, counterparty *transactionHistory.CounterParty, additionalData map[string]string) (customerPAN string) {
	customerPAN = additionalData[additionalFieldKeyCustomerPAN]
	switch transactionSubType {
	case trxSubtypeSavingsOvo:
		switch transactionType {
		case trxTypeSpendMoney:
			if counterparty.ActivityType == activityTypeQRISCPM || counterparty.ActivityType == activityTypeP2M {
				customerPAN = counterparty.CustomerPAN
			}
		}
	}
	return customerPAN
}

func processTransactionOriginalExternalID(res *transactionHistory.GetTransactionDetailResponse) string {
	if res == nil {
		return ""
	}

	if res.TransactionSubtype == trxSubtypeSavingsOvo && res.TransactionType == trxTypeSpendMoneyReversal {
		return res.ExternalID
	}

	return ""
}

// ProcessTransactionExternalID ...
func ProcessTransactionExternalID(txType, transactionSubType, referenceID, externalID, originalExternalID string, additionalData map[string]string) string {
	switch transactionSubType {
	case trxSubtypeQRISAlto:
		rrn := additionalData[additionalFieldKeyRRN]
		if rrn == "" {
			return externalID
		}
		return rrn

	case trxSubtypeSavingsOvo:
		if txType == trxTypeSpendMoneyReversal {
			return originalExternalID
		}
	}

	return externalID
}

// ProcessTransactionReferenceID ...
func ProcessTransactionReferenceID(transactionSubType string, originalReferenceID string, referenceID string) string {
	switch transactionSubType {
	case trxSubtypeQRISAlto:
		referenceID = ""
	case trxSubTypeBiFast:
		referenceID = ""
	}

	return referenceID
}
