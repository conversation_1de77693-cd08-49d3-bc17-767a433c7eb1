package helper

import (
	"context"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	txnLimit "gitlab.myteksi.net/dakota/transaction-limit/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func logErrorTransactionLimit(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.TransactionLimitLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// FetchCustomerTransferLimit ...
func FetchCustomerTransferLimit(ctx context.Context, request *txnLimit.GetTransactionLimitRequestV2, safeID string, t txnLimit.TransactionLimit, logTag string) (*txnLimit.GetTransactionLimitResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, safeID)
	withHTTPHeader = commonCtx.WithHTTPHeader(withHTTPHeader, commonCtx.HeaderXServiceID, constants.DIGIBANK)
	response, err := t.SearchTransactionLimitRulesV2(withHTTPHeader, request)
	if err != nil {
		logErrorTransactionLimit(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetTransferLimit ...
func GetTransferLimit(ctx context.Context, request *txnLimit.GetTransactionLimitRulesRequest, userEmail string, partnerID string, t txnLimit.TransactionLimit, logTag string) (*txnLimit.GetTransactionLimitRulesResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXEmailIdentifier, userEmail)
	withHTTPHeader = commonCtx.WithHTTPHeader(withHTTPHeader, commonCtx.HeaderOAuthPartnerIDHeader, partnerID)
	response, err := t.ListTransactionLimitRules(withHTTPHeader, request)
	if err != nil {
		logErrorTransactionLimit(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}
