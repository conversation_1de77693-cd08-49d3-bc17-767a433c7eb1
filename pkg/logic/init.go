// Package logic is the package that contains all the business logic of the application.
package logic

import (
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/redis"
	loanAppAPI "gitlab.myteksi.net/dakota/lending/loan-app/api"
	loanCoreAPI "gitlab.myteksi.net/dakota/lending/loan-core/api"
	loanExpAPI "gitlab.myteksi.net/dakota/lending/loan-exp/api"
	paymentOpsTrfAPI "gitlab.myteksi.net/dakota/payment-ops-trf/api"
	payAuthZAPI "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	productMasterAPI "gitlab.super-id.net/bersama/core-banking/product-master/api"
	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerJourneyExperienceAPI "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	transactionHistory "gitlab.super-id.net/bersama/deposit/transaction-history/api"
	amlServiceAPI "gitlab.super-id.net/bersama/fintrust/aml-service/api"
	customerExperienceAPI "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	customerMasterAPI "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	chatbotAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/chatbot"
	customerExperienceHttpAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
	grabIDAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid"
	hedwigAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig"
	opsAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/ops"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

type process struct {
	QueueByElementCodes map[constants.ElementCodes]sqs.QueueClient

	AppConfig                                 *config.AppConfig                                `inject:"config"`
	RedisClient                               redis.Client                                     `inject:"client.redisClient"`
	S3Client                                  s3client.S3                                      `inject:"client.s3"`
	Hedwig                                    hedwigAPI.Hedwig                                 `inject:"client.Hedwig"`
	AccountServiceClient                      accountServiceAPI.AccountService                 `inject:"client.accountService"`
	PayAuthZClient                            payAuthZAPI.PayAuthz                             `inject:"client.payAuthZ"`
	PaymentOpsTrfClient                       paymentOpsTrfAPI.PaymentOpsTransfer              `inject:"client.paymentOpsTrf"`
	CustomerExperienceClient                  customerExperienceAPI.CustomerExperience         `inject:"client.customerExperience"`
	CustomerMasterClient                      customerMasterAPI.CustomerMaster                 `inject:"client.customerMaster"`
	ProductMasterClient                       productMasterAPI.ProductMaster                   `inject:"client.productMaster"`
	TransactionHistoryClient                  transactionHistory.TxHistory                     `inject:"client.transactionHistory"`
	TransactionLimitClient                    transactionLimitAPI.TransactionLimit             `inject:"client.transactionLimit"`
	CustomerJournalClient                     customerJournalAPI.CustomerJournal               `inject:"client.customerJournal"`
	CustomerJourneyExperiencePreferenceClient customerJourneyExperienceAPI.PreferenceCenter    `inject:"client.customerJourneyExperiencePreference"`
	AmlServiceCustomerClient                  amlServiceAPI.Customer                           `inject:"client.amlServiceCustomer"`
	LoanAppClient                             loanAppAPI.LoanApp                               `inject:"client.loanApp"`
	LoanExpClient                             loanExpAPI.LoanExp                               `inject:"client.loanExp"`
	LoanCoreClient                            loanCoreAPI.LoanCore                             `inject:"client.loanCore"`
	CustomerExperienceHttpClient              customerExperienceHttpAPI.CustomerExperienceHTTP `inject:"client.customerExperienceHttp"`
	ChatbotClient                             chatbotAPI.Chatbot                               `inject:"client.chatbot"`
	GrabIDClient                              grabIDAPI.GrabID                                 `inject:"client.grabid"`
	OpsService                                opsAPI.OpsHTTP                                   `inject:"client.ops"`

	QueueOpsUnlinkAccount    sqs.QueueClient `inject:"queue.opsUnlinkAccount"`
	QueueOpsTransferOnBehalf sqs.QueueClient `inject:"queue.opsTransferOnBehalf"`
	QueueOpsBlockAccount     sqs.QueueClient `inject:"queue.opsBlockAccount"`
	QueueOpsUnblockAccount   sqs.QueueClient `inject:"queue.opsUnblockAccount"`
}

var (
	// Process is the interactor for all logic
	Process = &process{}
)

// InitLogic ...
func InitLogic(app *servus.Application) {
	app.MustRegister("process", Process)
	Process.init()
}

func (p *process) init() {
	p.QueueByElementCodes = map[constants.ElementCodes]sqs.QueueClient{
		constants.BlockAccount:   p.QueueOpsBlockAccount,
		constants.UnblockAccount: p.QueueOpsUnblockAccount,
	}
}
