package logic

import (
	loanAppMock "gitlab.myteksi.net/dakota/lending/loan-app/api/mock"
	loanExpMock "gitlab.myteksi.net/dakota/lending/loan-exp/api/mock"
	payAuthzMock "gitlab.myteksi.net/dakota/payment/pay-authz/api/mock"
	txLimitMock "gitlab.myteksi.net/dakota/transaction-limit/api/mock"
	accountServiceBersamaMock "gitlab.super-id.net/bersama/core-banking/account-service/api/mock"
	productMasterMock "gitlab.super-id.net/bersama/core-banking/product-master/api/mock"
	customerJournalMock "gitlab.super-id.net/bersama/corex/customer-journal/api/mock"
	customerJourneyExperiencelMock "gitlab.super-id.net/bersama/corex/customer-journey-experience/api/mock"
	txHistoryMock "gitlab.super-id.net/bersama/deposit/transaction-history/api/mock"
	amlServiceMock "gitlab.super-id.net/bersama/fintrust/aml-service/api/mock"
	customerExperienceMock "gitlab.super-id.net/bersama/onboarding/customer-experience/api/mock"
	customerMasterMock "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2/mock"
	grabIDMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

var (
	// MockProcess ...
	MockProcess = &process{}
)

type MockProcessConfig struct {
	AppConfig                           *config.AppConfig
	CustomerExperience                  *customerExperienceMock.CustomerExperience
	CustomerMaster                      *customerMasterMock.CustomerMaster
	AccountService                      *accountServiceBersamaMock.AccountService
	ProductMaster                       *productMasterMock.ProductMaster
	TransactionHistory                  *txHistoryMock.TxHistory
	TxLimit                             *txLimitMock.TransactionLimit
	CustomerJournal                     *customerJournalMock.CustomerJournal
	CustomerJourneyExperiencePreference *customerJourneyExperiencelMock.PreferenceCenter
	AmlServiceCustomer                  *amlServiceMock.Customer
	PayAuthz                            *payAuthzMock.PayAuthz
	LoanExp                             *loanExpMock.LoanExp
	LoanApp                             *loanAppMock.LoanApp
	GrabID                              *grabIDMock.GrabID
}

// MockInitLogic ...
func MockInitLogic(mockConfig *MockProcessConfig) {
	MockProcess.AppConfig = mockConfig.AppConfig
	MockProcess.CustomerExperienceClient = mockConfig.CustomerExperience
	MockProcess.CustomerMasterClient = mockConfig.CustomerMaster
	MockProcess.AccountServiceClient = mockConfig.AccountService
	MockProcess.ProductMasterClient = mockConfig.ProductMaster
	MockProcess.TransactionHistoryClient = mockConfig.TransactionHistory
	MockProcess.TransactionLimitClient = mockConfig.TxLimit
	MockProcess.CustomerJournalClient = mockConfig.CustomerJournal
	MockProcess.CustomerJourneyExperiencePreferenceClient = mockConfig.CustomerJourneyExperiencePreference
	MockProcess.AmlServiceCustomerClient = mockConfig.AmlServiceCustomer
	MockProcess.PayAuthZClient = mockConfig.PayAuthz
	MockProcess.LoanExpClient = mockConfig.LoanExp
	MockProcess.LoanAppClient = mockConfig.LoanApp
	MockProcess.GrabIDClient = mockConfig.GrabID

	Process = MockProcess
}
