// Package logic SendNotification implement send notification to hedwig
package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	hedwigAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"
)

// SendNotificationAction is request for send notification after action
type SendNotificationAction struct {
	ActionType       constants.ActionType
	SafeID           string
	TicketID         string
	NotificationType api.SendNotificationType
	Params           map[string]string
}

// SendNotification is the business logic for sending notification after action - STILL UNUSED
func (p *process) SendNotification(ctx context.Context, req SendNotificationAction) {
	// TODO: construct send notification request per action
	var desc string
	notificationReq := constructNotificationByAction(req)

	_, err := SendNotificationLogic(ctx, notificationReq, p.Hedwig, p.AppConfig)
	if err != nil {
		desc = fmt.Sprintf("Send %s is failed - %s", constants.NotificationDescriptionMap[req.NotificationType], err.Error())
	} else {
		desc = fmt.Sprintf("Send %s is success", constants.NotificationDescriptionMap[req.NotificationType])
	}

	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     req.TicketID,
		IdentifierType: constants.TicketID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1, // FIXME: Hardcoded
			Valid: true,
		},
		Title:        desc,
		Description:  desc,
		ActivityType: constants.Ticket,
		// TODO: fill in extra params?
	}

	_, err = auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SendNotificationLogTag, "failed inserting audit trails: "+err.Error(), utils.GetTraceID(ctx))
	}
}

// constructNotificationByAction is for construct send notification request after action
func constructNotificationByAction(req SendNotificationAction) *api.SendNotificationRequest {
	templates := constants.NotificationTemplateMap[req.ActionType]
	var templateID string
	switch req.NotificationType {
	case api.SendNotificationType_PUSH_INBOX:
		templateID = templates.PushInboxTemplate
	case api.SendNotificationType_PUSH:
		templateID = templates.PushTemplate
	case api.SendNotificationType_EMAIL:
		templateID = templates.EmailTemplate
	default:
		templateID = ""
	}

	notificationReq := &api.SendNotificationRequest{
		RecipientID:      req.SafeID,
		NotificationType: req.NotificationType,
		Template: &api.Template{
			Id:       templateID,
			Language: constants.ID,
			Params:   req.Params,
		},
	}

	return notificationReq
}

// SendNotificationLogic is to handle business logic from send notification API
func SendNotificationLogic(ctx context.Context, req *api.SendNotificationRequest, hedwigClient hedwigAPI.Hedwig, appConfig *config.AppConfig) (*api.SendNotificationResponse, error) {
	var res *api.SendNotificationResponse
	var err error

	switch req.NotificationType {
	case api.SendNotificationType_PUSH_INBOX:
		res, err = SendPushInbox(ctx, req, hedwigClient, appConfig)
	case api.SendNotificationType_PUSH:
		res, err = SendPush(ctx, req, hedwigClient, appConfig)
	case api.SendNotificationType_EMAIL:
		res, err = SendEmail(ctx, req, hedwigClient, appConfig)
	default:
		err = servus.ServiceError{
			HTTPCode: api.FieldMissing.HTTPStatusCode(),
			Code:     string(api.FieldMissing),
			Message:  "notification type is missing/invalid in request",
		}
		slogwrapper.FromContext(ctx).Warn(constants.RequestSendNotificationValidationLogTag, "notification type is missing or invalid in request", utils.GetTraceID(ctx))
		return nil, err
	}

	return res, err
}

// SendPushInbox ...
func SendPushInbox(ctx context.Context, req *api.SendNotificationRequest, hedwigClient hedwigAPI.Hedwig, appConfig *config.AppConfig) (*api.SendNotificationResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, constants.XGrabkitClientID, constants.OnedashAPI)
	template := req.Template
	if template.Id != "" {
		template.Id = fetchTemplateID(api.SendNotificationType_PUSH_INBOX, template.Id, appConfig)
	}

	if template != nil && template.Params == nil {
		template.Params = make(map[string]string)
	}

	if _, ok := template.Params["message_date"]; !ok {
		currentTime := time.Now()
		loc, _ := time.LoadLocation("Asia/Jakarta")
		currentTime = currentTime.In(loc)
		template.Params["message_date"] = currentTime.Format("02 Jan 2006, 03:04PM")
	}

	hedwigReq := &hedwigAPI.PushInboxRequest{
		RecipientID:   req.RecipientID,
		RecipientType: constants.DIGIBANK,
		Template: &hedwigAPI.Template{
			ID:       template.Id,
			Language: template.Language,
			Params:   template.Params,
		},
	}

	slogwrapper.FromContext(ctx).Info(constants.PushInboxNotificationLogTag, "request to send push inbox notification", utils.GetTraceID(ctx))
	response, err := hedwigClient.PushInbox(withHTTPHeader, hedwigReq)
	if err != nil {
		slogwrapper.FromContext(ctx).Warn(constants.PushInboxNotificationLogTag, "unable to fetch push inbox notification"+err.Error())
		return nil, errorwrapper.GetHTTPErrorResponse(err, "unable to fetch push inbox notification")
	}

	return &api.SendNotificationResponse{
		MessageID: response.MessageID,
	}, nil
}

// SendPush ...
func SendPush(ctx context.Context, req *api.SendNotificationRequest, hedwigClient hedwigAPI.Hedwig, appConfig *config.AppConfig) (*api.SendNotificationResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, constants.XGrabkitClientID, constants.OnedashAPI)
	template := req.Template
	template.Id = fetchTemplateID(api.SendNotificationType_PUSH, template.Id, appConfig)

	hedwigReq := &hedwigAPI.PushRequest{
		RecipientID:   req.RecipientID,
		RecipientType: constants.DIGIBANK,
		Template: &hedwigAPI.Template{
			ID:       template.Id,
			Language: template.Language,
			Params:   template.Params,
		},
	}

	slogwrapper.FromContext(ctx).Info(constants.PushNotificationLogTag, "request to send push notification", utils.GetTraceID(ctx))
	response, err := hedwigClient.Push(withHTTPHeader, hedwigReq)
	if err != nil {
		slogwrapper.FromContext(ctx).Warn(constants.PushNotificationLogTag, "unable to fetch push notification"+err.Error())
		return nil, errorwrapper.GetHTTPErrorResponse(err, "unable to fetch push notification")
	}

	return &api.SendNotificationResponse{
		MessageID: response.MessageID,
	}, nil
}

// SendEmail ...
func SendEmail(ctx context.Context, req *api.SendNotificationRequest, hedwigClient hedwigAPI.Hedwig, appConfig *config.AppConfig) (*api.SendNotificationResponse, error) {
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, constants.XGrabkitClientID, constants.OnedashAPI)

	template := req.Template
	template.Id = fetchTemplateID(api.SendNotificationType_EMAIL, template.Id, appConfig)

	hedwigReq := &hedwigAPI.EmailRequest{
		RecipientID:   req.RecipientID,
		RecipientType: constants.DIGIBANK,
		Template: &hedwigAPI.Template{
			ID:       template.Id,
			Language: template.Language,
			Params:   template.Params,
		},
		Attachments: nil,
	}

	if len(req.Attachments) > 0 {
		emailAttachments := make([]hedwigAPI.Attachments, 0)
		for _, attachment := range req.Attachments {
			emailAttachments = append(emailAttachments, hedwigAPI.Attachments{
				FileName: attachment.Filename,
				File:     attachment.File,
				URL:      attachment.Url,
			})
		}
		hedwigReq.Attachments = emailAttachments
	}

	slogwrapper.FromContext(ctx).Info(constants.EmailNotificationLogTag, "request to send email notification", utils.GetTraceID(ctx))
	response, err := hedwigClient.Email(withHTTPHeader, hedwigReq)
	if err != nil {
		slogwrapper.FromContext(ctx).Warn(constants.EmailNotificationLogTag, "unable to fetch email notification"+err.Error())
		return nil, errorwrapper.GetHTTPErrorResponse(err, "unable to fetch email notification")
	}

	return &api.SendNotificationResponse{
		MessageID: response.MessageID,
	}, nil
}

func fetchTemplateID(notificationType api.SendNotificationType, id string, appConfig *config.AppConfig) string {
	if id == "" {
		return id
	}
	var templateID string
	switch notificationType {
	case api.SendNotificationType_PUSH_INBOX:
		templateID = appConfig.Hedwig.PushInboxNotificationTemplateMappings[id]
	case api.SendNotificationType_PUSH:
		templateID = appConfig.Hedwig.PushNotificationTemplateMappings[id]
	case api.SendNotificationType_EMAIL:
		templateID = appConfig.Hedwig.EmailNotificationTemplateMappings[id]
	default:
		templateID = id
	}

	return templateID
}
