// Package stream ...
package stream

import (
	"context"
	"database/sql"
	"fmt"
	"runtime/debug"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/account_detail"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	keyPrefix = "close_account_"
)

// HandleDepositsAccountDetailsStream responsible for handling the account details stream
func HandleDepositsAccountDetailsStream(ctx context.Context, conf *config.AppConfig, depositsAccountDetail account_detail.AccountDetail) error {
	slog.FromContext(ctx).Info(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("Received event from deposits-core-streams: %+v", depositsAccountDetail))

	defer func() {
		if err := recover(); err != nil {
			slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("Panic occurred. error: %s, stacktrace %s", err, string(debug.Stack())))
		} else {
			slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("Executed successfully %v ", depositsAccountDetail))
		}
	}()

	if depositsAccountDetail.Account == nil || depositsAccountDetail.Account.Status != "CLOSED" {
		return nil
	}

	accountID := depositsAccountDetail.Account.ID
	if err := processAccount(ctx, conf, accountID); err != nil {
		return err
	}

	return nil
}

func processAccount(ctx context.Context, conf *config.AppConfig, accountID string) error {
	// get data from redis to see if account exists
	queueRedis, err := redis.GetRedisValue(ctx, keyPrefix+accountID, constants.DepositsAccountDetailsStreamHandlerLogTag)
	if err != nil {
		return err
	}

	// if account exists, update status to closed on DB
	if queueRedis == "" {
		slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("no data in redis for accountID %s", accountID))
		return fmt.Errorf("no data in redis for accountID %s", accountID)
	}

	if err = updateDatabase(ctx, conf, accountID); err != nil {
		return err
	}

	// delete redis cache
	if err = redis.DeleteRedisKey(ctx, keyPrefix+accountID, constants.DepositsAccountDetailsStreamHandlerLogTag); err != nil {
		return err
	}

	// update audit trails
	// handle notification
	// TODO: callback appian

	return nil
}

func updateDatabase(ctx context.Context, conf *config.AppConfig, accountID string) error {
	// will use redis to get queue db data in the future
	//var queueJSON api.UpdateCASAAccountStatusRequest
	//if err := json.Unmarshal([]byte(queueRedis), &queueJSON); err != nil {
	//	slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("error unmarshaling redis value for account %s with error: %s", accountID, err.Error()))
	//	return err
	//}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, conf.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error("stream.DepositAccountDetail", fmt.Sprintf("error getting database handle: %v", err.Error()))
		return err
	}

	// temp get data from database, later will be through redis
	queueDB, err := storage.GetQueueByIdentifierAndEvent(ctx, db, accountID, "close_account")
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("DB error when getting queue for account %s, error: %s", accountID, err))
		return err
	}

	queueDB.Status = 1 // TODO: change to closed/done status
	queueDB.UpdatedBy = sql.NullInt64{Int64: 1, Valid: true}
	queueDB.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}

	if _, err = storage.UpdateQueue(ctx, db, queueDB); err != nil {
		slog.FromContext(ctx).Warn(constants.DepositsAccountDetailsStreamHandlerLogTag, fmt.Sprintf("DB error when getting detail for account %s, error: %s", accountID, err))
		return err
	}

	return nil
}
