package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// SystemProceedTicketStatus proceed the ticket status based on ticket chain, it will proceed the chain with action SYSTEM_EXECUTE
//
// nolint: funlen, errcheck
func (p *process) SystemProceedTicketStatus(ctx context.Context, ticketID int64) error {
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return err
	}

	// Get ticket by id
	ticketDTO, err := storage.GetTicketByID(ctx, master, ticketID)
	if err != nil {
		return errorwrapper.Error(apiError.ResourceNotFound, "failed to get ticket by id")
	}

	// Get ticket chain by element id
	chains, err := storage.GetTicketChainByElementIDMap(ctx, master, ticketDTO.ElementID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain by element id")
	}
	chain, ok := chains[constants.ActionSystemExecute]
	if !ok {
		return errorwrapper.Error(apiError.ResourceNotFound, "ticket chain not found")
	}

	// Check if ticket status is ready for execution
	if chain.CurrentStatusID != ticketDTO.TicketStatusID {
		return errorwrapper.Error(apiError.BadRequest, "ticket status is not valid")
	}

	// Proceed ticket status
	prevStatusID := ticketDTO.TicketStatusID
	ticketDTO.TicketStatusID = chain.NextStatusID
	ticketDTO.UpdatedBy = sql.NullInt64{Int64: constants.SystemUserID, Valid: true}
	ticketDTO.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}

	// Begin transaction
	tx, err := master.Begin()
	defer tx.Rollback()
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to begin transaction")
	}

	// update ticket status
	err = storage.UpdateTicket(ctx, tx, ticketDTO)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket")
	}

	// Insert the ticket history
	_, err = storage.CreateTicketHistory(ctx, tx, &storage.TicketHistoryDTO{
		TicketID:     ticketDTO.ID,
		Data:         ticketDTO.Data,
		ActionName:   constants.ActionSystemExecute,
		PrevStatusID: prevStatusID,
		NextStatusID: ticketDTO.TicketStatusID,
		CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:    sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
	})
	if err != nil {
		return errorwrapper.WrapError(err, apiError.Idem, "failed to create ticket history")
	}

	// commit transaction
	err = tx.Commit()
	if err != nil {
		// Create audit trail for system task failure
		createSystemTaskFailedAuditTrail(ctx, ticketDTO, err)
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to commit transaction")
	}

	// Create audit trail for successful system task execution
	createSystemTaskSuccessAuditTrail(ctx, ticketDTO, prevStatusID)

	return nil
}

// createSystemTaskFailedAuditTrail creates an audit trail entry for a failed system task
func createSystemTaskFailedAuditTrail(ctx context.Context, ticketDTO *storage.TicketDTO, err error) {
	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":     ticketDTO.ID,
		"element_id":    ticketDTO.ElementID,
		"priority_id":   ticketDTO.PriorityID,
		"status_id":     ticketDTO.TicketStatusID,
		"source":        ticketDTO.Source,
		"deadline_time": ticketDTO.DeadlineTime.Time,
		"assignee_id":   ticketDTO.AssigneeUserID.Int64,
		"data":          ticketDTO.Data,
		"event_type":    constants.SystemTaskFailed,
		"error_details": err.Error(),
		"error_code":    "SYSTEM_ERROR",
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketDTO.ID)),
		IdentifierType: constants.TicketID,
		Title:          "System Task Failed",
		Description:    fmt.Sprintf("System task failed for ticket with id %d: %s", ticketDTO.ID, err.Error()),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}

// createSystemTaskSuccessAuditTrail creates an audit trail entry for a successful system task execution
func createSystemTaskSuccessAuditTrail(ctx context.Context, ticketDTO *storage.TicketDTO, prevStatusID int64) {
	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":      ticketDTO.ID,
		"element_id":     ticketDTO.ElementID,
		"priority_id":    ticketDTO.PriorityID,
		"prev_status_id": prevStatusID,
		"next_status_id": ticketDTO.TicketStatusID,
		"source":         ticketDTO.Source,
		"deadline_time":  ticketDTO.DeadlineTime.Time,
		"assignee_id":    ticketDTO.AssigneeUserID.Int64,
		"data":           ticketDTO.Data,
		"event_type":     constants.SystemTaskCompleted,
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: constants.SystemUserID, Valid: true},
		Identifier:     strconv.Itoa(int(ticketDTO.ID)),
		IdentifierType: constants.TicketID,
		Title:          "System Task Completed",
		Description:    fmt.Sprintf("System task completed for ticket with id %d", ticketDTO.ID),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}
