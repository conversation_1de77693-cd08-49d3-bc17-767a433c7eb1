package logic

import (
	"context"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// UnblockAccountRequest ...
type UnblockAccountRequest struct {
	AccountID          string   `json:"AccountID" validate:"account_id"`
	IdempotencyKey     string   `json:"IdempotencyKey" validate:"required"`
	HoldCodes          []string `json:"HoldCodes" validate:"block_unblock_hold_codes"`
	UpdatedBy          string   `json:"UpdatedBy"`
	TicketID           string   `json:"TicketID"`
	SafeID             string   `json:"SafeID"` // Can be removed from the request by get by account id
	IsSendNotification bool     `json:"IsSendNotification"`
}

// UnblockAccountResponse ...
type UnblockAccountResponse struct {
	AccountID     string `json:"AccountID,omitempty"`
	Status        string `json:"Status,omitempty"`
	FailureReason string `json:"FailureReason,omitempty"`
	QueueFeedback
}

// UnblockAccountNotificationParams ...
type UnblockAccountNotificationParams struct {
	UserName       string `json:"user_name"`
	AccountWording string `json:"account_wording"`
	AccountName    string `json:"account_name"`
	AccountIDs     string `json:"account_ids"`
	MessageDate    string `json:"message_date"`
}

// UnblockAccount is the business logic for unblocking an account.
//
// nolint: gocyclo, funlen, dupl
func (p *process) UnblockAccount(ctx context.Context, req *UnblockAccountRequest) (*UnblockAccountResponse, error) {
	if req == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	var res = &UnblockAccountResponse{}

	// fetch the applicable hold codes
	currentHoldCodes, err := p.getCurrentAccountHoldCodes(ctx, req.AccountID)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "error fetching hold codes", slog.Error(err))
		return nil, errorwrapper.Error(apiError.BadRequest, "account is not blocked")
	}

	// remove the WHOLE_BALANCE_HOLD from the hold codes
	updatedHoldCodes, isValid := removeBlockAccountHoldCode(currentHoldCodes, req.HoldCodes)
	if !isValid {
		return nil, errorwrapper.Error(apiError.BadRequest, "account is not blocked")
	}

	// send request to account service to update the hold codes to unblock the account
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, req.IdempotencyKey)
	resUpdate, err := p.AccountServiceClient.UpdateCASAAccountParameters(ctx, &accountServiceAPI.UpdateCASAAccountParametersRequest{
		AccountID: req.AccountID,
		ProductSpecificParameters: &accountServiceAPI.CASAAccountParams{
			ApplicableHoldcodes: updatedHoldCodes,
		},
		UpdatedBy: req.UpdatedBy,
	})
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "error updating account", slog.Error(err))
		return &UnblockAccountResponse{QueueFeedback: QueueFeedback{NeedRequeue: true}}, errorwrapper.WrapError(err, apiError.Idem, "failed to update account")
	}

	res = &UnblockAccountResponse{
		AccountID:     resUpdate.AccountID,
		Status:        resUpdate.Status,
		FailureReason: resUpdate.FailureReason,
	}

	// send notification if required
	if req.IsSendNotification {
		params, err := structToMap(UnblockAccountNotificationParams{
			UserName:       "", // FIXME
			AccountWording: "", // FIXME
			AccountName:    "", // FIXME
			AccountIDs:     "", // FIXME
			MessageDate:    time.Now().Format("2006-01-02 15:04:05"),
		})
		if err != nil {
			return nil, errorwrapper.Error(apiError.Idem, "failed to marshal notification params")
		}

		// FIXME: send notification email
		p.SendNotification(ctx, SendNotificationAction{
			ActionType:       constants.UnblockAccountAction,
			SafeID:           req.AccountID,
			TicketID:         req.TicketID,
			NotificationType: api.SendNotificationType_EMAIL,
			Params:           params,
		})

		// FIXME: send notification push inbox, fix params
		p.SendNotification(ctx, SendNotificationAction{
			ActionType:       constants.UnblockAccountAction,
			SafeID:           req.AccountID,
			TicketID:         req.TicketID,
			NotificationType: api.SendNotificationType_PUSH_INBOX,
			Params:           params,
		})
	}

	return &UnblockAccountResponse{
		AccountID:     res.AccountID,
		Status:        res.Status,
		FailureReason: res.FailureReason,
	}, nil
}

// removeBlockAccountHoldCode will remove hold code based on request
// Second return will be true if the hold code is removed/found, false otherwise. Proceed only if second return is true.
func removeBlockAccountHoldCode(existing []accountServiceAPI.ApplicableHoldcode, req []string) ([]accountServiceAPI.ApplicableHoldcode, bool) {
	var updatedHoldCodes = make([]accountServiceAPI.ApplicableHoldcode, 0)
	var reqMap = make(map[accountServiceAPI.ApplicableHoldcode]bool)
	var expectedLen = len(existing) - len(req)

	// if existing hold codes is empty, will invalidate the request
	if len(existing) == 0 {
		return existing, false
	}

	// make map for faster lookup
	for _, code := range req {
		reqMap[accountServiceAPI.ApplicableHoldcode(code)] = true
	}

	// the idea is forming a new slice with hold codes that are not in the request
	for _, holdCode := range existing {
		if !reqMap[holdCode] {
			updatedHoldCodes = append(updatedHoldCodes, holdCode)
		}
	}

	// check the validity of new hold codes by comparing the slice length
	if expectedLen != len(updatedHoldCodes) {
		return existing, false
	}

	return updatedHoldCodes, true
}
