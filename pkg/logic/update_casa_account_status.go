package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// UpdateCASAAccountStatus is business logic for updating casa account status.
func (p *process) UpdateCASAAccountStatus(ctx context.Context, message interface{}) {
	req := message.(*api.UpdateCASAAccountStatusRequest)
	if req == nil {
		return
	}

	err := validations.ValidateUpdateCASAAccountStatus(ctx, req)
	if err != nil {
		return
	}

	// temp set data from req, in future will include queueID
	err = redis.SetRedisValue(ctx, "close_account_"+req.AccountID, req, p.AppConfig.RedisAppConfig.ExpiryTimeForRedis, constants.UpdateCASAAccountStatusLogTag)
	if err != nil {
		return
	}

	// send request
	updateRes, err := updateCASAAccountStatus(ctx, req, p.AccountServiceClient)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateCASAAccountStatusLogTag, constants.AccountServiceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		fmt.Printf("result: %s", err.Error())
	}

	// write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1, // FIXME: Hardcoded
			Valid: true,
		},
		Identifier:     req.TicketID,
		IdentifierType: constants.TicketID,
		Title:          "Update CASA Account status failed",
		Description:    fmt.Sprintf("Update Account %s status has been successfully called", req.AccountID),
		ActivityType:   constants.Ticket,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.UpdateCASAAccountStatusLogTag, fmt.Sprintf("error update casa status req: %v, error: %v", auditTrailReq, auditErr))
	}

	// temp
	if updateRes != nil {
		fmt.Printf("result: %v", updateRes)
	}

	// TODO: callback ke appian
	// go sendCallbackToAppian()
}

func updateCASAAccountStatus(ctx context.Context, req *api.UpdateCASAAccountStatusRequest, p accountServiceAPI.AccountService) (*api.UpdateCASAAccountStatusResponse, error) {
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, req.IdempotencyKey)
	res, err := p.UpdateCASAAccountStatus(ctx, &accountServiceAPI.UpdateCASAAccountStatusRequest{
		AccountID:        req.AccountID,
		Status:           accountServiceAPI.AccountStatus(req.Status),
		ClosingTimestamp: req.ClosingTimestamp,
		UpdatedBy:        req.UpdatedBy,
	})
	if err != nil {
		fmt.Printf("error Update CASA Account Status: %s", err.Error())
		return nil, err
	}

	return &api.UpdateCASAAccountStatusResponse{
		AccountID: res.AccountID,
		Status:    api.AccountStatus(res.Status),
		UpdatedBy: res.UpdatedBy,
	}, nil
}
