package logic

import (
	"context"
	"fmt"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	customerExperienceAPI "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UpdateCustomerDataRequest ...
type UpdateCustomerDataRequest struct {
	CustomerId         string         `json:"customerId"`
	IsSendNotification bool           `json:"isSendNotification"`
	ReasonCode         string         `json:"reasonCode"`
	UpdateFormFields   []string       `json:"updateFormFields"`
	Keys               []string       `json:"key"`
	UpdateValues       map[string]any `json:"updatedValue"`
}

type UpdateCustomerDataConfig struct {
	CustomerID   string
	FormKey      string
	Fields       []interface{}
	UpdateReason string
	UpdatedBy    string
	ApprovedBy   string
}

type UpdateCustomerDataPhoneNumber struct {
	CustomerID     string
	NewPhoneNumber string
	UpdatedBy      string
	ApprovedBy     string
	UpdateReason   string
}

// UpdateCustomerDataResponse ...
type UpdateCustomerDataResponse struct {
	AccountID     string `json:"accountID,omitempty"`
	Status        string `json:"status,omitempty"`
	FailureReason string `json:"failureReason,omitempty"`
	QueueFeedback
}

// UpdateCustomerDataNotificationParams ...
type UpdateCustomerDataNotificationParams struct {
	UserName       string `json:"user_name"`
	AccountWording string `json:"account_wording"`
	AccountName    string `json:"account_name"`
	AccountIDs     string `json:"account_ids"`
	MessageDate    string `json:"message_date"`
}

type executeUpdateCustomerDataPhoneNumberOperation struct {
	clientFunc   func(context.Context, *customerExperienceAPI.OpsUpdateCustomerPhoneNumberRequest) (*customerExperienceAPI.OpsUpdateCustomerPhoneNumberResponse, error)
	successTitle string
	successMsg   string
}

type executeUpdateCustomerDataConfigOperation struct {
	clientFunc   func(context.Context, *customerExperienceAPI.OpsUpdateCustomerDataConfigReq) (*customerExperienceAPI.OpsUpdateCustomerDataConfigRes, error)
	successTitle string
	successMsg   string
}

// Core operation handler
func (p *process) handleUpdateCustomerData(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	operation func() error,
	successTitle, successMsg string,
) error {
	log := updateCustomerDataLogTag

	// Execute the operation
	if err := operation(); err != nil {
		// nolint:errcheck,gosec
		p.HandleAuditTrail(ctx, ticketDTO.ID,
			successTitle+" failed",
			fmt.Sprintf("Failed to %s: %s", successMsg, err.Error()))

		slog.FromContext(ctx).Error(log,
			fmt.Sprintf("error %s: %v", successMsg, err))
		return err
	}

	// Update ticket status
	if err := p.SystemProceedTicketStatus(ctx, ticketDTO.ID); err != nil {
		slog.FromContext(ctx).Error(log,
			"failed to proceed ticket status", slog.Error(err))
	}

	// Record successful operation
	// nolint:errcheck,gosec
	p.HandleAuditTrail(ctx, ticketDTO.ID,
		successTitle+" success",
		successMsg+" successfully")

	return nil
}

func (p *process) executeUpdateDataCustomerPhoneNumber(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	req *customerExperienceAPI.OpsUpdateCustomerPhoneNumberRequest,
	op executeUpdateCustomerDataPhoneNumberOperation,
) error {
	if err := utils.LoadStruct(ticketDTO.Data.Payload, req); err != nil {
		return fmt.Errorf("failed to load payload: %v", err)
	}

	return p.handleUpdateCustomerData(ctx, ticketDTO,
		func() error {
			_, err := op.clientFunc(ctx, req)
			return err
		},
		op.successTitle,
		op.successMsg)
}

func (p *process) executeUpdateDataCustomerConfig(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	req *customerExperienceAPI.OpsUpdateCustomerDataConfigReq,
	op executeUpdateCustomerDataConfigOperation,
) error {
	if err := utils.LoadStruct(ticketDTO.Data.Payload, req); err != nil {
		return fmt.Errorf("failed to load payload: %v", err)
	}

	return p.handleUpdateCustomerData(ctx, ticketDTO,
		func() error {
			_, err := op.clientFunc(ctx, req)
			return err
		},
		op.successTitle,
		op.successMsg)
}

// Execute Update Customer Data
func (p *process) ExecuteUpdateCustomerData(ctx context.Context, ticketDTO *storage.TicketDTO, key string) error {
	payloadMap, ok := ticketDTO.Data.Payload.(UpdateCustomerDataRequest)
	if !ok {
		return fmt.Errorf("expected Payload to be UpdateCustomerDataRequest{}, got %T", ticketDTO.Data.Payload)
	}

	switch key {
	case "phoneNumber":
		req := &customerExperienceAPI.OpsUpdateCustomerPhoneNumberRequest{}

		req.NewPhoneNumber = payloadMap.UpdateValues["phone"].(string)
		req.CustomerID = payloadMap.CustomerId
		req.UpdatedBy = ticketDTO.AssigneeUserName.String
		req.UpdateReason = payloadMap.ReasonCode

		return p.executeUpdateDataCustomerPhoneNumber(ctx, ticketDTO, req, executeUpdateCustomerDataPhoneNumberOperation{
			clientFunc: func(ctx context.Context, r *customerExperienceAPI.OpsUpdateCustomerPhoneNumberRequest) (*customerExperienceAPI.OpsUpdateCustomerPhoneNumberResponse, error) {
				customerReq := &customerExperienceAPI.OpsUpdateCustomerPhoneNumberRequest{}
				withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, req.CustomerID)

				return p.CustomerExperienceClient.OpsUpdateCustomerPhoneNumber(withHTTPHeader, customerReq)
			},
			successTitle: "Execute File",
			successMsg:   "execute file",
		})
	case "personalInfoOps.updateDataOps",
		"financialInfoOps.updateDataOps",
		"registeredAddressOps.updateDataOps",
		"domicileAddressOps.updateDataOps",
		"mailingAddressOps.updateDataOps":
		req := &customerExperienceAPI.OpsUpdateCustomerDataConfigReq{}

		req.UpdatedBy = ticketDTO.AssigneeUserName.String
		req.UpdateReason = payloadMap.ReasonCode
		req.FormKey = key
		req.Fields = payloadMap.UpdateValues[key].([]customerExperienceAPI.FieldRequest)

		return p.executeUpdateDataCustomerConfig(ctx, ticketDTO, req, executeUpdateCustomerDataConfigOperation{
			clientFunc: func(ctx context.Context, r *customerExperienceAPI.OpsUpdateCustomerDataConfigReq) (*customerExperienceAPI.OpsUpdateCustomerDataConfigRes, error) {
				customerReq := &customerExperienceAPI.OpsUpdateCustomerDataConfigReq{}
				withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, payloadMap.CustomerId)

				return p.CustomerExperienceClient.OpsUpdateCustomerDataConfiguration(withHTTPHeader, customerReq)
			},
			successTitle: "Execute File",
			successMsg:   "execute file",
		})
	default:
		return fmt.Errorf("unsupported key: %s", key)
	}
}
