package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

func (p *process) UpdateDataSegregation(ctx context.Context, req *api.UpdateDataSegregationRequest) (*api.UpdateDataSegregationResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.DataSegregation, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return &api.UpdateDataSegregationResponse{}, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return &api.UpdateDataSegregationResponse{}, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return &api.UpdateDataSegregationResponse{}, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return &api.UpdateDataSegregationResponse{}, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return &api.UpdateDataSegregationResponse{}, err
	}

	role, err := permissionManagementStorage.GetRoleByID(ctx, db, req.RoleID)
	if err != nil {
		return &api.UpdateDataSegregationResponse{}, err
	}

	if req.IsCheckParent {
		err = p.UpdateDataSegregationCheckParent(ctx, db, user, req, role)
	} else {
		err = p.UpdateDataSegregationDefault(ctx, db, user, req, role)
	}
	if err != nil {
		return &api.UpdateDataSegregationResponse{}, err
	}

	return &api.UpdateDataSegregationResponse{
		Status: "SUCCESS",
	}, nil
}

func (p *process) UpdateDataSegregationCheckParent(ctx context.Context, db *sql.DB, user *permissionManagementStorage.UserDTO, req *api.UpdateDataSegregationRequest, role *permissionManagementStorage.RoleDTO) error {
	for _, v := range req.SegregationIDs {
		segregations, err := storage.CheckEligibilitySegregationWithParent(ctx, db, req.Status == 1, v, req.RoleID)
		if err != nil {
			slog.FromContext(ctx).Error(constants.UpdateDataSegregationLogTag, "error fetching eligibility segregation from DB", slog.Error(err))
			return errorwrapper.Error(apiError.InternalServerError, "failed to check eligibility before update data segregation")
		}

		err = p.updateDataSegregationToDB(ctx, db, segregations, role, user, req.Status, req.IsCheckParent)
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *process) UpdateDataSegregationDefault(ctx context.Context, db *sql.DB, user *permissionManagementStorage.UserDTO, req *api.UpdateDataSegregationRequest, role *permissionManagementStorage.RoleDTO) error {
	segregations, err := storage.CheckEligibilityUpdateSegregation(ctx, db, req.RoleID, req.SegregationIDs, req.Status == 1)
	if err != nil {
		slog.FromContext(ctx).Error(constants.UpdateDataSegregationLogTag, "error fetching eligibility segregation from DB", slog.Error(err))
		return errorwrapper.Error(apiError.InternalServerError, "failed to check eligibility before update data segregation")
	}

	err = p.updateDataSegregationToDB(ctx, db, segregations, role, user, req.Status, req.IsCheckParent)
	if err != nil {
		return err
	}

	return nil
}

func (p *process) updateDataSegregationToDB(ctx context.Context, db *sql.DB, data []*storage.DataSegregationEligibility, role *permissionManagementStorage.RoleDTO, user *permissionManagementStorage.UserDTO, status int64, isCheckParent bool) error {
	isActive := status == 1

	if len(data) == 0 {
		return nil
	}

	for _, seg := range data {
		if seg.EligibleUpdate {
			var err error
			if isActive {
				_, err = storage.InsertRolesSegregations(ctx, db, &storage.RoleSegregationDTO{
					RoleID:        role.ID,
					SegregationID: seg.ID,
				})
			} else {
				err = storage.DeleteRoleSegregation(ctx, db, &storage.RoleSegregationDTO{
					RoleID:        role.ID,
					SegregationID: seg.ID,
				})
			}

			if err != nil {
				slog.FromContext(ctx).Error(constants.UpdateDataSegregationLogTag, "error update roles segregation", slog.Error(err))
				return errorwrapper.Error(apiError.InternalServerError, "error update roles segregation "+err.Error())
			}
			p.setAuditHistoryDataSegregation(ctx, role, seg, user, constants.AuditStatusWordingMap[int(status)])
		} else {
			if !isActive && isCheckParent {
				return nil
			}
		}
	}

	return nil
}

func (p *process) setAuditHistoryDataSegregation(ctx context.Context, role *permissionManagementStorage.RoleDTO, data *storage.DataSegregationEligibility, user *permissionManagementStorage.UserDTO, action string) {
	// write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     strconv.FormatInt(role.ID, 10),
		IdentifierType: data.Name,
		ActivityType:   constants.DataSegregationConfig,
		Title:          "Update Data Segregation",
		Description:    fmt.Sprintf("%s for role %v", action, role.Name),
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.UpdateDataSegregationLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}
