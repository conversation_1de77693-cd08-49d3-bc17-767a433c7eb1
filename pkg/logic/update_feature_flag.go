package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// UpdateFeatureFlag for updating feature flag
// nolint:dupl,funlen
func (p *process) UpdateFeatureFlag(ctx context.Context, req *api.UpdateFeatureFlagRequest) (*api.UpdateFeatureFlagResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.FeatureFlag, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get data from DB to detect change
	dataDB, err := storage.GetFeatureFlagByName(ctx, db, []string{req.Name})
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get feature flag from database")
	}
	if dataDB == nil || len(dataDB) == 0 {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, "feature flag not found in database")
	}

	dbFlag := dataDB[0]
	var remarkParts []string

	// Check for description change
	if strings.TrimSpace(req.Description) != strings.TrimSpace(dbFlag.Description) {
		remarkParts = append(remarkParts, "Change Description")
	}

	// Check for Value change
	if req.Value != dbFlag.Value {
		switch req.Value {
		case constants.FeatureFlagActive:
			remarkParts = append(remarkParts, "Activate Status")
		case constants.FeatureFlagInactive:
			remarkParts = append(remarkParts, "Inactivate Status")
		}
	}

	// Combine into final remark
	remark := "Edit - "
	// write audit trail only if have change
	if len(remarkParts) > 0 {
		remark += strings.Join(remarkParts, ", ")

		auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
			CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
			CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
			Identifier:     "",
			IdentifierType: req.Name,
			ActivityType:   constants.FeatureFlagConfig,
			Title:          "",
			Description:    remark,
		}
		_, err = auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
		if err != nil {
			slog.FromContext(ctx).Error(constants.CreateFeatureFlagLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, err))
		}
	}

	// update feature flag
	featureFlagDTO, err := updateFeatureFlag(ctx, req, db, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update feature flag")
	}

	// set feature flag to redis
	redisObject := make(map[string]interface{})
	redisObject[req.Name] = featureFlagDTO
	err = redis.SetHashRedisValue(ctx, redisObject, constants.FeatureFlagHashRedisKey, constants.UpdateFeatureFlagLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update into redis")
	}

	return &api.UpdateFeatureFlagResponse{
		Status: "success",
	}, nil
}

func updateFeatureFlag(ctx context.Context, req *api.UpdateFeatureFlagRequest, db *sql.DB, ID int64) (*storage.FeatureFlagDTO, error) {
	featureFlagDTO := &storage.FeatureFlagDTO{
		Name:        req.Name,
		Value:       req.Value,
		Description: req.Description,
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: ID, Valid: true},
	}

	// update feature flag
	err := storage.UpdateFeatureFlag(ctx, db, featureFlagDTO)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update feature flag")
	}

	return &storage.FeatureFlagDTO{
		Name:        req.Name,
		Value:       req.Value,
		Description: req.Description,
	}, nil
}
