package logic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// UpdateModule updates a module
func (p *process) UpdateModule(ctx context.Context, req *api.UpdateModuleRequest) (*api.UpdateModuleResponse, error) {
	moduleID, err := p.processModuleUpdate(ctx, req)
	if err != nil {
		return nil, err
	}

	return &api.UpdateModuleResponse{
		Id: moduleID,
	}, nil
}

func (p *process) processModuleUpdate(ctx context.Context, req *api.UpdateModuleRequest) (int64, error) {
	// Check permission
	user, err := p.checkModuleUpdatePermission(ctx)
	if err != nil {
		return 0, err
	}

	// Get database handle and validate
	db, err := p.getAndValidateModuleUpdate(ctx, req)
	if err != nil {
		return 0, err
	}

	// Update module
	err = p.updateModuleInDB(ctx, db, req, user)
	if err != nil {
		return 0, err
	}

	// Write audit trail
	p.writeModuleUpdateAuditTrail(ctx, req.Id, req.Name, user.ID)

	return req.Id, nil
}

func (p *process) checkModuleUpdatePermission(ctx context.Context) (*permissionManagementStorage.UserDTO, error) {
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this action")
	}
	return user, nil
}

func (p *process) getAndValidateModuleUpdate(ctx context.Context, req *api.UpdateModuleRequest) (*sql.DB, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Check if module name already exists (excluding current module)
	existingModule, err := storage.GetModuleByNameExcludeID(ctx, db, req.Name, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to check module name")
	}
	if existingModule != nil {
		return nil, errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("module with name '%s' already exists", req.Name))
	}

	return db, nil
}

func (p *process) updateModuleInDB(ctx context.Context, db *sql.DB, req *api.UpdateModuleRequest, user *permissionManagementStorage.UserDTO) error {
	moduleDTO := &storage.ModuleDTO{
		ID:   req.Id,
		Name: req.Name,
		UpdatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		Status: req.Status,
	}

	return storage.UpdateModule(ctx, db, moduleDTO)
}

func (p *process) writeModuleUpdateAuditTrail(ctx context.Context, moduleID int64, name string, userID int64) {
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: userID, Valid: true},
		Identifier:     fmt.Sprint(moduleID),
		IdentifierType: constants.ModuleID,
		ActivityType:   string(constants.ModuleConfig),
		Title:          fmt.Sprintf("Module %s successfully updated", name),
		Description:    fmt.Sprintf("Module %s successfully updated", name),
	}

	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(constants.UpdateModuleLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}
}
