package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// UpdateTicketAssignee updates a ticket without capturing the ticket or creating a new ticket history
//
// nolint: funlen, dupl, errcheck
func (p *process) UpdateTicketAssignee(ctx context.Context, req *api.UpdateTicketAssigneeRequest) (*api.UpdateTicketAssigneeResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, err
	}

	// get ticket data
	ticketDTO, err := storage.GetTicketByID(ctx, master, req.Id)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket by id: %v", err))
	}

	requestorUser, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// if target user id is empty then clear the assignee
	if req.TargetUserID == "" {
		if requestorUser.ID != ticketDTO.AssigneeUserID.Int64 {
			return nil, errorwrapper.Error(apiError.Forbidden, "user is not authorized to deassign other user ticket")
		}
		return &api.UpdateTicketAssigneeResponse{Id: req.Id}, p.deassignTicket(ctx, req, requestorUser)
	}

	// get target user id
	user, err := permissionManagementStorage.GetUserByUserID(ctx, master, req.TargetUserID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user by id")
	}

	// check whether assignee is able to take action on current status
	bitwiseValue, err := permissionManagementStorage.GetUserBitwiseValueForElementID(ctx, master, user.ID, ticketDTO.ElementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to get user bitwise value for element id")
	}

	// get ticket chain to determine possible action id
	chainMap, err := storage.GetTicketChainByElementIDMap(ctx, master, ticketDTO.ElementID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket chain by element id: %v", err))
	}

	finalMap := make(map[string]int64)
	for _, chain := range chainMap {
		if bitwiseValue&chain.BitwiseRequired == chain.BitwiseRequired {
			finalMap[chain.ActionName] = chain.NextStatusID
		}
	}

	// if user has no action to current status then can't assign the ticket
	if len(finalMap) == 0 {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, "user does not have action to current status")
	}

	// update the ticket assignee
	err = storage.UpdateTicketAssignee(ctx, master, req.Id, user.ID)
	if err != nil {
		return nil, err
	}

	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":     req.Id,
		"element_id":    ticketDTO.ElementID,
		"priority_id":   ticketDTO.PriorityID,
		"status_id":     ticketDTO.TicketStatusID,
		"source":        ticketDTO.Source,
		"deadline_time": ticketDTO.DeadlineTime.Time,
		"assignee_id":   user.ID,
		"data":          ticketDTO.Data,
		"event_type":    constants.TaskAssigned,
		"assignee_name": user.Name,
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: requestorUser.ID, Valid: true},
		Identifier:     strconv.Itoa(int(req.Id)),
		IdentifierType: constants.TicketID,
		Title:          "Assign Ticket",
		Description:    fmt.Sprintf("Ticket assigned to user %s", user.Name),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return &api.UpdateTicketAssigneeResponse{
		Id: req.Id,
	}, nil
}

// deassignTicket deassigns a ticket
func (p *process) deassignTicket(ctx context.Context, req *api.UpdateTicketAssigneeRequest, user *permissionManagementStorage.UserDTO) error {
	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	err = storage.UpdateTicketAssignee(ctx, db, req.Id, 0)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.Idem, "failed to update ticket assignee")
	}

	// Get ticket data for snapshot
	ticketDTO, err := storage.GetTicketByID(ctx, db, req.Id)
	if err != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error getting ticket data for audit trail: %v", err))
		// Continue with audit trail creation even if we can't get ticket data
	}

	// Create data snapshot for audit trail
	dataSnapshot := map[string]interface{}{
		"ticket_id":       req.Id,
		"event_type":      constants.TaskDeassigned,
		"deassigned_from": user.Name,
		"deassigned_by":   user.ID,
	}

	// Add ticket data to snapshot if available
	if ticketDTO != nil {
		dataSnapshot["element_id"] = ticketDTO.ElementID
		dataSnapshot["priority_id"] = ticketDTO.PriorityID
		dataSnapshot["status_id"] = ticketDTO.TicketStatusID
		dataSnapshot["source"] = ticketDTO.Source
		dataSnapshot["deadline_time"] = ticketDTO.DeadlineTime.Time
		dataSnapshot["data"] = ticketDTO.Data
	}

	// Write audit trail
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: user.ID, Valid: true},
		Identifier:     strconv.Itoa(int(req.Id)),
		IdentifierType: constants.TicketID,
		Title:          "Deassign Ticket",
		Description:    fmt.Sprintf("Ticket deassigned from user %s", user.Name),
		ActivityType:   constants.Ticket,
		ExtraParams:    dataSnapshot,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(createTicketLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return nil
}
