package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// UpdateTicketStatus Updates a ticket status and notes without changing the data
//
// nolint: funlen, dupl, errcheck
func (p *process) UpdateTicketStatus(ctx context.Context, req *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, err
	}

	// get ticket by id
	ticketDTO, err := storage.GetTicketByID(ctx, master, req.Id)
	if err != nil {
		return nil, err
	}
	prevStatusID := ticketDTO.TicketStatusID

	// Authenticate the request
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, err
	}
	bitwiseValue, err := permissionManagementStorage.GetUserBitwiseValueForElementID(ctx, master, user.ID, ticketDTO.ElementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get user bitwise value for element id")
	}

	// for ticket advancement only will check ticket chain
	chainBitwiseRequired, err := storage.GetTicketChainBitwiseRequired(ctx, master, req.Id, req.NextStatusID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain bitwise required")
	} else if bitwiseValue&chainBitwiseRequired == chainBitwiseRequired {
		return nil, errorwrapper.Error(apiError.Forbidden, "user is not authorized to update ticket")
	}

	dto := makeUpdateTicketStatusDTO(ticketDTO, req, user.ID)

	isTicketClosed := false
	// compute ticket close datetime if ticket is rejected or completed or cancelled
	if dto.TicketStatusID == constants.TicketStatusCompleted || dto.TicketStatusID == constants.TicketStatusCancelled || dto.TicketStatusID == constants.TicketStatusRejected {
		dto.TicketCloseDatetime = sql.NullTime{Time: time.Now(), Valid: true}
		isTicketClosed = true
	}

	// Handle hold/resume actions
	var auditDescription string
	var auditEventType string

	if req.Action == constants.ActionHoldTicket {
		// Validate reason is provided
		if req.HoldReason == "" {
			return nil, errorwrapper.Error(apiError.BadRequest, "Hold reason is required")
		}

		// Validate remarks when reason is "Others"
		if req.HoldReason == constants.HoldReasonOthers && req.HoldRemarks == "" {
			return nil, errorwrapper.Error(apiError.BadRequest, "Remarks are required when reason is 'Others'")
		}

		auditDescription = fmt.Sprintf("Case placed on hold with no auto-resume time. Reason: %s", req.HoldReason)
		if req.HoldRemarks != "" {
			auditDescription += fmt.Sprintf(" %s", req.HoldRemarks)
		}
		auditEventType = constants.CaseHeld

	} else if req.Action == constants.ActionResumeTicket {
		auditDescription = "Case resumed"
		auditEventType = constants.CaseResumed
	}

	// Begin transaction
	tx, err := master.Begin()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	// Handle hold/resume specific database operations
	if req.Action == constants.ActionHoldTicket {
		err = holdTicketAction(ctx, tx, ticketDTO, req, user)
		if err != nil {
			return nil, err
		}
	} else if req.Action == constants.ActionResumeTicket {
		err = resumeTicketAction(ctx, tx, ticketDTO, req, user)
		if err != nil {
			return nil, err
		}
	}

	// update the ticket
	err = storage.UpdateTicketStatus(ctx, tx, ticketDTO.ID, req.NextStatusID, user.ID, dto.TicketCloseDatetime)
	if err != nil {
		return nil, err
	}

	// Insert the ticket history
	historyID, err := storage.CreateTicketHistory(ctx, tx, &storage.TicketHistoryDTO{
		TicketID:     ticketDTO.ID,
		Data:         ticketDTO.Data,
		ActionName:   req.Action,
		Note:         sql.NullString{String: req.Note, Valid: true},
		PrevStatusID: prevStatusID,
		NextStatusID: req.NextStatusID,
		CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:    sql.NullInt64{Int64: user.ID, Valid: true},
	})
	if err != nil {
		return nil, err
	}

	// commit the transaction
	err = tx.Commit()
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("failed to commit transaction: %v", err))
	}

	title := "Ticket status updated"
	description := fmt.Sprintf("User %s updated the ticket status with id %d", user.Name, dto.ID)

	// Determine event type based on status change
	eventType := constants.CaseStatusUpdated
	if req.Action == constants.ActionHoldTicket {
		title = "Case Held"
		description = auditDescription
		eventType = auditEventType
	} else if req.Action == constants.ActionResumeTicket {
		title = "Case Resumed"
		description = auditDescription
		eventType = auditEventType
	} else if req.NextStatusID == constants.TicketStatusCompleted {
		eventType = constants.CaseCompleted
	} else if req.NextStatusID == constants.TicketStatusRejected {
		eventType = constants.CaseRejected
	} else if req.NextStatusID == constants.TicketStatusCancelled {
		eventType = constants.CaseCancelled
		title = "Case Cancelled"
		description = fmt.Sprintf("Cased cancelled. Reason:%s", req.Note)
	}

	WriteAuditLogForTicket(ctx, master, dto, user.ID, dto.ID, strconv.Itoa(int(historyID)), title, description, eventType)
	if isTicketClosed && dto.ParentTicketID.Valid {
		WriteAuditLogForTicket(ctx, master, nil, user.ID, dto.ParentTicketID.Int64, "", "Child case closed", fmt.Sprintf("Case %d closed", dto.ID), "")
	}

	// check whether to trigger system execution
	err = p.CheckTicketExecution(ctx, dto)
	if err != nil {
		slog.FromContext(ctx).Error(updateTicketLogTag, fmt.Sprintf("error checking ticket execution: %v", err))
	}

	return &api.UpdateTicketResponse{
		Id: dto.ID,
	}, nil
}

func holdTicketAction(ctx context.Context, tx *sql.Tx, ticketDTO *storage.TicketDTO, req *api.UpdateTicketRequest, user *permissionManagementStorage.UserDTO) error {
	// Store original deadline if not already stored
	if !ticketDTO.OriginalDeadlineTime.Valid {
		err := storage.UpdateTicketOriginalDeadline(ctx, tx, req.Id)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, "failed to store original deadline")
		}
	}

	// move this to storage func
	err := storage.UpdateTicketOnHoldFields(ctx, tx, req.Id, req.HoldReason, req.HoldRemarks, user.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, "failed to set hold fields")
	}

	return nil
}

func resumeTicketAction(ctx context.Context, tx *sql.Tx, ticketDTO *storage.TicketDTO, req *api.UpdateTicketRequest, user *permissionManagementStorage.UserDTO) error {
	// Calculate hold duration and update deadline
	if ticketDTO.OnHoldAt.Valid {
		resumeTime := time.Now()
		holdDuration := resumeTime.Sub(ticketDTO.OnHoldAt.Time)

		// Recalculate deadline by adding hold duration to original deadline
		var newDeadline time.Time
		if ticketDTO.OriginalDeadlineTime.Valid {
			newDeadline = ticketDTO.OriginalDeadlineTime.Time.Add(holdDuration)
		} else {
			newDeadline = ticketDTO.DeadlineTime.Time.Add(holdDuration)
		}

		// Update ticket with resume information
		err := storage.UpdateOnTicketResume(ctx, tx, req.Id, resumeTime, newDeadline, int64(holdDuration.Seconds()), user.ID)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, "failed to resume ticket")
		}
	}
	
	return nil
}

func makeUpdateTicketStatusDTO(current *storage.TicketDTO, req *api.UpdateTicketRequest, userID int64) *storage.TicketDTO {
	var updated = *current

	updated.TicketStatusID = req.NextStatusID
	updated.UpdatedBy = sql.NullInt64{Int64: userID, Valid: true}
	updated.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}

	return &updated
}
