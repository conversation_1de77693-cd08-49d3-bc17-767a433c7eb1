package logic

import "gitlab.super-id.net/bersama/opsce/onedash-be/api"

// Group related types and constants
const (
	waiveOffLogTag = "stream.waiveOff"
)

// WaiveOffRequest represents the structure for waive-off file processing.
type WaiveOffRequest struct {
	TicketCreationType string
	ReasonCode         string
	OpsReasonCode      string
	SourceFile         api.FilePayload
	SuccessFile        api.FilePayload
	FailedFile         api.FilePayload
}
