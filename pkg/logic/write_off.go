package logic

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// Group related types and constants
const (
	writeOffLogTag = "stream.writeOff"
)

// WriteOffRequest represents the structure for write-off file processing.
type WriteOffRequest struct {
	TicketCreationType string          `json:"ticketCreationType"`
	ReasonCode         string          `json:"reasonCode"`
	SourceFile         api.FilePayload `json:"sourceFile"`
	SuccessFile        api.FilePayload `json:"successFile"`
	FailedFile         api.FilePayload `json:"failedFile"`
}

type ExecuteFileRequest struct {
	FileName string
	Type     string
}

type ExecuteFileResponse struct {
	Status  string
	Message string
}

type executeFileOperation struct {
	clientFunc   func(context.Context, *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error)
	successTitle string
	successMsg   string
}

// Core operation handler
func (p *process) handleExecuteFileOperation(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	operation func() error,
	successTitle, successMsg string,
	elementCode constants.ElementCodes,
) error {
	log := ""
	switch elementCode {
	case constants.WriteOff:
		log = writeOffLogTag
	case constants.WaiveOff:
		log = waiveOffLogTag
	default:
		return fmt.Errorf("unsupported element for this execute file operation: %s", ticketDTO.ElementName)
	}

	// Execute the operation
	if err := operation(); err != nil {
		// nolint:errcheck,gosec
		p.HandleAuditTrail(ctx, ticketDTO.ID,
			successTitle+" failed",
			fmt.Sprintf("Failed to %s: %s", successMsg, err.Error()))

		slog.FromContext(ctx).Error(log,
			fmt.Sprintf("error %s: %v", successMsg, err))
		return err
	}

	// Update ticket status
	if err := p.SystemProceedTicketStatus(ctx, ticketDTO.ID); err != nil {
		slog.FromContext(ctx).Error(log,
			"failed to proceed ticket status", slog.Error(err))
	}

	// nolint:errcheck,gosec
	// Record successful operation
	p.HandleAuditTrail(ctx, ticketDTO.ID,
		successTitle+" success",
		successMsg+" successfully")

	return nil
}

func (p *process) executeFile(
	ctx context.Context,
	ticketDTO *storage.TicketDTO,
	req *api.ExecuteFileRequest,
	op executeFileOperation,
	elementCode constants.ElementCodes,
) error {
	return p.handleExecuteFileOperation(ctx, ticketDTO,
		func() error {
			_, err := op.clientFunc(ctx, req)
			return err
		},
		op.successTitle,
		op.successMsg,
		elementCode)
}

// ExecuteFile ops
func (p *process) ExecuteFile(ctx context.Context, ticketDTO *storage.TicketDTO, elementCode constants.ElementCodes) error {
	req := &api.ExecuteFileRequest{}

	switch elementCode {
	case constants.WriteOff:
		var writeOffPayload WriteOffRequest
		payloadMap, ok := ticketDTO.Data.Payload.(map[string]interface{})
		if !ok {
			return fmt.Errorf("expected Payload to be map[string]interface{}, got %T", ticketDTO.Data.Payload)
		}

		if err := utils.LoadStruct(payloadMap, &writeOffPayload); err != nil {
			return fmt.Errorf("failed to decode payload into WriteOffRequest: %w", err)
		}
		req.FileName = writeOffPayload.SuccessFile.FileName
		req.Type = fileprocessor.WriteOffFileProcessorUsecaseV2
	case constants.WaiveOff:
		var waiveOffPayload WriteOffRequest
		payloadMap, ok := ticketDTO.Data.Payload.(map[string]interface{})
		if !ok {
			return fmt.Errorf("expected Payload to be map[string]interface{}, got %T", ticketDTO.Data.Payload)
		}

		if err := utils.LoadStruct(payloadMap, &waiveOffPayload); err != nil {
			return fmt.Errorf("failed to decode payload into WriteOffRequest: %w", err)
		}
		req.FileName = waiveOffPayload.SuccessFile.FileName
		req.Type = fileprocessor.WaiveOffFileProcessorUsecaseV2
	default:
		return fmt.Errorf("unsupported elementCode: %v", elementCode)
	}

	return p.executeFile(ctx, ticketDTO, req, executeFileOperation{
		clientFunc: func(ctx context.Context, r *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
			return p.OpsService.ExecuteFile(ctx, r)
		},
		successTitle: "Execute File",
		successMsg:   "execute file",
	}, elementCode)
}
