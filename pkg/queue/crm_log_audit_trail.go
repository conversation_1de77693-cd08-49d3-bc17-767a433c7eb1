package queue

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	logTag                  = "sqs"
	logAuditTrailsSqsLogTag = "sqs.LogAuditTrails"
	blockAccountLogTag      = "sqs.BlockAccount"
	unblockAccountLogTag    = "sqs.UnblockAccount"

	defaultVisibilityTimeout = 10 * time.Second
	all                      = "All"
	approximateReceiveCount  = "ApproximateReceiveCount"
	maxRetry                 = 5
)

// LogAuditTrailsRequest ...
type LogAuditTrailsRequest struct {
	RequestID   string
	RequestTime time.Time
	ServiceName string
	Data        interface{}
}

var (
	// AuditTrailRules ...
	AuditTrailRules map[string]interface{}
)

// QueueLogAuditTrailsMessage ...
//
// nolint: funlen
func (qs *QueueService) QueueLogAuditTrailsMessage(ctx context.Context, req sqs.HandlerRequest) (sqs.HandlerResponse, error) {
	msg := req.Message

	slog.FromContext(ctx).Info(logAuditTrailsSqsLogTag,
		fmt.Sprintf("receive log audit trail message with id: %v", *msg.MessageId))

	message := LogAuditTrailsRequest{}
	AuditTrailRules = qs.appConfig.LogAuditTrailsRules

	if err := json.Unmarshal([]byte(*msg.Body), &message); err != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag,
			fmt.Sprintf("failed to unmarshal message with id: %v", *msg.MessageId), slog.Error(err))
		return sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         msg,
		}, nil
	}

	messageRequest := message.Data.(map[string]interface{})
	jsonString, err := json.Marshal(messageRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag,
			fmt.Sprintf("failed to marshal message with id: %v and request id: %v",
				*msg.MessageId, message.RequestID), slog.Error(err))
		return sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         msg,
		}, nil
	}

	request := api.LogAuditTrailRequest{}
	err = json.Unmarshal(jsonString, &request)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag,
			fmt.Sprintf("failed to unmarshal message with id: %v and request id: %v",
				*msg.MessageId, message.RequestID), slog.Error(err))
		return sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         msg,
		}, nil
	}
	eventRule := getRuleByService(ctx, request, message.ServiceName, qs.appConfig)
	slog.FromContext(ctx).Info(logAuditTrailsSqsLogTag,
		fmt.Sprintf("start proceed log audit trail request ID: %v", message.RequestID))
	err = eventRule.apply()
	if err != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag, fmt.Sprintf("error when execute rule for request id: %v", message.RequestID), slog.Error(err))
		return sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         msg,
		}, nil
	}

	slog.FromContext(ctx).Info(logAuditTrailsSqsLogTag,
		fmt.Sprintf("complete proceed log audit trail request ID: %v", message.RequestID))

	return sqs.HandlerResponse{
		DeleteFromQueue: true,
		Message:         msg,
	}, nil
}

func getRuleByService(ctx context.Context, req api.LogAuditTrailRequest, serviceName string, cfg *config.AppConfig) rules {
	switch serviceName {
	case "CRM":
		return &CRMRules{
			ctx:         ctx,
			req:         req,
			serviceName: serviceName,
			appConfig:   cfg,
		}
	default:
		return &defaultRules{
			ctx:         ctx,
			req:         req,
			serviceName: serviceName,
			appConfig:   cfg,
		}
	}
}

// rules ...
type rules interface {
	apply() error
}

// CRMRules ...
type CRMRules struct {
	ctx         context.Context
	req         api.LogAuditTrailRequest
	serviceName string
	appConfig   *config.AppConfig
}

// defaultRules ...
type defaultRules struct {
	ctx         context.Context
	req         api.LogAuditTrailRequest
	serviceName string
	appConfig   *config.AppConfig
}

// apply ...
func (r *defaultRules) apply() error {
	return saving(r.ctx, r.appConfig, r.req)
}

// apply ...
func (cr *CRMRules) apply() error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(cr.ctx, cr.appConfig.Data.MySQL.Master)
	if err != nil {
		return err
	}

	result, err := storage.GetOneAuditTrailForRuleChecking(cr.ctx, db,
		cr.req.UserID,
		cr.req.RelatedID,
		cr.serviceName,
		cr.req.Event,
		cr.req.SafeID)

	if err != nil && errors.Is(err, sql.ErrNoRows) {
		return saving(cr.ctx, cr.appConfig, cr.req)
	} else if err != nil {
		slog.FromContext(cr.ctx).Error(logAuditTrailsSqsLogTag, "error query to db", slog.Error(err))
		return err
	}
	timeRange := time.Duration(5) * time.Minute
	if logRules, ok := AuditTrailRules["CRMRule"]; ok {
		mapRule := logRules.(map[string]interface{})
		if configTime, hasTimeRange := mapRule["timeRange"]; hasTimeRange {
			timeRange = time.Duration(configTime.(float64)) * time.Minute
		}
	}
	if result != nil {
		log := result
		createdAt := log.CreatedAt.Local()
		isWithin := time.Since(createdAt) <= timeRange
		if isWithin {
			slog.FromContext(cr.ctx).Info(logAuditTrailsSqsLogTag, "skip to save audit trail")
			return nil
		}
	}

	return saving(cr.ctx, cr.appConfig, cr.req)
}

// saving ...
func saving(ctx context.Context, appConfig *config.AppConfig, req api.LogAuditTrailRequest) error {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, appConfig.Data.MySQL.Master)
	if err != nil {
		return err
	}

	rawMetadata := json.RawMessage("{}")
	rawData, errMarshal := json.Marshal(req.Metadata)
	if errMarshal != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag,
			fmt.Sprintf("error occured marshal metadata: %v, error: %v, set to default value {}",
				req.Metadata, errMarshal),
			slog.Error(err))
	}

	if req.Metadata != nil && errMarshal == nil {
		rawMetadata = rawData
	}

	_, err = storage.CreateAuditLog(ctx, db, &storage.LogAuditTrail{
		Name:      req.Name,
		UserID:    req.UserID,
		Email:     req.Email,
		Event:     req.Event,
		Action:    req.Action,
		Metadata:  rawMetadata,
		RelatedID: req.RelatedID,
		Service:   req.ServiceName,
		CreatedAt: time.Now(),
		SafeID:    req.SafeID,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(logAuditTrailsSqsLogTag, fmt.Sprintf("error when save audit trail: %v", err), slog.Error(err))
		return err
	}
	return nil
}
