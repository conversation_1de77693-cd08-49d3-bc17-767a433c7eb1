// Package queue ...
//
// nolint: golint, gocyclo, funlen, dupl
package queue

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// OpsBlockAccountQueueHandler is to handle block account requests from SQS queue.
func (qs *QueueService) OpsBlockAccountQueueHandler(ctx context.Context, payload sqs.HandlerRequest) (sqs.HandlerResponse, error) {
	var (
		defaultHandlerResponse = sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         payload.Message,
			Success:         false,
		}
	)
	slog.FromContext(ctx).Info(blockAccountLogTag, fmt.Sprintln("received block account request", *payload.Message.MessageId, *payload.Message.Body))

	req, err := unmarshallBlockAccountRequest(payload)
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "failed to unmarshal block account request", slog.Error(err))
		return defaultHandlerResponse, nil
	}

	// if requeued more than 5 times, delete from queue. Should be made configurable later
	if retryCount, _ := strconv.Atoi(aws.StringValue(payload.Message.Attributes[approximateReceiveCount])); retryCount >= 5 {
		slog.FromContext(ctx).Warn(blockAccountLogTag, fmt.Sprintf("message requeued more than 5 times, delete from queue, req: %v", req))
		// audit trails for failed block account
		auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
			CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
			CreatedBy:      sql.NullInt64{Int64: 1, Valid: true}, // FIXME
			Identifier:     req.TicketID,
			IdentifierType: constants.TicketID,
			Title:          "Block Account failed",
			Description:    fmt.Sprintf("Failed to block account %s", req.AccountID),
			ActivityType:   constants.Ticket,
		}
		_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
		if auditErr != nil {
			slog.FromContext(ctx).Error(blockAccountLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
		}
		return defaultHandlerResponse, nil
	}

	ticketID, err := strconv.Atoi(req.TicketID)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "failed to convert ticket id to int", slog.Error(err))
		return defaultHandlerResponse, nil
	}

	// process the message
	res, err := logic.Process.BlockAccount(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "failed to process message", slog.Error(err))
		if res != nil {
			defaultHandlerResponse.DeleteFromQueue = !res.QueueFeedback.NeedRequeue
		}
		return defaultHandlerResponse, nil
	}

	// proceed ticket status
	err = logic.Process.SystemProceedTicketStatus(ctx, int64(ticketID))
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "failed to proceed ticket status", slog.Error(err))
	}

	// write audit trail for blocking account result
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 1, Valid: true}, // FIXME
		Identifier:     req.TicketID,
		IdentifierType: constants.TicketID,
		Title:          "Block Account Success",
		Description:    fmt.Sprintf("Account %s has been blocked", req.AccountID),
		ActivityType:   constants.Ticket,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return sqs.HandlerResponse{
		DeleteFromQueue: true,
		Message:         payload.Message,
		Success:         true,
	}, nil
}

func unmarshallBlockAccountRequest(payload sqs.HandlerRequest) (*logic.BlockAccountRequest, error) {
	var queueReq logic.QueueRequest
	if err := json.Unmarshal([]byte(*payload.Message.Body), &queueReq); err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to unmarshal payload from sqs")
	}

	// Use utils.LoadStruct to convert the map[string]interface{} to BlockAccountRequest
	var req logic.BlockAccountRequest
	if err := utils.LoadStruct(queueReq.Payload, &req); err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to cast payload to BlockAccountRequest")
	}

	req.TicketID = queueReq.TicketID
	return &req, nil
}
