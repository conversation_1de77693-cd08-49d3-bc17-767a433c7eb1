package queue

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// OpsDeactivateLOCQueueHandler is to handle queue for deactivate LOC
//
//nolint:funlen
func (qs *QueueService) OpsDeactivateLOCQueueHandler(ctx context.Context, message sqs.HandlerRequest) (sqs.HandlerResponse, error) {
	slog.FromContext(ctx).Info(constants.DeactivateLOCLogTag, fmt.Sprintf("start processing with message id: %v", message.Message.MessageId), utils.GetTraceID(ctx))

	var (
		defaultHandlerResponse = sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         message.Message,
			Success:         false,
		}
	)

	req := &api.DeactivateLOCRequest{}
	if err := json.Unmarshal([]byte(*message.Message.Body), req); err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("failed to marshal sqs message: %v", err.Error()), utils.GetTraceID(ctx))
		return defaultHandlerResponse, nil
	}

	res, err := logic.Process.DeactivateLOC(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("failed to process message: %v", err.Error()), utils.GetTraceID(ctx))
		//TODO AUDIT
		return defaultHandlerResponse, nil
	}

	if res.Status == constants.LOCDeactivationFailed {
		// write audit trail
		auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
			Identifier:     req.TicketID,
			IdentifierType: constants.TicketID,
			CreatedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			},
			CreatedBy: sql.NullInt64{
				Int64: 1, // FIXME: Hardcoded
				Valid: true,
			},
			Title:        "Deactivate LOC failed",
			Description:  fmt.Sprintf("Account %v failed to deactivate because %v", res.LoanAccountID, res.StatusDescription),
			ActivityType: constants.Ticket,
			ReferenceID:  res.IdempotencyKey,
		}
		_, err = auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
		if err != nil {
			slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailsDTO, err.Error()))
		}
		_ = updateQueueToCompleted(ctx, req)
		return defaultHandlerResponse, nil
	}

	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     req.TicketID,
		IdentifierType: constants.TicketID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1, // FIXME: Hardcoded
			Valid: true,
		},
		Title:        "Deactivate LOC success",
		Description:  fmt.Sprintf("Success request deactivate LOC for %v. Status: %v", res.LoanAccountID, res.StatusReason),
		ActivityType: constants.Ticket,
		ReferenceID:  res.IdempotencyKey,
	}
	_, err = auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailsDTO, err.Error()))
	}

	return sqs.HandlerResponse{
		DeleteFromQueue: true,
		Message:         message.Message,
		Success:         true,
	}, nil
}

func updateQueueToCompleted(ctx context.Context, req *api.DeactivateLOCRequest) error {
	filters := []commonStorage.QueryCondition{
		commonStorage.EqualTo("identifier", req.LocAccountID),
		commonStorage.EqualTo("event_name", constants.DeactivateLOC),
		commonStorage.DescendingOrder("id"),
		commonStorage.Limit(1),
	}

	var dbResponse []*storage.QueueDTO
	dbResponse, err := logic.Process.GetQueueLog(ctx, filters)
	if err != nil && errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("Error in fetching queue data, for data %+v", req))
		return err
	}
	if len(dbResponse) == 0 {
		slog.FromContext(ctx).Info(constants.DeactivateLOCLogTag, fmt.Sprintf("No existing queue entry found, for queue data: %+v", req))
		return nil
	}
	queue := dbResponse[0]
	// Update Queue
	queue.Status = int32(1)
	queue.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	queue.UpdatedBy = sql.NullInt64{
		Int64: 1, // FIXME: Hardcoded
		Valid: true,
	}

	_, err = logic.Process.UpdateQueueLog(ctx, queue)
	if err != nil {
		slog.FromContext(ctx).Info(constants.DeactivateLOCLogTag, fmt.Sprintf("Error updating queue data for: %+v, error: %v", queue, err.Error()))
		return err
	}
	return nil
}
