// Package queue ...
//
// nolint: golint, gocyclo, funlen, dupl
package queue

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
)

// OpsUnblockAccountQueueHandler is to handle unlink account
func (qs *QueueService) OpsUnblockAccountQueueHandler(ctx context.Context, payload sqs.HandlerRequest) (sqs.HandlerResponse, error) {
	var (
		defaultHandlerResponse = sqs.HandlerResponse{
			DeleteFromQueue: true,
			Message:         payload.Message,
			Success:         false,
		}
	)

	req, err := unmarshallUnblockAccountRequest(payload)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "failed to unmarshal unblock account request", slog.Error(err))
		return defaultHandlerResponse, nil
	}

	// if requeued more than 5 times, delete from queue. Should be made configurable later
	if retryCount, _ := strconv.Atoi(aws.StringValue(payload.Message.Attributes[approximateReceiveCount])); retryCount >= 5 {
		slog.FromContext(ctx).Warn(unblockAccountLogTag, fmt.Sprintf("message requeued more than 5 times, delete from queue, req: %v", req))
		// audit trails for failed unblock account
		auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
			CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
			CreatedBy:      sql.NullInt64{Int64: 1, Valid: true}, // FIXME
			Identifier:     req.TicketID,
			IdentifierType: constants.TicketID,
			Title:          "Unblock Account failed",
			Description:    fmt.Sprintf("Failed to unblock account %s", req.AccountID),
			ActivityType:   constants.Ticket,
		}
		_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
		if auditErr != nil {
			slog.FromContext(ctx).Error(unblockAccountLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
		}
		return defaultHandlerResponse, nil
	}

	ticketID, err := strconv.Atoi(req.TicketID)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "failed to convert ticket id to int", slog.Error(err))
		return defaultHandlerResponse, nil
	}

	// process the message
	res, err := logic.Process.UnblockAccount(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "failed to process message", slog.Error(err))
		if res != nil {
			defaultHandlerResponse.DeleteFromQueue = !res.NeedRequeue
		}
		return defaultHandlerResponse, nil
	}

	// proceed ticket status
	err = logic.Process.SystemProceedTicketStatus(ctx, int64(ticketID))
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "failed to proceed ticket status", slog.Error(err))
	}

	// write audit trail for unblocking account result
	auditTrailReq := &auditTrailStorage.AuditTrailsDTO{
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:      sql.NullInt64{Int64: 1, Valid: true}, // FIXME
		Identifier:     req.TicketID,
		IdentifierType: constants.TicketID,
		Title:          "Unblock Account Success",
		Description:    fmt.Sprintf("Account %s has been unblocked", req.AccountID),
		ActivityType:   constants.Ticket,
	}
	_, auditErr := auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailReq)
	if auditErr != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailReq, auditErr))
	}

	return sqs.HandlerResponse{
		DeleteFromQueue: true,
		Message:         payload.Message,
		Success:         true,
	}, nil
}

func unmarshallUnblockAccountRequest(payload sqs.HandlerRequest) (*logic.UnblockAccountRequest, error) {
	var queueReq logic.QueueRequest
	if err := json.Unmarshal([]byte(*payload.Message.Body), &queueReq); err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to unmarshal payload from sqs")
	}

	req, ok := queueReq.Payload.(logic.UnblockAccountRequest)
	if !ok {
		return nil, errorwrapper.Error(apiError.BadRequest, "failed to cast payload to UnblockAccountRequest")
	}
	req.TicketID = queueReq.TicketID
	return &req, nil
}
