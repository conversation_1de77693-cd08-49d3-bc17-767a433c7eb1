package scheduler

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/handlers"
	generateexecutionresultfileV2 "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background/generate_execution_result_fileV2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"

	"github.com/go-co-op/gocron"
)

const (
	scheduleWaiveOffJobTagV2 = "scheduler.opsWaiveOffJobV2"
)

// ScheduleWaiveOffExporterJob ...
func ScheduleWaiveOffExporterJob(ctx context.Context, service *handlers.OpsService, appConfig *config.AppConfig, scheduler *gocron.Scheduler) {
	slog.FromContext(ctx).Info(scheduleWaiveOffJobTagV2, "Running ScheduleWaiveOffJobV2...")
	waiveOffJobConfig := appConfig.SchedulerConfig.WaiveOffJob
	_, err := scheduler.Cron(waiveOffJobConfig.CronExpression).Do(asyncScheduleWaiveOffJob, ctx, service, waiveOffJobConfig)
	if err != nil {
		slog.FromContext(ctx).Error(scheduleWaiveOffJobTagV2, fmt.Sprintf("Failed to run/schedule waiveeoff v2 alto status check job: %s", err.Error()))
	}
}

// asyncScheduleWaiveOffJob is the handler for the waive off exporter job
func asyncScheduleWaiveOffJob(ctx context.Context, service *handlers.OpsService, waiveOffJobConfig config.SchedulerConfigDetail) {
	slog.FromContext(ctx).Info(scheduleWaiveOffJobTagV2, "Started ScheduleWaiveOffJobV2...")
	defer slog.FromContext(ctx).Info(scheduleWaiveOffJobTagV2, "Finished ScheduleWaiveOffJobV2...")

	// make sure only one instance of this job is running
	if !acquireLock(ctx, service.RedisClient, waiveOffJobConfig.SchedulerLockDurationInSec, fileProcessorSchedulerID, scheduleWaiveOffJobTagV2) {
		slog.FromContext(ctx).Info(scheduleWaiveOffJobTagV2, "Failed to acquire lock")
		return
	}

	generateexecutionresultfileV2.New(
		service.AppConfig,
		service.RedisClient,
		fileprocessor.WaiveOffFileProcessorUsecaseV2,
	).Handle()
}
