package scheduler

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/handlers"
	generateexecutionresultfile "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background/generate_execution_result_file"
	generateexecutionresultfileV2 "gitlab.super-id.net/bersama/opsce/onedash-be/pkg/background/generate_execution_result_fileV2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"

	"github.com/go-co-op/gocron"
)

const (
	scheduleWriteOffJobTag   = "scheduler.opsWriteOffJob"
	fileProcessorSchedulerID = "OPS_BULK_FILE_PROC_SCHEDULER"
	scheduleWriteOffJobTagV2 = "scheduler.opsWriteOffJobV2"
)

// ScheduleWriteOffExporterJob ...
func ScheduleWriteOffExporterJob(ctx context.Context, service *handlers.OpsService, appConfig *config.AppConfig, scheduler *gocron.Scheduler) {
	slog.FromContext(ctx).Info(scheduleWriteOffJobTag, "Running ScheduleWriteOffJob...")
	writeOffJobConfig := appConfig.SchedulerConfig.WriteOffJob
	_, err := scheduler.Cron(writeOffJobConfig.CronExpression).Do(asyncScheduleWriteOffJob, ctx, service, writeOffJobConfig)
	if err != nil {
		slog.FromContext(ctx).Error(scheduleWriteOffJobTag, fmt.Sprintf("Failed to run/schedule alto status check job: %s", err.Error()))
	}

	_, err = scheduler.Cron(writeOffJobConfig.CronExpression).Do(asyncScheduleWriteOffJobV2, ctx, service, writeOffJobConfig)
	if err != nil {
		slog.FromContext(ctx).Error(scheduleWriteOffJobTag, fmt.Sprintf("Failed to run/schedule writeoff v2 alto status check job: %s", err.Error()))
	}
}

// asyncScheduleWriteOffJob is the handler for the write off exporter job
func asyncScheduleWriteOffJob(ctx context.Context, service *handlers.OpsService, writeOffJobConfig config.SchedulerConfigDetail) {
	slog.FromContext(ctx).Info(scheduleWriteOffJobTag, "Started ScheduleWriteOffJob...")
	defer slog.FromContext(ctx).Info(scheduleWriteOffJobTag, "Finished ScheduleWriteOffJob...")

	// make sure only one instance of this job is running
	if !acquireLock(ctx, service.RedisClient, writeOffJobConfig.SchedulerLockDurationInSec, fileProcessorSchedulerID, scheduleWriteOffJobTag) {
		slog.FromContext(ctx).Info(scheduleWriteOffJobTag, "Failed to acquire lock")
		return
	}

	generateexecutionresultfile.New(
		service.AppConfig,
		service.AppianClient,
		service.RedisClient,
	).Handle()
}

// asyncScheduleWriteOffJob is the handler for the write off exporter job
func asyncScheduleWriteOffJobV2(ctx context.Context, service *handlers.OpsService, writeOffJobConfig config.SchedulerConfigDetail) {
	slog.FromContext(ctx).Info(scheduleWriteOffJobTagV2, "Started ScheduleWriteOffJobV2...")
	defer slog.FromContext(ctx).Info(scheduleWriteOffJobTagV2, "Finished ScheduleWriteOffJobV2...")

	// make sure only one instance of this job is running
	if !acquireLock(ctx, service.RedisClient, writeOffJobConfig.SchedulerLockDurationInSec, fileProcessorSchedulerID, scheduleWriteOffJobTagV2) {
		slog.FromContext(ctx).Info(scheduleWriteOffJobTagV2, "Failed to acquire lock V2")
		return
	}

	generateexecutionresultfileV2.New(
		service.AppConfig,
		service.RedisClient,
		fileprocessor.WriteOffFileProcessorUsecaseV2,
	).Handle()
}
