package sqs

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	awssqs "github.com/aws/aws-sdk-go/service/sqs"
	"gitlab.myteksi.net/dakota/common/aws/sqsClient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

type sqsOrchestrator struct {
	conf            *config.SQSConfig
	handler         Hand<PERSON><PERSON>un<PERSON>
	cancel          context.CancelFunc
	mutex           sync.Mutex
	statsDClient    servusStatsD.Client
	producer        sqsClient.Producer
	consumer        sqsClient.Consumer
	tag             string
	consumerStarted bool
}

// HandleMessage
func (q *sqsOrchestrator) HandleMessage(ctx context.Context, req HandlerRequest) (HandlerResponse, error) {
	return q.handler(ctx, req)
}

// StartConsumer ...
func (q *sqsOrchestrator) StartConsumer(ctxt context.Context) {
	// prevent multiple consumer start
	q.mutex.Lock()
	if q.consumerStarted {
		return
	}
	ctx, cancel := context.WithCancel(ctxt)
	q.cancel = cancel
	q.consumerStarted = true
	q.mutex.Unlock()
	go q.consume(ctx)
}

// consume ...
func (q *sqsOrchestrator) consume(ctx context.Context) {
	messageCh := make(chan *awssqs.Message)
	var wg sync.WaitGroup
	defer close(messageCh)
	defer q.cancel()

	q.startMessagePool(ctx, messageCh, &wg, q.conf)

	for {
		select {
		case <-ctx.Done():
			slog.FromContext(ctx).Info(q.tag, "message consumer stopped")
			return
		default:
			receivedMsgs, err := q.consumer.ReceiveMessage(ctx, aws.StringSlice([]string{all}), int64(q.conf.NoWorker), nil, defaultVisibilityTimeout)
			if err != nil {
				slog.FromContext(ctx).Warn(q.tag, "failed to receive message from sqs", slog.Error(err))
				continue
			}

			// add number of messages received from the queue
			wg.Add(len(receivedMsgs))

			// send received messages to sqs, so they can be processed
			for _, message := range receivedMsgs {
				messageCh <- message
			}

			// wait for workers in the pool to be finished.
			wg.Wait()
		}
	}
}

// startMessagePoll: starts 10 goroutines which listens to the msgCh which receives the messages from the SQS.
//
// nolint: gocognit
func (q *sqsOrchestrator) startMessagePool(ctx context.Context, msgCh chan *awssqs.Message, wg *sync.WaitGroup, conf *config.SQSConfig) {
	for i := 0; i < conf.NoWorker; i++ {
		go func() {
			for {
				select {
				case <-ctx.Done():
					return
				case msg, channelClosed := <-msgCh:
					// If the channel is closed
					if !channelClosed {
						return
					}
					if msg == nil {
						continue
					} else {
						slog.FromContext(ctx).Info(logTag, fmt.Sprintf("[Consumer][%v] received message with message_id: %v", q.tag, *msg.MessageId))
					}

					// handle the message
					res, err := q.handler(ctx, HandlerRequest{
						Message: msg,
					})
					if err != nil {
						slog.FromContext(ctx).Warn(logTag, "error on handle message", slog.Error(err))
					}

					// if requeued more than 5 times, delete from queue. Should be made configurable later
					if retryCount, _ := strconv.Atoi(aws.StringValue(msg.Attributes["ApproximateReceiveCount"])); retryCount >= 5 {
						slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("message requeued more than 5 times, delete from queue, msg body: %v", *msg.Body))
						res.DeleteFromQueue = true
					} else {
						// if <5 retries, apply backoff
						if err := q.ChangeMessageVisibility(ctx, *msg.ReceiptHandle, exponentialBackoff(2, retryCount)); err != nil {
							slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("failed to change message visibility for %s", *msg.MessageId), slog.Error(err))
						}
					}

					// Delete the message
					// if want to requeue then do nothing, the message will be visible again after visibility timeout
					if res.DeleteFromQueue {
						if err := q.consumer.DeleteMessage(ctx, *msg.ReceiptHandle); err != nil {
							slog.FromContext(ctx).Error(logTag, "failed to delete message", slog.Error(err))
						}
					}

					slog.FromContext(ctx).Info(logTag, fmt.Sprintf("[Consumer][%v] finished processing message with message_id: %v", q.tag, *msg.MessageId))

					// release the waitgroup to inform that the message has been processed.
					wg.Done()
				}
			}
		}()
	}
}

// SendMessage ...
func (q *sqsOrchestrator) SendMessage(parent context.Context, messageBody string, attributes sqsClient.MessageAttributes) error {
	return q.producer.SendMessage(parent, messageBody, attributes)
}

// SendMessageWithDelay ...
func (q *sqsOrchestrator) SendMessageWithDelay(parent context.Context, messageBody string, delaySeconds int64, attributes sqsClient.MessageAttributes) error {
	return q.producer.SendMessageWithDelay(parent, messageBody, delaySeconds, attributes)
}

// GetQueueAttributes ...
func (q *sqsOrchestrator) GetQueueAttributes(parent context.Context, attributes sqsClient.AttributeNames) (map[string]*string, error) {
	return q.producer.GetQueueAttributes(parent, attributes)
}

// ReceiveMessage receives a message from SQS synchronously
func (q *sqsOrchestrator) ReceiveMessage(parent context.Context,
	attributeName sqsClient.AttributeNames,
	maxNumberOfMessages int64, // 1 to 10
	messageAttributeName sqsClient.AttributeNames,
	visibilityTimeout time.Duration, // maximum 12 hours
) ([]*awssqs.Message, error) {
	return q.consumer.ReceiveMessage(parent, attributeName, maxNumberOfMessages, messageAttributeName, visibilityTimeout)
}

// ChangeMessageVisibility changes the visibility of a message.
func (q *sqsOrchestrator) ChangeMessageVisibility(parent context.Context,
	receiptHandler string,
	visibilityTimeout time.Duration,
) error {
	return q.consumer.ChangeMessageVisibility(parent, receiptHandler, visibilityTimeout)
}

// DeleteMessage deletes a message from SQS synchronously
func (q *sqsOrchestrator) DeleteMessage(parent context.Context, receiptHandle string) error {
	return q.consumer.DeleteMessage(parent, receiptHandle)
}

// DeleteMessageBatch deletes multiple messages from SQS synchronously
func (q *sqsOrchestrator) DeleteMessageBatch(parent context.Context, receiptHandles ...string) (failed []string, err error) {
	return q.consumer.DeleteMessageBatch(parent, receiptHandles...)
}

// ListQueues returns a list of your queues whose names are begin with the speicified prefix
func (q *sqsOrchestrator) ListQueues(parent context.Context, prefix string) ([]*string, error) {
	return q.consumer.ListQueues(parent, prefix)
}

// ShutdownConsumer ...
func (q *sqsOrchestrator) ShutdownConsumer(ctx context.Context) {
	q.cancel()
}

// exponentialBackoff ...
func exponentialBackoff(baseInterval int, currentCount int) time.Duration {
	delay := 1 << (currentCount - 1) * baseInterval
	return time.Duration(delay) * time.Second
}

// IsEnabled ...
func (q *sqsOrchestrator) IsEnabled() bool {
	return q.conf.Enabled
}
