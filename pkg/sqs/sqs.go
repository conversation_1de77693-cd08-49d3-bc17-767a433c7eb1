// Package sqs provides the message queue
package sqs

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/service/sqs"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"

	"gitlab.myteksi.net/dakota/common/aws/sqsClient"
	"gitlab.myteksi.net/dakota/common/aws/sqsClient/consumer"
	"gitlab.myteksi.net/dakota/common/aws/sqsClient/producer"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	all    string = "All"
	logTag string = "onedash_sqs"

	defaultVisibilityTimeout = 10 * time.Second
)

// HandlerFunc is the function signature for the handler
type HandlerFunc func(ctx context.Context, req HandlerRequest) (HandlerResponse, error)

// HandlerRequest is the request object for the handler
type HandlerRequest struct {
	Message *sqs.Message
}

// HandlerResponse is the response object for the handler
type HandlerResponse struct {
	Success         bool
	DeleteFromQueue bool
	Message         *sqs.Message
}

// QueueClient is the interface for the sqs client for both producer and consumer
type QueueClient interface {
	sqsClient.Producer
	sqsClient.Consumer
	HandleMessage(ctx context.Context, req HandlerRequest) (HandlerResponse, error)
	StartConsumer(ctx context.Context)
	ShutdownConsumer(ctx context.Context)
	IsEnabled() bool
}

// NewQueueClient creates a new sqs client
func NewQueueClient(ctx context.Context, conf *config.SQSConfig, handler HandlerFunc, statsd servusStatsD.Client) (QueueClient, error) {
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("creating sqs client for queue %s", conf.Tag))
	prod, err := producer.NewProducer(conf.QueueURL, conf.AwsRegion, conf.DelaySeconds)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "failed to create sqs producer", slog.Error(err))
		return nil, err
	}

	cons, err := consumer.NewConsumer(conf.QueueURL, conf.AwsRegion, conf.WaitTimeSeconds)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "failed to create sqs consumer", slog.Error(err))
		return nil, err
	}

	orch := &sqsOrchestrator{
		conf:         conf,
		handler:      handler,
		mutex:        sync.Mutex{},
		statsDClient: statsd,
		producer:     &prod,
		consumer:     &cons,
		tag:          conf.Tag,
	}

	return orch, nil
}
