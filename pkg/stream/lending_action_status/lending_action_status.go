// Package lendingactionstatus provides the logic to handle lending action status event.
package lendingactionstatus

import (
	"context"
	"fmt"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/ops_lending_action_status_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	onedashRedis "gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/fileprocessor"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// <PERSON><PERSON> handles the lending action status event.
func Handle(ctx context.Context, conf *config.AppConfig, data *ops_lending_action_status_event.OpsLendingActionStatusEvent) error {
	slog.FromContext(ctx).Info(constants.LendingActionStatusKafkaTag, "Received event from lending-action-status-streams")

	if data != nil {
		slog.FromContext(ctx).Info(constants.LendingActionStatusKafkaTag, fmt.Sprintf("Received data from lending stream: %+v", data))
	}

	if data.EventType == constants.InsuranceClaimStatus {
		return handleInsuranceClaimStatusUpdate(ctx, conf, data)
	}

	return nil
}

func handleInsuranceClaimStatusUpdate(ctx context.Context, conf *config.AppConfig, data *ops_lending_action_status_event.OpsLendingActionStatusEvent) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, conf.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, constants.LendingActionStatusKafkaTag, "failed to get database handle")
	}
	interactor := fileprocessor.NewBulkWriteOffFileProcessor().WithCache(onedashRedis.RedisClient).WithDB(db)

	processorDump, err := storage.GetFileNameFileProcessorDumpRowByReferenceIDAndIdentifier(ctx, db, data.ReferenceID, data.Value)
	if err != nil {
		return errorwrapper.WrapError(err, constants.LendingActionStatusKafkaTag, "failed to get filename")
	}

	// update row status
	err = interactor.UpdateRowStatus(ctx, data.Value, fileprocessor.WriteOffDTO{
		CIFNumber:              data.Value,
		LoanAccelerationStatus: data.Payload[fileprocessor.FieldLoanAccelerationStatus],
		WriteOffStatus:         data.Payload[fileprocessor.FieldWriteOffStatus],
		WaiveOffStatus:         data.Payload[fileprocessor.FieldWaiveOffStatus],
		CloseLOCStatus:         data.Payload[fileprocessor.FieldCloseLOCStatus],
		FailureReason:          data.Payload[fileprocessor.FieldFailureReason],
		ReferenceID:            data.ReferenceID,
	}, data.Payload, processorDump.FileName.String)
	if err != nil {
		return errorwrapper.WrapError(err, constants.LendingActionStatusKafkaTag, "failed to update row status")
	}

	return nil
}
