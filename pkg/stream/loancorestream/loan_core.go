// Package loancorestream is for handling loan core event
package loancorestream

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"

	loanCoreLocSchema "gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_loc"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

const (
	// LocDeactivationEventType is event type for deactivate LOC
	LocDeactivationEventType = "LOC_DEACTIVATION"

	// StatusClosedLocClosure ...
	StatusClosedLocClosure = "CLOSED"
)

// HandleLoanCoreStream ...
func HandleLoanCoreStream(ctx context.Context, loanCoreData *loanCoreLocSchema.LoanCoreLoc) error {
	slog.FromContext(ctx).Info(constants.LoanCoreKafkaTag, fmt.Sprintf("Received event from loan-core-streams: %+v", loanCoreData))
	// Skip if not LOC_DEACTIVATION
	if loanCoreData.EventType != LocDeactivationEventType {
		slog.FromContext(ctx).Info(constants.LoanCoreKafkaTag, fmt.Sprintf("HandleLoanCoreStream: skipped, event type is not %s: %+v", LocDeactivationEventType, loanCoreData))
		return nil
	}

	err := handlePostDeactivateLOC(ctx, loanCoreData)
	if err != nil {
		return err
	}
	return nil
}

func handlePostDeactivateLOC(ctx context.Context, loanCoreData *loanCoreLocSchema.LoanCoreLoc) error {
	var dbResponse []*storage.QueueDTO
	dbResponse, err := getQueueData(ctx, loanCoreData.AccountID)
	if err != nil && errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Info(constants.LoanCoreKafkaTag, fmt.Sprintf("Error in fetching queue data, for data %+v", loanCoreData))
		return err
	}
	if len(dbResponse) == 0 {
		slog.FromContext(ctx).Info(constants.LoanCoreKafkaTag, fmt.Sprintf("No existing queue entry found, for data: %+v", loanCoreData))
		return nil
	}

	queue := dbResponse[0]
	var desc string
	if loanCoreData.Status == StatusClosedLocClosure {
		desc = fmt.Sprintf("Success deactivate LOC for %v. Status: %v", loanCoreData.AccountID, loanCoreData.Status)
	} else {
		desc = fmt.Sprintf("Failed deactivate LOC for %v. Status: %v - %v", loanCoreData.AccountID, loanCoreData.Status, loanCoreData.StatusReasonDescription)
	}

	//Add Audit Trail
	auditTrailsDTO := &auditTrailStorage.AuditTrailsDTO{
		Identifier:     queue.TicketID,
		IdentifierType: constants.TicketID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1, // FIXME: Hardcoded
			Valid: true,
		},
		Title:        "Deactivate LOC Callback",
		Description:  desc,
		ActivityType: constants.Ticket,
	}
	_, err = auditTrailLogic.AuditTrailProcess.InsertAuditTrails(ctx, auditTrailsDTO)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("error writing audit trail req: %v, error: %v", auditTrailsDTO, err.Error()))
	}

	//TODO: Callback Appian

	// Update Queue
	queue.Status = int32(1)
	queue.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}
	queue.UpdatedBy = sql.NullInt64{
		Int64: 1, // FIXME: Hardcoded
		Valid: true,
	}

	_, err = logic.Process.UpdateQueueLog(ctx, queue)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DeactivateLOCLogTag, fmt.Sprintf("error update queue: %v, error: %v", queue, err.Error()))
	}
	return nil
}

func getQueueData(ctx context.Context, locAccountID string) ([]*storage.QueueDTO, error) {
	filters := []commonStorage.QueryCondition{
		commonStorage.EqualTo("identifier", locAccountID),
		commonStorage.EqualTo("event_name", constants.DeactivateLOC),
		commonStorage.DescendingOrder("id"),
		commonStorage.Limit(1),
	}

	return logic.Process.GetQueueLog(ctx, filters)
}
