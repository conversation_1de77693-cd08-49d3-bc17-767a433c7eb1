package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

func GetCustomerSegmentList(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]CustomerSegmentDTO, error) {
	baseQuery := `SELECT id, is_active, created_by, created_at, updated_by, updated_at, name FROM customer_segment`
	query, args := storage.BuildQuery(baseQuery, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "customer segment not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var customerSegments []CustomerSegmentDTO
	for rows.Next() {
		var c CustomerSegmentDTO
		err := rows.Scan(
			&c.ID, &c.IsActive, &c.CreatedBy,
			&c.CreatedAt, &c.UpdatedBy, &c.UpdatedAt, &c.Name,
		)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		customerSegments = append(customerSegments, c)
	}

	return customerSegments, nil
}

// GetCustomerSegmentWithCondition get 1 customer segment matching with condition
func GetCustomerSegmentWithCondition(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (*CustomerSegmentDTO, error) {
	// Prepare the query
	baseQuery := `SELECT id, is_active, created_by, created_at, updated_by, updated_at, name FROM customer_segment`
	query, args := storage.BuildQuery(baseQuery, conditions...)
	// Execute the query
	row := db.QueryRowContext(ctx, query, args...)

	// Scan the result
	customerSegment := &CustomerSegmentDTO{}
	err := row.Scan(&customerSegment.ID, &customerSegment.IsActive, &customerSegment.CreatedBy, &customerSegment.CreatedAt, &customerSegment.UpdatedBy, &customerSegment.UpdatedAt, &customerSegment.Name)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return customerSegment, nil
}
