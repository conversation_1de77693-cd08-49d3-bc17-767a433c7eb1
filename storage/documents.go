package storage

import (
	"context"
	"database/sql"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateDocument creates a document
func CreateDocument(ctx context.Context, db *sql.Tx, dto *DocumentDTO) (int64, error) {
	var query = `
		INSERT INTO documents (created_at, created_by, name, url, ticket_id, status, type, description, count, validation_status, parent_document_id)
		VALUES (?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?)
	`

	res, err := db.ExecContext(ctx, query, dto.CreatedAt, dto.CreatedBy, dto.Name, dto.URL, dto.TicketID, dto.Type, dto.Description, dto.Count, dto.ValidationStatus, dto.ParentDocumentID)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, "storage: failed to create document")
	}

	return res.LastInsertId()
}

// DeleteDocument soft deletes a document
func DeleteDocument(ctx context.Context, db *sql.DB, documentID int64) error {
	var query = `
		UPDATE documents
		SET status = 0
		WHERE id = ?
	`

	_, err := db.ExecContext(ctx, query, documentID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "storage: failed to delete document")
	}

	return nil
}

// GetDocumentByName gets a document by name
func GetDocumentByName(ctx context.Context, db *sql.DB, name string) (*DocumentDTO, error) {
	var query = `
		SELECT id, created_at, created_by, name, url, ticket_id, status, type, description, count, validation_status, parent_document_id
		FROM documents
		WHERE status = 1 AND name = ?
	`

	var dto DocumentDTO
	err := db.QueryRowContext(ctx, query, name).Scan(&dto.ID, &dto.CreatedAt, &dto.CreatedBy, &dto.Name, &dto.URL, &dto.TicketID, &dto.Status, &dto.Type, &dto.Description, &dto.Count, &dto.ValidationStatus, &dto.ParentDocumentID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to get document")
	}

	return &dto, nil
}

// GetTicketDocuments gets documents by ticket ID
//
// nolint: errcheck
func GetTicketDocuments(ctx context.Context, db *sql.DB, ticketID int64) ([]*DocumentListDTO, error) {
	var query = `
		SELECT d.id, d.created_at, u1.name AS created_by, d.name, d.url, d.ticket_id, d.type, d.description
		FROM documents d
		LEFT JOIN users u1 ON d.created_by = u1.id
		WHERE d.ticket_id = ? AND d.status = 1
		ORDER BY d.id DESC
	`

	rows, err := db.QueryContext(ctx, query, ticketID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, "failed to get documents")
	}
	defer rows.Close()

	var dtos []*DocumentListDTO
	for rows.Next() {
		var dto DocumentListDTO
		err := rows.Scan(&dto.ID, &dto.CreatedAt, &dto.CreatedBy, &dto.Name, &dto.URL, &dto.TicketID, &dto.Type, &dto.Description)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, "failed to scan documents")
		}
		dtos = append(dtos, &dto)
	}

	return dtos, nil
}

// LinkDocumentsToTicket links unlinked documents to a ticket
func LinkDocumentsToTicket(ctx context.Context, tx *sql.Tx, ticketID int64, documentIDs []int64) error {
	if len(documentIDs) == 0 {
		return nil
	}

	// Convert []int64 to []any for the query builder
	values := make([]any, len(documentIDs))
	for i, id := range documentIDs {
		values[i] = id
	}

	baseQuery := `UPDATE documents SET ticket_id = ?, updated_at = NOW()`

	// Build WHERE conditions using query builder
	inCondition := commonStorage.In("id", values...)
	isNullCondition := commonStorage.IsNull("ticket_id")
	statusCondition := commonStorage.EqualTo("status", 1)

	// Combine conditions
	whereConditions := commonStorage.And(inCondition, isNullCondition, statusCondition)

	// Build the full query
	query, args := commonStorage.BuildQuery(baseQuery, whereConditions)

	// Prepend ticketID to args since it's the first parameter in the UPDATE
	allArgs := append([]any{ticketID}, args...)

	result, err := tx.ExecContext(ctx, query, allArgs...)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	if rowsAffected != int64(len(documentIDs)) {
		return errorwrapper.Error(apiError.BadRequest, "some documents are already linked or don't exist")
	}

	return nil
}

// ValidateUnlinkedDocuments validates that documents exist and are unlinked
func ValidateUnlinkedDocuments(ctx context.Context, db *sql.DB, documentIDs []int64) error {
	if len(documentIDs) == 0 {
		return nil
	}

	// Convert []int64 to []any for the query builder
	values := make([]any, len(documentIDs))
	for i, id := range documentIDs {
		values[i] = id
	}

	baseQuery := `SELECT COUNT(*) FROM documents`

	// Build WHERE conditions using query builder
	inCondition := commonStorage.In("id", values...)
	isNullCondition := commonStorage.IsNull("ticket_id")
	statusCondition := commonStorage.EqualTo("status", 1)

	// Combine conditions
	whereConditions := commonStorage.And(inCondition, isNullCondition, statusCondition)

	// Build the full query
	query, args := commonStorage.BuildQuery(baseQuery, whereConditions)

	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	if count != int64(len(documentIDs)) {
		return errorwrapper.Error(apiError.BadRequest, "some documents are already linked or don't exist")
	}

	return nil
}

// Update Document For Validation
func UpdateDocumentsForValidation(ctx context.Context, db *sql.DB, dto *DocumentDTO) error {
	query := `
		UPDATE documents
		SET 
			updated_at = ?,
			count = ?, 
			validation_status = ?,
			parent_document_id = ?
		WHERE id = ?;
	`

	result, err := db.ExecContext(ctx, query, dto.UpdatedAt, dto.Count, dto.ValidationStatus, dto.ParentDocumentID, dto.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// DeleteDocument soft deletes a document
func DeleteDocumentByParentID(ctx context.Context, db *sql.DB, documentID int64) error {
	var query = `
		UPDATE documents
		SET status = 0
		WHERE parent_document_id = ?
	`

	_, err := db.ExecContext(ctx, query, documentID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "storage: failed to delete document")
	}

	return nil
}
