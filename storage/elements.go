package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateElement ...
func CreateElement(ctx context.Context, db *sql.DB, element *ElementDTO) (int64, error) {
	// Prepare the query
	query := `INSERT INTO elements (name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	// Execute the query
	result, err := db.ExecContext(ctx, query, element.Name, element.CreatedAt.Time, element.CreatedBy.Int64, element.UpdatedAt.Time, element.UpdatedBy.Int64, element.ModuleID, element.DefaultPriorityID, element.Code, element.Status, element.HasTicketing, element.DefaultTicketRequestorID, element.DefaultCustomerSegmentID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	// Get the last inserted ID
	lastID, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return lastID, nil
}

// UpdateElement ...
func UpdateElement(ctx context.Context, db *sql.DB, element *ElementDTO) error {
	// Prepare the query
	query := `
		UPDATE elements
		SET name = ?, updated_at = ?, updated_by = ?, module_id = ?, default_priority_id = ?, code = ?, status = ?, has_ticketing = ?, default_customer_segment_id = ?, default_ticket_requestor_id = ?
		WHERE id = ?
	`
	// Execute the query
	_, err := db.ExecContext(ctx, query, element.Name, element.UpdatedAt.Time, element.UpdatedBy.Int64, element.ModuleID, element.DefaultPriorityID, element.Code, element.Status, element.HasTicketing, element.DefaultCustomerSegmentID, element.DefaultTicketRequestorID, element.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetElementByID ...
//
// nolint: dupl
func GetElementByID(ctx context.Context, db *sql.DB, id int64) (*ElementDTO, error) {
	// Prepare the query
	query := `
		SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id
		FROM elements
		WHERE id = ?
	`
	// Execute the query
	row := db.QueryRowContext(ctx, query, id)

	// Scan the result
	element := &ElementDTO{}
	err := row.Scan(&element.ID, &element.Name, &element.CreatedAt, &element.CreatedBy, &element.UpdatedAt, &element.UpdatedBy, &element.ModuleID, &element.DefaultPriorityID, &element.Code, &element.Status, &element.HasTicketing, &element.DefaultTicketRequestorID, &element.DefaultCustomerSegmentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return element, nil
}

// GetUserEligibleElements ...
//
// nolint: errcheck, dupl
func GetUserEligibleElements(ctx context.Context, db *sql.DB, userID string, perm int) ([]ElementDTO, error) {
	// Prepare the query
	query := `
		SELECT e.id, e.name, e.created_at, e.created_by, e.updated_at, e.updated_by, e.module_id, e.default_priority_id, e.code
		FROM elements e
		INNER JOIN roles_elements_permissions rep on rep.element_id = e.id
		INNER JOIN users_roles ur on ur.role_id = rep.role_id
		INNER JOIN users u on ur.user_id = u.id
		WHERE (rep.bitwise_value & ?) = ?
		AND u.user_id = ?
	`

	// Execute the query
	rows, err := db.QueryContext(ctx, query, perm, perm, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	// Scan the results
	elements := []ElementDTO{}
	for rows.Next() {
		element := ElementDTO{}
		err := rows.Scan(&element.ID, &element.Name, &element.CreatedAt, &element.CreatedBy, &element.UpdatedAt, &element.UpdatedBy, &element.ModuleID, &element.DefaultPriorityID, &element.Code)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		elements = append(elements, element)
	}

	return elements, nil
}

// GetModuleElementsByRole ...
//
// nolint: dupl
func GetModuleElementsByRole(ctx context.Context, db *sql.DB, roleID int64) ([]*ModuleElementPermissionsDTO, error) {
	var query = `
	SELECT rep.element_id as element_id,
	       e.name as element_name,
	       e.module_id as module_id,
	       m.name as module_name,
	       rep.bitwise_value as bitwise_value
	FROM roles_elements_permissions rep
	INNER JOIN elements e ON rep.element_id = e.id
	INNER JOIN modules m ON e.module_id = m.id
	where rep.role_id = ?
	`

	rows, err := db.QueryContext(ctx, query, roleID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var permissions []*ModuleElementPermissionsDTO
	for rows.Next() {
		var perm ModuleElementPermissionsDTO
		scanErr := rows.Scan(&perm.ElementID,
			&perm.ElementName,
			&perm.ModuleID,
			&perm.ModuleName,
			&perm.BitwiseValue)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		permissions = append(permissions, &perm)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return permissions, nil
}

// GetElements ...
func GetElements(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]ElementListDTO, error) {
	// Prepare the query
	query := `
		SELECT e.id, e.name, e.created_at, u.name as created_by, e.updated_at, u2.name as updated_by, e.module_id, e.default_priority_id, e.code, e.status, e.has_ticketing, e.default_ticket_requestor_id, e.default_customer_segment_id
		FROM elements e
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN users u2 ON e.updated_by = u2.id
	`
	query, args := storage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	// Scan the results
	elements := []ElementListDTO{}
	for rows.Next() {
		element := ElementListDTO{}
		err := rows.Scan(&element.ID, &element.Name, &element.CreatedAt, &element.CreatedBy, &element.UpdatedAt, &element.UpdatedBy, &element.ModuleID, &element.DefaultPriorityID, &element.Code, &element.Status, &element.HasTicketing, &element.DefaultTicketRequestorID, &element.DefaultCustomerSegmentID)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		elements = append(elements, element)
	}

	return elements, nil
}

// GetElementsCount ...
func GetElementsCount(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (int64, error) {
	// Prepare the query
	baseQuery := `
		SELECT COUNT(id)
		FROM elements e
	`
	query, args := storage.BuildQuery(baseQuery, conditions...)

	// Execute the query
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return count, nil
}

// GetUserEligibleElementsByModuleID ...
//
// nolint: errcheck, dupl
func GetUserEligibleElementsByModuleID(ctx context.Context, db *sql.DB, perms string, userID, moduleID int64) ([]ElementDTO, error) {
	// Prepare the query
	query := `
		SELECT e.id, e.name, e.created_at, e.created_by, e.updated_at, e.updated_by, e.module_id, e.default_priority_id, e.code
		FROM elements e
		INNER JOIN permissions p on p.module_id = e.module_id
		INNER JOIN roles_elements_permissions rep on rep.element_id = e.id
		INNER JOIN users_roles ur on ur.role_id = rep.role_id
		WHERE p.name = ?
		AND ur.user_id = ?
		AND e.module_id = ?
		GROUP BY e.id
	`

	// Execute the query
	rows, err := db.QueryContext(ctx, query, perms, userID, moduleID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	// Scan the results
	elements := []ElementDTO{}
	for rows.Next() {
		element := ElementDTO{}
		err := rows.Scan(&element.ID, &element.Name, &element.CreatedAt, &element.CreatedBy, &element.UpdatedAt, &element.UpdatedBy, &element.ModuleID, &element.DefaultPriorityID, &element.Code)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		elements = append(elements, element)
	}

	return elements, nil
}

// GetElementByCode ...
//
// nolint: dupl
func GetElementByCode(ctx context.Context, db *sql.DB, code string) (*ElementDTO, error) {
	// Prepare the query
	query := `
		SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id
		FROM elements
		WHERE code = ?
	`
	// Execute the query
	row := db.QueryRowContext(ctx, query, code)

	// Scan the result
	element := &ElementDTO{}
	err := row.Scan(&element.ID, &element.Name, &element.CreatedAt, &element.CreatedBy, &element.UpdatedAt, &element.UpdatedBy, &element.ModuleID, &element.DefaultPriorityID, &element.Code, &element.Status, &element.HasTicketing, &element.DefaultTicketRequestorID, &element.DefaultCustomerSegmentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return element, nil
}

// GetElementByCodeExcludeID ...
func GetElementByCodeExcludeID(ctx context.Context, db *sql.DB, code string, excludeID int64) (*ElementDTO, error) {
	query := `SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, 
             default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id
             FROM elements 
             WHERE code = ?`
	args := []any{code}

	if excludeID != 0 {
		query += " AND id != ?"
		args = append(args, excludeID)
	}

	element := &ElementDTO{}
	err := db.QueryRowContext(ctx, query, args...).Scan(
		&element.ID,
		&element.Name,
		&element.ModuleID,
		&element.Code,
		&element.Status,
		&element.CreatedAt,
		&element.CreatedBy,
		&element.UpdatedAt,
		&element.UpdatedBy,
		&element.DefaultPriorityID,
		&element.HasTicketing,
		&element.DefaultTicketRequestorID,
		&element.DefaultCustomerSegmentID,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	return element, nil
}
