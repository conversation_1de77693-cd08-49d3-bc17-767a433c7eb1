package storage

import (
	"context"
	"database/sql"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetElementByCode(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	now := time.Now()

	testCases := []struct {
		name          string
		code          string
		mockSetup     func()
		expectedError bool
		expectedNil   bool
	}{
		{
			name: "success - element found",
			code: "TEST_CODE",
			mockSetup: func() {
				rows := sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}).
					AddRow(1, "Test Element", now, 1, now, 1, 1, 1, "TEST_CODE", 1, true, -1, -1)
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ?`)).
					WithArgs("TEST_CODE").
					WillReturnRows(rows)
			},
			expectedError: false,
			expectedNil:   false,
		},
		{
			name: "success - element not found",
			code: "NON_EXISTENT",
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ?`)).
					WithArgs("NON_EXISTENT").
					WillReturnError(sql.ErrNoRows)
			},
			expectedError: true,
			expectedNil:   true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			result, err := GetElementByCode(ctx, db, tc.code)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tc.expectedNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tc.code, result.Code)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestGetElementByCodeExcludeID(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	now := time.Now()

	testCases := []struct {
		name          string
		code          string
		excludeID     int64
		mockSetup     func()
		expectedError bool
		expectedNil   bool
	}{
		{
			name:      "success - no duplicate found",
			code:      "TEST_CODE",
			excludeID: 1,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ? AND id != ?`)).
					WithArgs("TEST_CODE", int64(1)).
					WillReturnError(sql.ErrNoRows)
			},
			expectedError: false,
			expectedNil:   true,
		},
		{
			name:      "success - duplicate found",
			code:      "TEST_CODE",
			excludeID: 1,
			mockSetup: func() {
				rows := sqlmock.NewRows([]string{"id", "name", "module_id", "code", "status", "created_at", "created_by", "updated_at", "updated_by", "default_priority_id", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}).
					AddRow(2, "Test Element", 1, "TEST_CODE", 1, now, 1, now, 1, 1, true, -1, -1)
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ? AND id != ?`)).
					WithArgs("TEST_CODE", int64(1)).
					WillReturnRows(rows)
			},
			expectedError: false,
			expectedNil:   false,
		},
		{
			name:      "error - database error",
			code:      "TEST_CODE",
			excludeID: 1,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ? AND id != ?`)).
					WithArgs("TEST_CODE", int64(1)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
			expectedNil:   true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			result, err := GetElementByCodeExcludeID(ctx, db, tc.code, tc.excludeID)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tc.expectedNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tc.code, result.Code)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
