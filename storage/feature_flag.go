package storage

import (
	"context"
	"database/sql"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// CreateFeatureFlag ...
func CreateFeatureFlag(ctx context.Context, db *sql.DB, p *FeatureFlagDTO) error {
	var query = `INSERT INTO feature_flag (name, value, description, status, created_at, created_by, updated_at, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := db.ExecContext(ctx, query, p.Name, p.Value, p.Description, p.Status, p.<PERSON>, p.<PERSON>, p.<PERSON>, p.UpdatedBy)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	_, err = result.LastInsertId()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// UpdateFeatureFlag ...
func UpdateFeatureFlag(ctx context.Context, db *sql.DB, r *FeatureFlagDTO) error {
	var query = `UPDATE feature_flag
	SET updated_at = ?, updated_by = ?, value = ?, description = ?
	WHERE name = ? and status = 1`

	result, err := db.ExecContext(ctx, query, r.UpdatedAt, r.UpdatedBy, r.Value, r.Description, r.Name)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// GetFeatureFlagByName ...
func GetFeatureFlagByName(ctx context.Context, db *sql.DB, name []string) ([]*FeatureFlagDTO, error) {
	var query = `SELECT name, value, description FROM feature_flag`
	curName := utils.ConvertArrayStringToAny(name)
	conditions := []commonStorage.QueryCondition{
		commonStorage.In("name", curName...),
		commonStorage.EqualTo("status", 1),
	}
	query, args := commonStorage.BuildQuery(query, conditions...)
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var featureFlags []*FeatureFlagDTO
	for rows.Next() {
		var featureFlag FeatureFlagDTO
		scanErr := rows.Scan(&featureFlag.Name,
			&featureFlag.Value,
			&featureFlag.Description)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		featureFlags = append(featureFlags, &featureFlag)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return featureFlags, nil
}

// DeleteFeatureFlag will delete feature flag
func DeleteFeatureFlag(ctx context.Context, db *sql.DB, r *FeatureFlagDTO) error {
	var query = `DELETE FROM feature_flag WHERE name = ?`
	result, err := db.ExecContext(ctx, query, r.Name)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("delete feature flag expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// GetFeatureFlagList ...
func GetFeatureFlagList(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition) ([]*FeatureFlagListDTO, error) {
	var query = `
	select r.name, r.value, r.description, r.status, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by from feature_flag r
    left join users u ON r.created_by = u.id 
	left join users u2 ON r.updated_by = u2.id`
	query, args := commonStorage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var featureFlags []*FeatureFlagListDTO
	for rows.Next() {
		var ff FeatureFlagListDTO
		scanErr := rows.Scan(
			&ff.Name,
			&ff.Value,
			&ff.Description,
			&ff.Status,
			&ff.CreatedAt,
			&ff.UpdatedAt,
			&ff.CreatedBy,
			&ff.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		featureFlags = append(featureFlags, &ff)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return featureFlags, nil
}

// GetCountFeatureFlags ...
func GetCountFeatureFlags(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition) (int64, error) {
	var query = `SELECT count(*) FROM feature_flag r`
	query, args := commonStorage.BuildQuery(query, conditions...)
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}
