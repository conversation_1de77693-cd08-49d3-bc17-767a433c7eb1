package storage

import (
	"context"
	"database/sql"
	"encoding/json"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateAuditLog ...
func CreateAuditLog(ctx context.Context, db *sql.DB, auditLog *LogAuditTrail) (int64, error) {
	var query = `INSERT INTO log_audit_trails (user_id, name, event, email, action, metadata, related_id, service, created_at, safe_id)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	if auditLog.Metadata == nil {
		auditLog.Metadata = json.RawMessage(`{}`)
	}

	result, err := db.ExecContext(ctx, query,
		auditLog.UserID,
		auditLog.Name,
		auditLog.Event,
		auditLog.Email,
		auditLog.Action,
		auditLog.Metadata,
		auditLog.RelatedID,
		auditLog.Service,
		auditLog.CreatedAt,
		auditLog.SafeID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// GetOneAuditTrailForRuleChecking ...
func GetOneAuditTrailForRuleChecking(ctx context.Context, db *sql.DB,
	userID, relatedID, serviceName, event, safeID string) (*LogAuditTrail, error) {
	var query = `SELECT id, user_id, name, event, email, action, metadata, related_id, service, created_at, safe_id 
					FROM log_audit_trails WHERE user_id = ? AND related_id = ? AND service = ? AND event = ? AND safe_id = ?
                                 ORDER BY created_at DESC LIMIT 1`
	var result LogAuditTrail
	err := db.QueryRowContext(ctx, query, userID, relatedID, serviceName, event, safeID).
		Scan(&result.ID,
			&result.UserID,
			&result.Name,
			&result.Event,
			&result.Email,
			&result.Action,
			&result.Metadata,
			&result.RelatedID,
			&result.Service,
			&result.CreatedAt,
			&result.SafeID)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// GetAuditTrailLogs ...
// nolint: funlen
func GetAuditTrailLogs(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition) ([]*LogAuditTrail, error) {
	query := "SELECT id, user_id, name, event, email, action, metadata, related_id, service, created_at, safe_id FROM log_audit_trails"

	query, args := commonStorage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var logs []*LogAuditTrail
	for rows.Next() {
		var log LogAuditTrail
		var tmp = &json.RawMessage{}
		var tmpSafeID *string
		scanErr := rows.Scan(&log.ID,
			&log.UserID,
			&log.Name,
			&log.Event,
			&log.Email,
			&log.Action,
			&tmp,
			&log.RelatedID,
			&log.Service,
			&log.CreatedAt,
			&tmpSafeID,
		)

		if tmp != nil {
			log.Metadata = *tmp
		}

		if tmpSafeID != nil {
			log.SafeID = *tmpSafeID
		}

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		logs = append(logs, &log)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return logs, nil
}
