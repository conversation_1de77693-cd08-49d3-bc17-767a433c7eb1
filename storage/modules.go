package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateModule ...
func CreateModule(ctx context.Context, db *sql.DB, module *ModuleDTO) (int64, error) {
	// Prepare the query
	query := `
		INSERT INTO modules (name, created_at, created_by, updated_at, updated_by, status)
		VALUES (?, ?, ?, ?, ?, ?)
	`
	// Execute the query
	result, err := db.ExecContext(ctx, query, module.Name, module.CreatedAt.Time, module.CreatedBy.Int64, module.UpdatedAt.Time, module.UpdatedBy.Int64, module.Status)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	// Get the last inserted ID
	lastID, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return lastID, nil
}

// UpdateModule ...
func UpdateModule(ctx context.Context, db *sql.DB, module *ModuleDTO) error {
	// Prepare the query
	query := `
		UPDATE modules
		SET name = ?, updated_at = ?, updated_by = ?, status = ?
		WHERE id = ?
	`
	// Execute the query
	_, err := db.ExecContext(ctx, query, module.Name, module.UpdatedAt.Time, module.UpdatedBy.Int64, module.Status, module.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetModulesCount retrieves only the total count of modules
func GetModulesCount(ctx context.Context, db *sql.DB, hasTicketing bool, conditions []storage.QueryCondition) (int64, error) {
	baseQuery := `
        SELECT COUNT(DISTINCT m.id) 
        FROM modules m
    `

	if hasTicketing {
		// return module with at least one element with has_ticketing
		baseQuery = `
        SELECT COUNT(DISTINCT m.id) 
        FROM modules m
		INNER JOIN elements e ON m.id = e.module_id AND e.has_ticketing = 1
    `
	}

	query, args := storage.BuildQuery(baseQuery, conditions...)

	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return count, nil
}

// GetModules retrieves modules with optional query conditions
func GetModules(ctx context.Context, db *sql.DB, hasTicketing bool, conditions []storage.QueryCondition) ([]ModuleListDTO, error) {
	baseQuery := `
        SELECT DISTINCT m.id, m.name, m.created_at, u.name as created_by, m.updated_at, u2.name as updated_by, m.status
        FROM modules m
		LEFT JOIN users u ON m.created_by = u.id
		LEFT JOIN users u2 ON m.updated_by = u2.id
    `

	if hasTicketing {
		// return module with at least one element with has_ticketing
		baseQuery = `
        SELECT DISTINCT m.id, m.name, m.created_at, u.name as created_by, m.updated_at, u2.name as updated_by, m.status
        FROM modules m
		LEFT JOIN users u ON m.created_by = u.id
		LEFT JOIN users u2 ON m.updated_by = u2.id
		INNER JOIN elements e ON m.id = e.module_id AND e.has_ticketing = 1
    `
	}

	query, args := storage.BuildQuery(baseQuery, conditions...)
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var modules []ModuleListDTO
	for rows.Next() {
		var module ModuleListDTO
		scanErr := rows.Scan(
			&module.ID,
			&module.Name,
			&module.CreatedAt,
			&module.CreatedBy,
			&module.UpdatedAt,
			&module.UpdatedBy,
			&module.Status,
		)
		module.HasTicketing = hasTicketing

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		modules = append(modules, module)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return modules, nil
}

// GetModuleByName ...
func GetModuleByName(ctx context.Context, db *sql.DB, name string) (*ModuleDTO, error) {
	query := `SELECT id, name, status, created_at, created_by, updated_at, updated_by 
             FROM modules 
             WHERE name = ?`

	module := &ModuleDTO{}
	err := db.QueryRowContext(ctx, query, name).Scan(
		&module.ID,
		&module.Name,
		&module.Status,
		&module.CreatedAt,
		&module.CreatedBy,
		&module.UpdatedAt,
		&module.UpdatedBy,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	return module, nil
}

// GetModuleByNameExcludeID ...
func GetModuleByNameExcludeID(ctx context.Context, db *sql.DB, name string, excludeID int64) (*ModuleDTO, error) {
	query := `SELECT id, name, status, created_at, created_by, updated_at, updated_by 
             FROM modules 
             WHERE name = ? AND id != ?`

	module := &ModuleDTO{}
	err := db.QueryRowContext(ctx, query, name, excludeID).Scan(
		&module.ID,
		&module.Name,
		&module.Status,
		&module.CreatedAt,
		&module.CreatedBy,
		&module.UpdatedAt,
		&module.UpdatedBy,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	return module, nil
}
