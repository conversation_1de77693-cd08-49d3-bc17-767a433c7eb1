package storage

import (
	"context"
	"database/sql"
	"encoding/json"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateQueue creates a new queue row
func CreateQueue(ctx context.Context, db *sql.DB, q *QueueDTO) (int64, error) {
	// insert into queue
	var query = `INSERT INTO queue (created_at, created_by, identifier_type, identifier, ticket_id, status, event_name, source, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	// marshal metadata
	metadata, err := json.Marshal(q.Metadata)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	result, err := db.ExecContext(ctx, query, q.CreatedAt, q.CreatedBy, q.IdentifierType, q.Identifier, q.TicketID, q.Status, q.EventName, q.Source, metadata)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateQueue updates a queue row
func UpdateQueue(ctx context.Context, db *sql.DB, queue *QueueDTO) (int64, error) {
	// update queue
	var query = `UPDATE queue SET status = ?, updated_at = ?, updated_by = ?, metadata = ? WHERE id = ?`

	// marshal metadata
	metadata, err := json.Marshal(queue.Metadata)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	result, err := db.ExecContext(ctx, query, queue.Status, queue.UpdatedAt, queue.UpdatedBy, metadata, queue.ID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rowsAffected == 0 {
		return 0, errorwrapper.Error(apiError.ResourceNotFound, "queue not found")
	}

	return 0, nil
}

// GetQueue ...
func GetQueue(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]*QueueDTO, error) {
	var query = "SELECT id, created_at, updated_at, created_by, updated_by, identifier_type, identifier, status, event_name, source, metadata, ticket_id FROM queue"
	query, args := storage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var queues []*QueueDTO
	for rows.Next() {
		var queue QueueDTO
		var tmp []byte
		scanErr := rows.Scan(&queue.ID,
			&queue.CreatedAt,
			&queue.UpdatedAt,
			&queue.CreatedBy,
			&queue.UpdatedBy,
			&queue.IdentifierType,
			&queue.Identifier,
			&queue.Status,
			&queue.EventName,
			&queue.Source,
			&tmp,
			&queue.TicketID)

		if tmp != nil {
			var metadata map[string]interface{}
			errUnmarshal := json.Unmarshal(tmp, &metadata)
			if errUnmarshal != nil {
				return nil, errorwrapper.Error(apiError.InternalServerError, errUnmarshal.Error())
			}
			queue.Metadata = metadata
		}

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		queues = append(queues, &queue)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return queues, nil
}

// GetQueueByIdentifierAndEvent ...
func GetQueueByIdentifierAndEvent(ctx context.Context, db *sql.DB, identifier string, eventName string) (*QueueDTO, error) {
	var query = `SELECT id, created_at, created_by, updated_at, updated_by, identifier_type, status, event_name, source, metadata FROM queue WHERE identifier = ? AND event_name = ? ORDER BY id DESC`

	var queue QueueDTO
	err := db.QueryRowContext(ctx, query, identifier, eventName).Scan(&queue.ID, queue.CreatedAt, queue.CreatedBy, queue.UpdatedAt, queue.UpdatedBy, queue.IdentifierType, queue.Status, queue.EventName, queue.Source, queue.Metadata)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &queue, nil
}
