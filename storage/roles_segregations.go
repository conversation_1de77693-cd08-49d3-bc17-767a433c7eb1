package storage

import (
	"context"
	"database/sql"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// InsertRolesSegregations ...
func InsertRolesSegregations(ctx context.Context, db *sql.DB, rs *RoleSegregationDTO) (int64, error) {
	var query = `INSERT IGNORE INTO roles_segregations (role_id, segregation_id) VALUES (?, ?)`
	fmt.Printf("Executing query: %s with args: %d, %d\n", query, rs.RoleID, rs.SegregationID)

	result, err := db.ExecContext(ctx, query, rs.RoleID, rs.SegregationID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// DeleteRoleSegregation ...
func DeleteRoleSegregation(ctx context.Context, db *sql.DB, rs *RoleSegregationDTO) error {
	var query = `DELETE FROM roles_segregations
	WHERE role_id = ? and segregation_id = ?`

	_, err := db.ExecContext(ctx, query, rs.RoleID, rs.SegregationID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}
