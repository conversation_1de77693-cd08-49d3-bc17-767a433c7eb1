package storage

import (
	"context"
	"database/sql"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// GetSegregationByRole retrieves field segregation by field key
func GetSegregationByRole(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]*SegregationDTO, error) {
	var query = `
		SELECT DISTINCT s.id, s.name, s.keyword, s.type, s.parent_segregation_id, s.order
		FROM segregations s
		LEFT JOIN roles_segregations rs ON s.id = rs.segregation_id
	`

	query, args := storage.BuildQuery(query, conditions...)

	var segregations []*SegregationDTO
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	for rows.Next() {
		var segregation SegregationDTO
		err = rows.Scan(
			&segregation.ID,
			&segregation.Name,
			&segregation.Key,
			&segregation.Type,
			&segregation.ParentSegregationID,
			&segregation.Order,
		)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		segregations = append(segregations, &segregation)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return segregations, nil
}

// GetSegregationDetails retrieves segregation details based on key
func GetSegregationDetails(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (*SegregationDTO, error) {
	var query = `
		SELECT s.id, s.name, s.keyword, s.parent_segregation_id, s.order, s.level, s.status, s.type
		FROM segregations s
	`

	query, args := storage.BuildQuery(query, conditions...)

	var segregation SegregationDTO
	err := db.QueryRowContext(ctx, query, args...).Scan(
		&segregation.ID,
		&segregation.Name,
		&segregation.Key,
		&segregation.ParentSegregationID,
		&segregation.Order,
		&segregation.Level,
		&segregation.Status,
		&segregation.Type,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return &segregation, nil
}

// GetRoleSegregationByParentID retrieves segregation by its parent
//
// nolint: dupl
func GetRoleSegregationByParentID(ctx context.Context, db *sql.DB, roleID int64, conditions []storage.QueryCondition) ([]*DataSegregationListDTO, error) {
	var query = `
	SELECT 
		s.id, 
		s.name, 
		s.parent_segregation_id, 
		CASE
			WHEN exists (SELECT 1 from segregations WHERE parent_segregation_id = s.id) THEN TRUE
			ELSE FALSE
		END as has_child,
	    CASE 
	    	WHEN rs.id IS NULL THEN 0
			ELSE 1
		END as status
	FROM segregations s
	LEFT JOIN roles_segregations rs ON rs.segregation_id = s.id AND rs.role_id = ?
	`

	args := []any{roleID}
	query, additionalArgs := storage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var data []*DataSegregationListDTO
	for rows.Next() {
		var segregation DataSegregationListDTO
		scanErr := rows.Scan(&segregation.ID,
			&segregation.Name,
			&segregation.ParentSegregationID,
			&segregation.HasChild,
			&segregation.Status)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		data = append(data, &segregation)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return data, nil
}

// GetRoleSegregationBySearch to get role segregation by search the name key
//
// nolint: dupl
func GetRoleSegregationBySearch(ctx context.Context, db *sql.DB, roleID int64, conditions []storage.QueryCondition) ([]*DataSegregationListDTO, error) {
	var query = `
	WITH segregation_child AS (
		SELECT s.id, s.name, s.parent_segregation_id FROM segregations s
		WHERE s.status = 1 AND NOT EXISTS (SELECT 1 from segregations s2 where s2.parent_segregation_id = s.id)
	)
	SELECT 
		s.id, 
		s.name, 
		s.parent_segregation_id, 
		s2.name as parent_name,
		CASE 
			WHEN rs.id IS NULL THEN 0
			ELSE 1
		END as status
	from segregation_child s
	LEFT JOIN segregations s2 ON s2.id = s.parent_segregation_id
	LEFT JOIN roles_segregations rs ON rs.segregation_id = s.id AND rs.role_id = ?
	`

	args := []any{roleID}
	query, additionalArgs := storage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)
	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var data []*DataSegregationListDTO
	for rows.Next() {
		var segregation DataSegregationListDTO
		scanErr := rows.Scan(&segregation.ID,
			&segregation.Name,
			&segregation.ParentSegregationID,
			&segregation.ParentName,
			&segregation.Status)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		data = append(data, &segregation)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return data, nil
}

// CheckEligibilitySegregationWithParent ...
func CheckEligibilitySegregationWithParent(ctx context.Context, db *sql.DB, isActivate bool, segregationID int64, roleID int64) ([]*DataSegregationEligibility, error) {
	query, args := buildQueryForCheckEligibilityByParent(isActivate, segregationID, roleID)
	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var data []*DataSegregationEligibility
	for rows.Next() {
		var segregation DataSegregationEligibility
		scanErr := rows.Scan(&segregation.ID,
			&segregation.ParentSegregationID,
			&segregation.Name,
			&segregation.EligibleUpdate)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		data = append(data, &segregation)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return data, nil
}

func buildQueryForCheckEligibilityByParent(isActive bool, segregationID, roleID int64) (string, []any) {
	baseQuery := `
    WITH RECURSIVE parent_hierarchy AS (
        SELECT s.id, s.parent_segregation_id, s.name
        FROM segregations s
        WHERE s.id = ?
        UNION ALL
        SELECT s.id, s.parent_segregation_id, s.name
        FROM segregations s
        JOIN parent_hierarchy ph ON s.id = ph.parent_segregation_id
    )`
	args := []any{segregationID}
	var query string
	if isActive {
		query = baseQuery + `
        SELECT 
            ph.id,
            ph.parent_segregation_id,
            ph.name,
            NOT EXISTS (
                SELECT 1 FROM roles_segregations rs 
                WHERE rs.role_id = ? 
                AND rs.segregation_id = ph.id
            ) as eligible_update
        FROM parent_hierarchy ph`
		args = append(args, roleID)
	} else {
		query = baseQuery + `,
		role_access AS (
			select rs.segregation_id, rs.role_id, s.parent_segregation_id
			from roles_segregations rs 
			INNER JOIN segregations s ON rs.segregation_id = s.id
			WHERE rs.role_id = ? AND rs.segregation_id NOT IN (select id from parent_hierarchy)
		)
        SELECT 
            ph.id,
            ph.parent_segregation_id,
            ph.name,
            CASE 
				WHEN NOT EXISTS (
					SELECT 1 FROM roles_segregations rs 
					WHERE rs.role_id = ? AND rs.segregation_id = ph.id
				) THEN FALSE
				WHEN EXISTS (
					SELECT 1 from role_access ra
					WHERE ra.parent_segregation_id = ph.id
				) THEN FALSE
				ELSE TRUE
			END as eligible_update
        FROM parent_hierarchy ph`
		args = append(args, roleID, roleID)
	}
	return query, args
}

// CheckEligibilityUpdateSegregation ...
func CheckEligibilityUpdateSegregation(ctx context.Context, db *sql.DB, roleID int64, segIDs []int64, isActivate bool) ([]*DataSegregationEligibility, error) {
	var query = `
	SELECT s.id,
		s.parent_segregation_id,
		s.name,
		CASE
			WHEN rs.id %s THEN TRUE
			ELSE FALSE
		END as eligible_update
		FROM segregations s 
		LEFT JOIN roles_segregations rs 
		ON s.id = rs.segregation_id AND rs.role_id = ?`

	var condition string
	if isActivate {
		condition = "IS NULL"
	} else {
		condition = "IS NOT NULL"
	}

	query = fmt.Sprintf(query, condition)

	args := []any{roleID}
	ids := make([]any, 0)
	for _, id := range segIDs {
		ids = append(ids, id)
	}
	filters := []storage.QueryCondition{storage.In("s.id", ids...)}
	query, additionalArgs := storage.BuildQuery(query, filters...)
	args = append(args, additionalArgs...)
	fmt.Println(args)
	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var data []*DataSegregationEligibility
	for rows.Next() {
		var segregation DataSegregationEligibility
		scanErr := rows.Scan(&segregation.ID,
			&segregation.ParentSegregationID,
			&segregation.Name,
			&segregation.EligibleUpdate)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		data = append(data, &segregation)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return data, nil
}
