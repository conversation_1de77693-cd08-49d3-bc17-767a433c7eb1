package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// GetStatuses ...
//
// nolint: errcheck
func GetStatuses(ctx context.Context, db *sql.DB) ([]*TicketStatusDTO, error) {
	query := `SELECT id, name, created_at, updated_at, created_by, updated_by FROM ticket_status`
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	statuses := []*TicketStatusDTO{}
	for rows.Next() {
		status := &TicketStatusDTO{}
		err := rows.Scan(&status.ID, &status.Name, &status.CreatedAt, &status.UpdatedAt, &status.CreatedBy, &status.UpdatedBy)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
			}
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		statuses = append(statuses, status)
	}

	return statuses, nil
}

// CreateStatus add status master data
func CreateStatus(ctx context.Context, db *sql.DB, status *TicketStatusDTO) (int64, error) {
	query := `INSERT INTO ticket_status (name, created_at, created_by, updated_at, updated_by) VALUES (?, ?, ?, ?, ?)`
	res, err := db.ExecContext(ctx, query, status.Name, status.CreatedAt, status.CreatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	id, err := res.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return id, nil
}

// UpdateStatus update status master data
func UpdateStatus(ctx context.Context, db *sql.DB, status *TicketStatusDTO) error {
	query := `UPDATE ticket_status SET name = ?, updated_at = ?, updated_by = ? WHERE id = ?`
	_, err := db.ExecContext(ctx, query, status.Name, status.UpdatedAt, status.UpdatedBy, status.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return nil
}
