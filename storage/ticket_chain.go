package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// GetTicketChainByActionNameAndElementID ...
func GetTicketChainByActionNameAndElementID(ctx context.Context, db *sql.DB, actionName string, elementID int64) (*TicketChainDTO, error) {
	var query = `
		SELECT id, created_at, created_by, updated_at, updated_by, current_status_id, next_status_id, element_id, action_name, bitwise_required
		FROM ticket_chain 
		WHERE action_name = ? AND element_id = ?
		LIMIT 1
	`

	var tc TicketChainDTO
	err := db.QueryRowContext(ctx, query, actionName, elementID).Scan(&tc.ID, &tc.CreatedAt, &tc.CreatedBy, &tc.UpdatedAt, &tc.UpdatedBy, &tc.CurrentStatusID, &tc.NextStatusID, &tc.ElementID, &tc.ActionName, &tc.BitwiseRequired)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return &tc, nil
}

// GetTicketChainByElementIDMap returns a map of ticket chain by element id with action name as key
func GetTicketChainByElementIDMap(ctx context.Context, db *sql.DB, elementID int64) (map[string]*TicketChainDTO, error) {
	chains, err := GetTicketChainByElementID(ctx, db, elementID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	var chainMap = make(map[string]*TicketChainDTO)

	// FIXME: make sure that no double action name
	for _, chain := range chains {
		chainMap[chain.ActionName] = chain
	}

	return chainMap, nil
}

// GetTicketChainByElementIDAndCurrentStatusID ...
//
// nolint: errcheck
func GetTicketChainByElementIDAndCurrentStatusID(ctx context.Context, db *sql.DB, elementID int64, currentStatusID int64) ([]*TicketChainDTO, error) {
	var query = `
		SELECT id, created_at, created_by, updated_at, updated_by, current_status_id, next_status_id, element_id, action_name, bitwise_required
		FROM ticket_chain 
		WHERE element_id = ? AND current_status_id = ?
	`

	rows, err := db.QueryContext(ctx, query, elementID, currentStatusID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var tcs []*TicketChainDTO
	for rows.Next() {
		var tc TicketChainDTO
		err = rows.Scan(&tc.ID, &tc.CreatedAt, &tc.CreatedBy, &tc.UpdatedAt, &tc.UpdatedBy, &tc.CurrentStatusID, &tc.NextStatusID, &tc.ElementID, &tc.ActionName, &tc.BitwiseRequired)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		tcs = append(tcs, &tc)
	}

	return tcs, nil
}

// GetTicketChainBitwiseRequired ...
func GetTicketChainBitwiseRequired(ctx context.Context, db *sql.DB, ticketID int64, nextStatusID int64) (int64, error) {
	var query = `
	select tc.bitwise_required from ticket_chain tc 
	inner join tickets t on tc.element_id = t.element_id and t.ticket_status_id = tc.current_status_id
	where t.id = ?
	and tc.next_status_id = ?
	limit 1;
	`

	var bitwise int64
	err := db.QueryRowContext(ctx, query, ticketID, nextStatusID).Scan(&bitwise)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, errorwrapper.Error(apiError.ResourceNotFound, "ticket chain not found")
		}
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return bitwise, nil
}

// CreateTicketChain ...
func CreateTicketChain(ctx context.Context, db *sql.DB, ticketChainDTO *TicketChainDTO) (int64, error) {
	var query = `
		INSERT INTO ticket_chain (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	res, err := db.ExecContext(ctx, query, ticketChainDTO.CreatedAt, ticketChainDTO.CreatedBy, ticketChainDTO.CurrentStatusID, ticketChainDTO.NextStatusID, ticketChainDTO.ElementID, ticketChainDTO.ActionName, ticketChainDTO.BitwiseRequired)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := res.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateTicketChain ...
func UpdateTicketChain(ctx context.Context, db *sql.DB, ticketChainDTO *TicketChainDTO) error {
	var query = `
		UPDATE ticket_chain
		SET updated_at = ?, updated_by = ?, current_status_id = ?, next_status_id = ?, element_id = ?, action_name = ?, bitwise_required = ?
		WHERE id = ?
	`

	_, err := db.ExecContext(ctx, query, ticketChainDTO.UpdatedAt, ticketChainDTO.UpdatedBy, ticketChainDTO.CurrentStatusID, ticketChainDTO.NextStatusID, ticketChainDTO.ElementID, ticketChainDTO.ActionName, ticketChainDTO.BitwiseRequired, ticketChainDTO.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// DeleteTicketChain ...
func DeleteTicketChain(ctx context.Context, db *sql.DB, id int64) error {
	var query = `
		DELETE FROM ticket_chain
		WHERE id = ?
	`

	_, err := db.ExecContext(ctx, query, id)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetTicketChainByElementID ...
//
// nolint: errcheck
func GetTicketChainByElementID(ctx context.Context, db *sql.DB, elementID int64) ([]*TicketChainDTO, error) {
	var query = `
		SELECT id, created_at, created_by, updated_at, updated_by, current_status_id, next_status_id, element_id, action_name, bitwise_required
		FROM ticket_chain 
		WHERE element_id = ?
	`

	rows, err := db.QueryContext(ctx, query, elementID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var tcs []*TicketChainDTO
	for rows.Next() {
		var tc TicketChainDTO
		err = rows.Scan(&tc.ID, &tc.CreatedAt, &tc.CreatedBy, &tc.UpdatedAt, &tc.UpdatedBy, &tc.CurrentStatusID, &tc.NextStatusID, &tc.ElementID, &tc.ActionName, &tc.BitwiseRequired)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		tcs = append(tcs, &tc)
	}

	return tcs, nil
}
