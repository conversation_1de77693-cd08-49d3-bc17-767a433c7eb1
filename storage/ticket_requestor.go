package storage

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// GetTicketRequestorList get a list of ticket requestors based on condition
func GetTicketRequestorList(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]TicketRequestorDTO, error) {
	baseQuery := `SELECT id, is_active, created_by, created_at, updated_by, updated_at, name FROM ticket_requestor`
	query, args := storage.BuildQuery(baseQuery, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "ticket requestor not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var ticketRequestors []TicketRequestorDTO
	for rows.Next() {
		var t TicketRequestorDTO
		err := rows.Scan(
			&t.ID, &t.IsActive, &t.CreatedBy,
			&t.CreatedAt, &t.UpdatedBy, &t.UpdatedAt, &t.Name,
		)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		ticketRequestors = append(ticketRequestors, t)
	}

	return ticketRequestors, nil
}
