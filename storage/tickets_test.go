package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

func TestCreateTicket(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()

	mock.ExpectBegin()
	tx, _ := db.Begin()
	defer tx.Rollback()
	ctx := context.Background()

	testCases := []struct {
		name          string
		dto           *TicketDTO
		mockSetup     func()
		expectedError bool
		expectedID    int64
	}{
		{
			name: "success - create ticket with all fields",
			dto: &TicketDTO{
				ElementID:      1,
				PriorityID:     2,
				DeadlineTime:   sql.NullTime{Time: time.Now().Add(24 * time.Hour), Valid: true},
				TicketStatusID: 3,
				Data: &api.TicketData{
					Payload:     nil,
					Description: "",
					Documents:   nil,
					Capture:     nil,
					Preview:     nil,
				},
				CreatedAt:           sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedAt:           sql.NullTime{Time: time.Now(), Valid: true},
				CreatedBy:           sql.NullInt64{Int64: 1, Valid: true},
				UpdatedBy:           sql.NullInt64{Int64: 1, Valid: true},
				Source:              "test",
				AssigneeUserID:      sql.NullInt64{Int64: 2, Valid: true},
				CaseCategory:        sql.NullString{String: "caseCategory", Valid: true},
				CaseSubcategory:     sql.NullString{String: "caseCategory", Valid: true},
				CustomerSegmentID:   sql.NullInt64{Int64: 1, Valid: true},
				DomainID:            sql.NullString{String: "MYxxx", Valid: true},
				TicketRequestorID:   sql.NullInt64{Int64: 1, Valid: true},
				Channel:             sql.NullString{String: "chat", Valid: true},
				TicketCloseDatetime: sql.NullTime{},
			},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`INSERT INTO tickets (element_id, priority_id, deadline_time, ticket_status_id, data, created_at, updated_at, created_by, updated_by, source, assignee_user_id, case_category, case_subcategory, ticket_close_datetime, domain_id, channel, ticket_requestor_id, customer_segment_id)
					VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`)).
					WithArgs(int64(1), int64(2), sqlmock.AnyArg(), int64(3), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), int64(1), int64(1), "test", int64(2), "caseCategory", "caseCategory", sql.NullTime{}, "MYxxx", "chat", int64(1), int64(1)).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			expectedError: false,
			expectedID:    1,
		},
		{
			name: "success - create ticket with minimal required fields",
			dto: &TicketDTO{
				ElementID:      1,
				PriorityID:     2,
				DeadlineTime:   sql.NullTime{Time: time.Now().Add(24 * time.Hour), Valid: true},
				TicketStatusID: 3,
				Data: &api.TicketData{
					Payload:     nil,
					Description: "",
					Documents:   nil,
					Capture:     nil,
					Preview:     nil,
				},
				CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				CreatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				UpdatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				Source:         "test",
				AssigneeUserID: sql.NullInt64{Int64: 2, Valid: true},
			},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`INSERT INTO tickets (element_id, priority_id, deadline_time, ticket_status_id, data, created_at, updated_at, created_by, updated_by, source, assignee_user_id, case_category, case_subcategory, ticket_close_datetime, domain_id, channel, ticket_requestor_id, customer_segment_id)
					VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`)).
					WithArgs(int64(1), int64(2), sqlmock.AnyArg(), int64(3), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), int64(1), int64(1), "test", int64(2), sql.NullString{}, sql.NullString{}, sql.NullTime{}, sql.NullString{}, sql.NullString{}, sql.NullInt64{}, sql.NullInt64{}).
					WillReturnResult(sqlmock.NewResult(2, 1))
			},
			expectedError: false,
			expectedID:    2,
		},
		{
			name: "error - database error",
			dto: &TicketDTO{
				ElementID:      1,
				PriorityID:     2,
				DeadlineTime:   sql.NullTime{Time: time.Now().Add(24 * time.Hour), Valid: true},
				TicketStatusID: 3,
				Data: &api.TicketData{
					Payload:     nil,
					Description: "",
					Documents:   nil,
					Capture:     nil,
					Preview:     nil,
				},
				CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				CreatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				UpdatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				Source:         "test",
				AssigneeUserID: sql.NullInt64{Int64: 2, Valid: true},
			},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`INSERT INTO tickets (element_id, priority_id, deadline_time, ticket_status_id, data, created_at, updated_at, created_by, updated_by, source, assignee_user_id, case_category, case_subcategory, ticket_close_datetime, domain_id, channel, ticket_requestor_id, customer_segment_id)
					VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`)).
					WithArgs(int64(1), int64(2), sqlmock.AnyArg(), int64(3), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), int64(1), int64(1), "test", int64(2), sql.NullString{}, sql.NullString{}, sql.NullTime{}, sql.NullString{}, sql.NullString{}, sql.NullInt64{}, sql.NullInt64{}).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
			expectedID:    0,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			id, err := CreateTicket(ctx, tx, tc.dto)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tc.expectedID, id)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestUpdateTicket(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	mock.ExpectBegin()
	tx, _ := db.Begin()
	defer tx.Rollback()
	ctx := context.Background()

	testCases := []struct {
		name          string
		dto           *TicketDTO
		mockSetup     func()
		expectedError bool
	}{
		{
			name: "success - update ticket",
			dto: &TicketDTO{
				ID:             1,
				TicketStatusID: 2,
				Data: &api.TicketData{
					Payload:     nil,
					Description: "",
					Documents:   nil,
					Capture:     nil,
					Preview:     nil,
				},
				UpdatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				AssigneeUserID: sql.NullInt64{Int64: 3, Valid: true},
			},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets 
					SET data = ?, ticket_status_id = ?, updated_at = ?, updated_by = ?, assignee_user_id = ?
					WHERE id = ?`)).
					WithArgs(sqlmock.AnyArg(), int64(2), sqlmock.AnyArg(), int64(1), int64(3), int64(1)).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			expectedError: false,
		},
		{
			name: "error - database error",
			dto: &TicketDTO{
				ID:             1,
				TicketStatusID: 2,
				Data: &api.TicketData{
					Payload:     nil,
					Description: "",
					Documents:   nil,
					Capture:     nil,
					Preview:     nil,
				},
				UpdatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
				UpdatedBy:      sql.NullInt64{Int64: 1, Valid: true},
				AssigneeUserID: sql.NullInt64{Int64: 3, Valid: true},
			},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets 
					SET data = ?, ticket_status_id = ?, updated_at = ?, updated_by = ?, assignee_user_id = ?
					WHERE id = ?`)).
					WithArgs(sqlmock.AnyArg(), int64(2), sqlmock.AnyArg(), int64(1), int64(3), int64(1)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			err := UpdateTicket(ctx, tx, tc.dto)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestUpdateTicketStatus(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	mock.ExpectBegin()
	tx, _ := db.Begin()
	defer tx.Rollback()
	ctx := context.Background()

	testCases := []struct {
		name                string
		ticketID            int64
		statusID            int64
		updatedBy           int64
		ticketCloseDatetime sql.NullTime
		mockSetup           func()
		expectedError       bool
	}{
		{
			name:                "success - update ticket status",
			ticketID:            1,
			statusID:            2,
			updatedBy:           3,
			ticketCloseDatetime: sql.NullTime{},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets 
					SET ticket_status_id = ?, updated_at = NOW(), updated_by = ?, ticket_close_datetime = ?
					WHERE id = ?`)).
					WithArgs(int64(2), int64(3), sql.NullTime{}, int64(1)).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			expectedError: false,
		},
		{
			name:      "success - update ticket status and close datetime",
			ticketID:  1,
			statusID:  2,
			updatedBy: 3,
			ticketCloseDatetime: func() sql.NullTime {
				t, err := time.Parse("2006-01-02 15:04:05", "2023-06-06 14:06:15")
				if err != nil {
					panic(err)
				}
				return sql.NullTime{Time: t, Valid: true}
			}(),
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets 
					SET ticket_status_id = ?, updated_at = NOW(), updated_by = ?, ticket_close_datetime = ?
					WHERE id = ?`)).
					WithArgs(int64(2), int64(3), func() sql.NullTime {
						t, err := time.Parse("2006-01-02 15:04:05", "2023-06-06 14:06:15")
						if err != nil {
							panic(err)
						}
						return sql.NullTime{Time: t, Valid: true}
					}(), int64(1)).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			expectedError: false,
		},
		{
			name:                "error - database error",
			ticketID:            1,
			statusID:            2,
			updatedBy:           3,
			ticketCloseDatetime: sql.NullTime{},
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets 
					SET ticket_status_id = ?, updated_at = NOW(), updated_by = ?, ticket_close_datetime = ?
					WHERE id = ?`)).
					WithArgs(int64(2), int64(3), sql.NullTime{}, int64(1)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			err := UpdateTicketStatus(ctx, tx, tc.ticketID, tc.statusID, tc.updatedBy, tc.ticketCloseDatetime)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestUpdateTicketAssignee(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	ctx := context.Background()

	testCases := []struct {
		name          string
		ticketID      int64
		assigneeID    int64
		mockSetup     func()
		expectedError bool
	}{
		{
			name:       "success - set assignee to null",
			ticketID:   1,
			assigneeID: 0,
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets SET assignee_user_id = NULL, updated_at = NOW() WHERE id = ?`)).
					WithArgs(int64(1)).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			expectedError: false,
		},
		{
			name:       "success - set assignee to value",
			ticketID:   1,
			assigneeID: 2,
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets SET assignee_user_id = ?, updated_at = NOW() WHERE id = ?`)).
					WithArgs(int64(2), int64(1)).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			expectedError: false,
		},
		{
			name:       "error - database error",
			ticketID:   1,
			assigneeID: 2,
			mockSetup: func() {
				mock.ExpectExec(regexp.QuoteMeta(
					`UPDATE tickets SET assignee_user_id = ?, updated_at = NOW() WHERE id = ?`)).
					WithArgs(int64(2), int64(1)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			err := UpdateTicketAssignee(ctx, db, tc.ticketID, tc.assigneeID)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestGetTicketByID(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	ctx := context.Background()

	data := map[string]interface{}{"foo": "bar"}
	dataBytes, _ := json.Marshal(data)
	now := time.Now()

	testCases := []struct {
		name          string
		ticketID      int64
		mockSetup     func()
		expectedError bool
		expectedNil   bool
	}{
		{
			name:     "success - ticket found",
			ticketID: 1,
			mockSetup: func() {
				rows := sqlmock.NewRows([]string{
					"id", "element_id", "priority_id", "deadline_time", "ticket_status_id", "data", "created_at", "updated_at", "created_by", "updated_by", "source", "assignee_user_id", "assignee_user_name",
					"case_category", "case_subcategory", "ticket_close_datetime", "domain_id", "channel", "ticket_requestor_id", "ticket_requestor_name", "customer_segment_id", "customer_segment_name",
				}).AddRow(
					1, 2, 3, now, 4, string(dataBytes), now, now, 5, 6, "source", 7, "assignee",
					"cat", "subcat", now, "domain", "channel", 8, "requestor", 9, "segment",
				)
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, t.source, t.assignee_user_id, u.name as assignee_user_name,
				       t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel, t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name
					FROM tickets t
					LEFT JOIN users u ON t.assignee_user_id = u.id
					LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
					LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id
					WHERE t.id = ?`)).
					WithArgs(int64(1)).
					WillReturnRows(rows)
			},
			expectedError: false,
			expectedNil:   false,
		},
		{
			name:     "ticket not found",
			ticketID: 1,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, t.source, t.assignee_user_id, u.name as assignee_user_name,
				       t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel, t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name
					FROM tickets t
					LEFT JOIN users u ON t.assignee_user_id = u.id
					LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
					LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id
					WHERE t.id = ?`)).
					WithArgs(int64(1)).
					WillReturnError(sql.ErrNoRows)
			},
			expectedError: true,
			expectedNil:   true,
		},
		{
			name:     "database error",
			ticketID: 1,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, t.source, t.assignee_user_id, u.name as assignee_user_name,
				       t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel, t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name
					FROM tickets t
					LEFT JOIN users u ON t.assignee_user_id = u.id
					LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
					LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id
					WHERE t.id = ?`)).
					WithArgs(int64(1)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
			expectedNil:   true,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			result, err := GetTicketByID(ctx, db, tc.ticketID)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tc.expectedNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tc.ticketID, result.ID)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestGetTicketList(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	ctx := context.Background()

	data := map[string]interface{}{"foo": "bar"}
	dataBytes, _ := json.Marshal(data)
	now := time.Now()

	testCases := []struct {
		name          string
		conditions    []storage.QueryCondition
		mockSetup     func()
		expectedError bool
		expectedLen   int
	}{
		{
			name:       "success - get ticket list",
			conditions: nil,
			mockSetup: func() {
				rows := sqlmock.NewRows([]string{
					"id", "element_id", "priority_id", "deadline_time", "ticket_status_id", "data", "created_at", "updated_at", "created_by", "updated_by", "source", "assignee_user_name", "element_name", "priority_name",
					"case_category", "case_subcategory", "ticket_close_datetime", "domain_id", "channel", "ticket_requestor_id", "ticket_requestor_name", "customer_segment_id", "customer_segment_name",
				}).AddRow(
					1, 2, 3, now, 4, string(dataBytes), now, now, 5, 6, "source", "assignee", "element", "priority",
					"cat", "subcat", now, "domain", "channel", 8, "requestor", 9, "segment",
				)
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, 
					       t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, 
					       t.source, u.name as assignee_user_name, e.name as element_name,
					       p.name as priority_name, t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel,
					       t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name
					FROM tickets t
					LEFT JOIN users u ON t.assignee_user_id = u.id
					INNER JOIN elements e ON t.element_id = e.id
					INNER JOIN ticket_priority p ON t.priority_id = p.id
					LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
					LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id`)).
					WillReturnRows(rows)
			},
			expectedError: false,
			expectedLen:   1,
		},
		{
			name:       "error - database error",
			conditions: nil,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`
					SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, 
					       t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, 
					       t.source, u.name as assignee_user_name, e.name as element_name,
					       p.name as priority_name, t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel,
					       t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name
					FROM tickets t
					LEFT JOIN users u ON t.assignee_user_id = u.id
					INNER JOIN elements e ON t.element_id = e.id
					INNER JOIN ticket_priority p ON t.priority_id = p.id
					LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
					LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id`)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
			expectedLen:   0,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			result, err := GetTicketList(ctx, db, tc.conditions)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Len(t, result, tc.expectedLen)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestGetTicketCount(t *testing.T) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	defer db.Close()
	ctx := context.Background()

	testCases := []struct {
		name          string
		conditions    []storage.QueryCondition
		mockSetup     func()
		expectedError bool
		expectedCount int64
	}{
		{
			name:       "success - get ticket count",
			conditions: nil,
			mockSetup: func() {
				rows := sqlmock.NewRows([]string{"COUNT(*)"}).AddRow(5)
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT COUNT(*) FROM tickets t
				INNER JOIN elements e ON t.element_id = e.id`)).
					WillReturnRows(rows)
			},
			expectedError: false,
			expectedCount: 5,
		},
		{
			name:       "error - database error",
			conditions: nil,
			mockSetup: func() {
				mock.ExpectQuery(regexp.QuoteMeta(`SELECT COUNT(*) FROM tickets t
				INNER JOIN elements e ON t.element_id = e.id`)).
					WillReturnError(sql.ErrConnDone)
			},
			expectedError: true,
			expectedCount: 0,
		},
	}

	for _, tc := range testCases {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSetup()

			count, err := GetTicketCount(ctx, db, tc.conditions)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tc.expectedCount, count)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
