package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerJournal "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerJournalMock "gitlab.super-id.net/bersama/corex/customer-journal/api/mock"
	customerJourneyExperiencePreference "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	customerJourneyExperiencePreferenceMock "gitlab.super-id.net/bersama/corex/customer-journey-experience/api/mock"
	customerExperience "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	customerExperienceMock "gitlab.super-id.net/bersama/onboarding/customer-experience/api/mock"
	customerMasterMock "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search Event History", func() {
	var (
		db     *sql.DB
		mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockCustomerJournal = &customerJournalMock.CustomerJournal{}
		mockCustomerJourneyExperiencePreference = &customerJourneyExperiencePreferenceMock.PreferenceCenter{}

		config := &logic.MockProcessConfig{
			CustomerMaster:                      mockCustomerMaster,
			CustomerExperience:                  mockCustomerExperience,
			CustomerJournal:                     mockCustomerJournal,
			CustomerJourneyExperiencePreference: mockCustomerJourneyExperiencePreference,
			AppConfig:                           service.AppConfig,
		}
		logic.MockInitLogic(config)

		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch Event History", func() {
		When("user has valid permissions and getting activity log", func() {
			It("should successfully return activity log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "activityLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("activityLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Activity Log", "activityLog", nil, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Triggered Date", "activityLogTriggeredDate", "oneliner_datetime", 1, 1).
						AddRow(2, "Event", "activityLogEvent", "oneliner", 1, 2).
						AddRow(3, "Activity", "activityLogActivity", "oneliner", 1, 3).
						AddRow(4, "Before Value", "activityLogBeforeValue", "oneliner", 1, 4).
						AddRow(5, "After Value", "activityLogAfterValue", "oneliner", 1, 5).
						AddRow(6, "Trigger", "activityLogTrigger", "oneliner", 1, 6).
						AddRow(7, "Status", "activityLogStatus", "oneliner", 1, 7).
						AddRow(8, "Failed Reason", "activityLogFailedReason", "oneliner", 1, 8))

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"activityType": "UPDATE_EMAIL",
								"actionType": "EMAIL_UPDATE",
								"beforeValue": "<EMAIL>",
								"afterValue": "<EMAIL>",
								"trigger": "APP",
								"status": "COMPLETED",
								"error": "[ERR] Some error message appears here"
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"activityType": "UPDATE_PROFILE",
								"actionType": "ADDRESS_UPDATE",
								"beforeValue": "Old Address, Jakarta",
								"afterValue": "New Address, Jakarta",
								"trigger": "APP",
								"status": "COMPLETED",
								"error": ""
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeActivity,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "activityLog",
						"type": "table",
						"label": "Activity Log",
						"children": [
							{
								"key": "activityLogTriggeredDate",
								"type": "oneliner_datetime",
								"label": "Triggered Date"
							},
							{
								"key": "activityLogEvent",
								"type": "oneliner",
								"label": "Event"
							},
							{
								"key": "activityLogActivity",
								"type": "oneliner",
								"label": "Activity"
							},
							{
								"key": "activityLogBeforeValue",
								"type": "oneliner",
								"label": "Before Value"
							},
							{
								"key": "activityLogAfterValue",
								"type": "oneliner",
								"label": "After Value"
							},
							{
								"key": "activityLogTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "activityLogStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "activityLogFailedReason",
								"type": "oneliner",
								"label": "Failed Reason"
							}
						]
					},
					"data": {
						"activityLog": {
							"data": [
								{
									"activityLogActivity": "EMAIL_UPDATE",
									"activityLogAfterValue": "<EMAIL>",
									"activityLogBeforeValue": "<EMAIL>",
									"activityLogEvent": "UPDATE_EMAIL",
									"activityLogFailedReason": "Some error message appears here",
									"activityLogStatus": "COMPLETED",
									"activityLogTrigger": "APP",
									"activityLogTriggeredDate": "2024-07-05T08:43:16Z"
								},
								{
									"activityLogActivity": "ADDRESS_UPDATE",
									"activityLogAfterValue": "New Address, Jakarta",
									"activityLogBeforeValue": "Old Address, Jakarta",
									"activityLogEvent": "UPDATE_PROFILE",
									"activityLogFailedReason": "",
									"activityLogStatus": "COMPLETED",
									"activityLogTrigger": "APP",
									"activityLogTriggeredDate": "2024-05-08T14:45:13Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"cif": null,
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting MFA log", func() {
			It("should successfully return MFA log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
						"identifierType": "SAFE_ID",
						"key": "mfaLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("mfaLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "MFA Log", "mfaLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "mfaLogTimestamp", "datetime", 1, 1).
						AddRow(2, "MFA Type", "mfaLogMfaType", "oneliner", 1, 2).
						AddRow(3, "Status", "mfaLogStatus", "oneliner", 1, 3).
						AddRow(4, "Cooldown Period", "mfaLogCooldownPeriod", "datetime", 1, 4).
						AddRow(5, "Login Source", "mfaLogLoginSource", "oneliner", 1, 5).
						AddRow(6, "FMLC Failed Reason", "mfaLogFmlcFailedReason", "oneliner", 1, 6).
						AddRow(7, "FMLC Transaction ID", "mfaLogFmlcTransactionId", "oneliner", 1, 7).
						AddRow(8, "FMLC Match Score", "mfaLogFmlcMatchScore", "oneliner", 1, 8).
						AddRow(9, "Control Triggered", "mfaLogControlTriggered", "oneliner", 1, 9).
						AddRow(10, "Selfie Image", "mfaLogSelfieImage", "image", 1, 10).
						AddRow(11, "Compare With Base Selfie", "mfaLogCompareWithBaseSelfie", "sensitiveImage", 1, 11),
					)

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
							"publicID": "ID161101195493"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"pagination": {
						"nextCursorID": "eyJjdXN0b21lcklEIjoiYjY2NDNhYmMtY2QxYy00ZDM1LTk1ZDQtNzBkM2VjZWZmMjdlIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2MDcyMjM5NjU2NzcyIn0=",
						"prevCursorID": "eyJjdXN0b21lcklEIjoiYjY2NDNhYmMtY2QxYy00ZDM1LTk1ZDQtNzBkM2VjZWZmMjdlIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2MDgzMDEwNjA5ODQ1In0="
					},
					"data": [
						{
						"customerID": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
						"eventTimestamp": "1751366083010609845",
						"metadata": {
							"clientType": null,
							"controlTriggered": null,
							"cooldownPeriod": null,
							"deviceInfo": {
							"appVersion": "unknown",
							"deviceBrand": "<no value>",
							"deviceID": "8ff67c39-5247-4d42-9c1d-7deb3c1da017",
							"deviceModel": null,
							"loginSource": "web-akulaku",
							"osName": null,
							"osVersion": "<no value>"
							},
							"facialMatchResult": null,
							"failReason": null,
							"fmlcFaceMatchScore": null,
							"groupID": "046e06cf-7bf3-4894-b94d-045a27ad2c42",
							"latestSelfie": null,
							"livenessResult": null,
							"mfaType": "OTP",
							"phoneNumber": "+62880003001047",
							"status": "success",
							"transactionId": null,
							"trigger": "/v1/login/prompt"
						}
						},
						{
						"customerID": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
						"eventTimestamp": "1751366072239656772",
						"metadata": {
							"clientType": null,
							"controlTriggered": null,
							"cooldownPeriod": null,
							"deviceInfo": {
							"appVersion": "unknown",
							"deviceBrand": "<no value>",
							"deviceID": "8ff67c39-5247-4d42-9c1d-7deb3c1da017",
							"deviceModel": null,
							"loginSource": "web-akulaku",
							"osName": null,
							"osVersion": "<no value>"
							},
							"facialMatchResult": null,
							"failReason": null,
							"fmlcFaceMatchScore": null,
							"groupID": "046e06cf-7bf3-4894-b94d-045a27ad2c42",
							"latestSelfie": null,
							"livenessResult": null,
							"mfaType": "OTP Request",
							"otpResend": false,
							"phoneNumber": "+62880003001047",
							"status": "success",
							"transactionId": null,
							"trigger": "/v1/login/prompt"
						}
						}
					]
					}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
					PageSize:   2,
					Endpoint:   constants.CustomerJournalLogTypeMFA,
				}).Return(mockJournalResponse, nil).Once()

				// mock for GetSelfieImagePresignedURL
				mockCustomerExperience.On("GetSelfieImagePresignedURL", mock.Anything, mock.Anything).Return(&customerExperience.GetSelfieImagePresignedURLResponse{
					Data: "https://example.com/selfie/123456.jpg",
				}, nil).Once()

				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
					"items": [
						{
							"applications": [
								{
									"selfieFile": [
										{
											"URL": "https://example.com/selfie/123456.jpg",
										}
									]
								}
							]
						}
					]
				}`

				var mockCustomerExperienceResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockCustomerExperienceResponse)

				// mock for GetCustomerOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, mock.Anything).Return(mockCustomerExperienceResponse, nil).Times(2)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "mfaLog",
						"type": "table",
						"label": "MFA Log",
						"children": [
							{
								"key": "mfaLogTimestamp",
								"type": "datetime",
								"label": "Timestamp"
							},
							{
								"key": "mfaLogMfaType",
								"type": "oneliner",
								"label": "MFA Type"
							},
							{
								"key": "mfaLogStatus",
								"type": "status",
								"label": "Status"
							},
							{
								"key": "mfaLogCooldownPeriod",
								"type": "datetime",
								"label": "Cooldown Period"
							},
							{
								"key": "mfaLogLoginSource",
								"type": "oneliner",
								"label": "Login Source"
							},
							{
								"key": "mfaLogFmlcFailedReason",
								"type": "oneliner",
								"label": "FMLC Failed Reason"
							},
							{
								"key": "mfaLogFmlcTransactionId",
								"type": "oneliner",
								"label": "FMLC Transaction ID"
							},
							{
								"key": "mfaLogFmlcMatchScore",
								"type": "oneliner",
								"label": "FMLC Match Score"
							},
							{
								"key": "mfaLogControlTriggered",
								"type": "oneliner",
								"label": "Control Triggered"
							},
							{
								"key": "mfaLogSelfieImage",
								"type": "image",
								"label": "Selfie Image"
							},
							{
								"key": "mfaLogBaseSelfie",
								"type": "sensitiveImage",
								"label": "Compare with Base Selfie"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"mfaLog": {
							"data": [
								{
									"mfaLogBaseSelfie": "https://idbank-dev-backend-customer-experience.s3.ap-southeast-3.amazonaws.com/user_uploaded_documents/b6643abc-cd1c-4d35-95d4-70d3eceff27e/applications/4af2d13c-6708-4f67-bf71-1e1b3025f7a3/liveness/4af2d13c-6708-4f67-bf71-1e1b3025f7a3.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIATBBUKB7ZDQYLHOEK%2F20250701%2Fap-southeast-3%2Fs3%2Faws4_request&X-Amz-Date=20250701T112106Z&X-Amz-Expires=1800&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGMaDmFwLXNvdXRoZWFzdC0zIkgwRgIhAIW3NaPv4SbR2mwV%2FCm%2FNDAbhvT6Lr3w9tKTjBIoHGKAAiEAw1HHKFFUjFQbOIZkpHb0Y9sQIeP%2FnQMxjyAH%2BEw1DB0qtAUI1P%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgwyMDg0MTU2MjUyMDIiDN%2BhqHONIlxDjQnXUiqIBfNVdbk3ZctH4H%2FP2tG7Rv%2BTaOb8FVdWeVnLGd7vLjNZXQzBP%2BAxtd6nzBCMYEkhX53adAvKgTTAF2xPRt89iY%2FbL6P5KMSvBnd9JYGRoK9z6oJUOpmptHKfuwxvNKdBtJm1nGxzztULUa5c44RwCc%2BR7XPq%2FmGVV90%2BMBXVkMm4%2FcwexVC1CIe4YBw%2BGeLUVHV1bqqAB0%2F33VtEWHTCIgpdjb2271RMUuuHpR5BU%2BW3vM6hDF7oLcwfK03rHgkVUXA8NeAB3FKq3OQWlpgG%2B55WuBC%2BD3Cq1lk6NggxigKQbPkNIz6jrOZ8OYZTbvNE8vM1LgxUB3UAaguT%2FjqRVNUCPg73XNE2QK1RiMuojbHTR0p8sLROGym6VOOB9SDgmr0tiCxbQ%2FtYQvcI7kJbOHbprj3NHOsLNO2QqulGbTUeXKQcRrmKxZKfYJgcp3dve%2Bu238TvVbPAXI3TYwO2LoiOxK8J9U9Fs%2BaB19yzz6YsiGtpjt3tiusQYO78Sx9Oxg9gSFevUUSe%2FC4ssIC81lnGEPfI4Uy1EliMqpZpmPCKsjJb0SaM%2FlSRoTlsqYRf%2Be%2FuyE2cvOvaz5CFPS2C7gqWbzjdJpXJOMIIm60duGhIA5UvAM0iEgc3rBOf%2Fo%2FJcWrC6mm%2FAs0ZjCzcQyX5ICDwvHDqKez9S1%2FYcP9GYQOdEXuQosfK9JYB0GLPgBO8ORgRvQqBT2EpxMtgWLdy0gYZwEQSBL8R3k%2BkMpi8hPovdP2B1Go3c%2BcgzyoNqS1fJck%2FX1unHMhvhJsWKQACpAsroOXc7pnk8SRPC6cgzd7f7ghRNaKoHEHIiOlyFRN5AVuZmG%2F36zTp3qFELU4OwNgdrtATTCE0cjDd%2Bo7DBjqaAf9b7nAqDyUUt8avZI9fnLTvXavhmfPG1FRzZt6ljlRSDWUozjH9DqtlitA1uU3a4lZ6qKN9%2BH%2BM%2BboJDg5%2Bs1SSHsuorxXT5Ac3SpbMSxuYF1Je58Bduk8TeOpkHVRbxDIJfxOFGY%2BdkKdDKSBgxTsqS%2F6e%2BMg7tCql39SXAhkpp5bEwxFx9Jtytke9gtxJnYpErxLFLHG%2F7wY%3D&X-Amz-SignedHeaders=host&X-Amz-Signature=388b41ebb9bf1c5f55dde004e27cd4e8f18808faa42b0205cd25b8804882d756",
									"mfaLogControlTriggered": null,
									"mfaLogCooldownPeriod": "",
									"mfaLogFmlcFailedReason": "",
									"mfaLogFmlcMatchScore": null,
									"mfaLogFmlcTransactionId": "",
									"mfaLogLoginSource": "web-akulaku",
									"mfaLogMfaType": "OTP - Prompt",
									"mfaLogSelfieImage": "",
									"mfaLogStatus": "success",
									"mfaLogTimestamp": "2025-07-01T17:34:43Z"
								},
								{
									"mfaLogBaseSelfie": "https://idbank-dev-backend-customer-experience.s3.ap-southeast-3.amazonaws.com/user_uploaded_documents/b6643abc-cd1c-4d35-95d4-70d3eceff27e/applications/4af2d13c-6708-4f67-bf71-1e1b3025f7a3/liveness/4af2d13c-6708-4f67-bf71-1e1b3025f7a3.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIATBBUKB7ZDQYLHOEK%2F20250701%2Fap-southeast-3%2Fs3%2Faws4_request&X-Amz-Date=20250701T112106Z&X-Amz-Expires=1800&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGMaDmFwLXNvdXRoZWFzdC0zIkgwRgIhAIW3NaPv4SbR2mwV%2FCm%2FNDAbhvT6Lr3w9tKTjBIoHGKAAiEAw1HHKFFUjFQbOIZkpHb0Y9sQIeP%2FnQMxjyAH%2BEw1DB0qtAUI1P%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgwyMDg0MTU2MjUyMDIiDN%2BhqHONIlxDjQnXUiqIBfNVdbk3ZctH4H%2FP2tG7Rv%2BTaOb8FVdWeVnLGd7vLjNZXQzBP%2BAxtd6nzBCMYEkhX53adAvKgTTAF2xPRt89iY%2FbL6P5KMSvBnd9JYGRoK9z6oJUOpmptHKfuwxvNKdBtJm1nGxzztULUa5c44RwCc%2BR7XPq%2FmGVV90%2BMBXVkMm4%2FcwexVC1CIe4YBw%2BGeLUVHV1bqqAB0%2F33VtEWHTCIgpdjb2271RMUuuHpR5BU%2BW3vM6hDF7oLcwfK03rHgkVUXA8NeAB3FKq3OQWlpgG%2B55WuBC%2BD3Cq1lk6NggxigKQbPkNIz6jrOZ8OYZTbvNE8vM1LgxUB3UAaguT%2FjqRVNUCPg73XNE2QK1RiMuojbHTR0p8sLROGym6VOOB9SDgmr0tiCxbQ%2FtYQvcI7kJbOHbprj3NHOsLNO2QqulGbTUeXKQcRrmKxZKfYJgcp3dve%2Bu238TvVbPAXI3TYwO2LoiOxK8J9U9Fs%2BaB19yzz6YsiGtpjt3tiusQYO78Sx9Oxg9gSFevUUSe%2FC4ssIC81lnGEPfI4Uy1EliMqpZpmPCKsjJb0SaM%2FlSRoTlsqYRf%2Be%2FuyE2cvOvaz5CFPS2C7gqWbzjdJpXJOMIIm60duGhIA5UvAM0iEgc3rBOf%2Fo%2FJcWrC6mm%2FAs0ZjCzcQyX5ICDwvHDqKez9S1%2FYcP9GYQOdEXuQosfK9JYB0GLPgBO8ORgRvQqBT2EpxMtgWLdy0gYZwEQSBL8R3k%2BkMpi8hPovdP2B1Go3c%2BcgzyoNqS1fJck%2FX1unHMhvhJsWKQACpAsroOXc7pnk8SRPC6cgzd7f7ghRNaKoHEHIiOlyFRN5AVuZmG%2F36zTp3qFELU4OwNgdrtATTCE0cjDd%2Bo7DBjqaAf9b7nAqDyUUt8avZI9fnLTvXavhmfPG1FRzZt6ljlRSDWUozjH9DqtlitA1uU3a4lZ6qKN9%2BH%2BM%2BboJDg5%2Bs1SSHsuorxXT5Ac3SpbMSxuYF1Je58Bduk8TeOpkHVRbxDIJfxOFGY%2BdkKdDKSBgxTsqS%2F6e%2BMg7tCql39SXAhkpp5bEwxFx9Jtytke9gtxJnYpErxLFLHG%2F7wY%3D&X-Amz-SignedHeaders=host&X-Amz-Signature=388b41ebb9bf1c5f55dde004e27cd4e8f18808faa42b0205cd25b8804882d756",
									"mfaLogControlTriggered": null,
									"mfaLogCooldownPeriod": "",
									"mfaLogFmlcFailedReason": "",
									"mfaLogFmlcMatchScore": null,
									"mfaLogFmlcTransactionId": "",
									"mfaLogLoginSource": "web-akulaku",
									"mfaLogMfaType": "OTP Request - Prompt",
									"mfaLogSelfieImage": "",
									"mfaLogStatus": "success",
									"mfaLogTimestamp": "2025-07-01T17:34:32Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiYjY2NDNhYmMtY2QxYy00ZDM1LTk1ZDQtNzBkM2VjZWZmMjdlIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2MDcyMjM5NjU2NzcyIn0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiYjY2NDNhYmMtY2QxYy00ZDM1LTk1ZDQtNzBkM2VjZWZmMjdlIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2MDgzMDEwNjA5ODQ1In0="
							}
						},
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting MFA log with phone number", func() {
			It("should successfully return MFA log results with phone number", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
					"identifierType": "SAFE_ID",
					"key": "mfaLog",
					"payload":{
						"isGetByPhoneNumber": "true"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("mfaLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "MFA Log", "mfaLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "mfaLogTimestamp", "datetime", 1, 1).
						AddRow(2, "MFA Type", "mfaLogMfaType", "oneliner", 1, 2).
						AddRow(3, "Status", "mfaLogStatus", "oneliner", 1, 3).
						AddRow(4, "Cooldown Period", "mfaLogCooldownPeriod", "datetime", 1, 4).
						AddRow(5, "Login Source", "mfaLogLoginSource", "oneliner", 1, 5).
						AddRow(6, "FMLC Failed Reason", "mfaLogFmlcFailedReason", "oneliner", 1, 6).
						AddRow(7, "FMLC Transaction ID", "mfaLogFmlcTransactionId", "oneliner", 1, 7).
						AddRow(8, "FMLC Match Score", "mfaLogFmlcMatchScore", "oneliner", 1, 8).
						AddRow(9, "Control Triggered", "mfaLogControlTriggered", "oneliner", 1, 9).
						AddRow(10, "Selfie Image", "mfaLogSelfieImage", "image", 1, 10).
						AddRow(11, "Compare With Base Selfie", "mfaLogCompareWithBaseSelfie", "sensitiveImage", 1, 11),
					)

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
							"publicID": "ID161101195493"
							"contacts": [
								{
									"contactType": "PRIMARY",
									"email": "",
									"phoneNumber": "+62880003001047"
								}
							]
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"pagination": {
						"nextCursorID": "eyJjdXN0b21lcklEIjoiWE5BX1NZbXgtcUFVREZCM1RUY2FjSDF3YmVsRkd1RzE1RXBlbUlCZVdRME8yaW89IiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2NzMyODk5NTg1ODAwIn0=",
						"prevCursorID": "eyJjdXN0b21lcklEIjoiWE5BX1NZbXgtcUFVREZCM1RUY2FjSDF3YmVsRkd1RzE1RXBlbUlCZVdRME8yaW89IiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2NzM2MDI4NjE1NjA4In0="
					},
					"data": [
						{
						"customerID": "XNA_SYmx-qAUDFB3TTcacH1wbelFGuG15EpemIBeWQ0O2io=",
						"eventTimestamp": "1751366736028615608",
						"metadata": {
							"controlTriggered": null,
							"cooldownPeriod": null,
							"facialMatchResult": "{}",
							"failReason": null,
							"fmlcFaceMatchScore": null,
							"fmlcSuccessCount": null,
							"groupID": null,
							"latestSelfie": "user_uploaded_documents//verifications/liveness-failed/d6fb2180-7e57-4cca-b351-12b66afa78ab_c97d6251403244bf8e062035f33df932-1751366573-_c97d6251403244bf8e062035f33df932-1751366573--20250701104535.jpg",
							"livenessResult": "{\"applicationStatus\":\"auto_declined\",\"results\":[{\"attempts\":\"1\",\"moduleId\":\"module_selfie\",\"imageUrl\":\"https://prod-audit-portal-idn.s3.ap-southeast-3.amazonaws.com/gkyc-ap-southeast-3/checkLiveness/2025-07-01/pd2htx/1751366584283-65236648-41ea-4317-b6bf-6347368aeb40/image.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256\\u0026X-Amz-Credential=ASIAZRKK5ZMR2GNEJO7W%2F20250701%2Fap-southeast-3%2Fs3%2Faws4_request\\u0026X-Amz-Date=20250701T104535Z\\u0026X-Amz-Expires=900\\u0026X-Amz-Security-Token=IQoJb3JpZ2luX2VjENf%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiRjBEAiAJga2pvV%2Bnxgk%2FgLvl%2B%2FwIA%2B6y5uRfeFMnXJNhfbqQugIgKDXh%2FYcwNPyExd2jH%2BmWoW0aWrEHlGYQXbBGzJVMZo0qxwUI0P%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARABGgw2NTU2NzY1MjUzNDciDNIWNMbIVYlOpsnlUiqbBc8TcRpTVKPWwipfGh1MPrY930SFhxfP4zOq6TLONsqXQTDpDS%2BvPajYdlTG1BPRBGcbrDAqs1v8szO57zD2O4DBIERJkJNZmcwTyJpAZm%2BoxdqeB3pTcZC3TTVksJnODJo32sELxZelRV93zvdWYfeqUzSdSvNZ0KdkJLRTWU3VmARN7IBcfCy0HpUDgOEFMBcKqHcONEOS2RG8sigsjnPYA%2F0cY4Lw%2FMsRDBgC2vuXRZlIwk1hZy0fJJRotgsgIYD92PELSJrJaX21ayOA4cx53iW2bjNoQHNsHVIo6xMyRgVbT17TfsQ9laX3fqimQvnco79thtUKX%2FznxzGEa3M%2BZNxv3mT7Jlk4Ppm1vEtwlAyp93ibx9Nz%2FY6FC0uJzklqaHmjqiN0kGRPlUZ%2B7qIjMbks4DstCoRDHxBmx8kP1Q1j6vS9BzWoCzZ75As%2BmQsnxxiKtS%2BV8Vo%2Bmi1N4mtnjhvY4rPR9fooA1L9sLsoXBnE29pa3N0KUSghtVuH7L9ogczcGmWm%2BSdllPZwyuGVmQ1QfCPRTDvGQvOkkXXBwuOBX6odOro1ppyUYGksyfAoL9AQChBBy5RvkSv8c2QBtQKDmBKkKYCyuaSyRbPWvspVJtH0VmMhlf76mW91bBeqe3i%2Bfs9o0E1cW6ruiPqRmHkWe2u9%2BU8tX%2Fg8AKA%2Bn%2BGge0tIrbqR6bIvXUlUyl5tjBHukt8Wcu6CFi1A%2FMSLCzyFX4x9izR%2BXqwTlqAQBwUm95xynCif32wa5DpiWv%2F1myBzAYKiVjys988bWiI%2FLeZuZgDd2LBB%2Fsgz7%2BmDkoxRY2q%2FZE4eQZYfzWlxnmvenca9Yh67hp5PG%2B1iN22hDY6JNEz9Z%2FWufRx3SG8DNFqn6yJmyHaHkMswsoeOwwY6sgE%2BQ%2Fuj%2FzOPEfuW63p1SeHLSGe6aLjy9rPiw%2FSLsSZ7gNiYPRCUxOA8YP18yni1ZGsAoDPYMqqUGKGewcFUDnk3MDumZK9mT%2BZlrhiU9d2y01A85Lpa4KXrexp%2BXWa316rA7%2FV7%2BfJTB%2BvxLPs6ep%2FBSlYx%2FepoOmXMnria5oVoPEAbtB6AscBF2%2FEGR%2BS6KX4KzHjXd0ufxdYz9mIU57%2FZiEh4iIxbU%2B7Ij7r9j0y1ep1Q\\u0026X-Amz-Signature=d86df1381cbb3a7379c05794a583185e19f3ccf2d28e271b8011bf620fdf97fa\\u0026X-Amz-SignedHeaders=host\",\"livenessSpoof\":\"\",\"apiResponse\":{\"statusCode\":200,\"status\":\"success\",\"metadata\":{\"requestId\":\"1751366584283-65236648-41ea-4317-b6bf-6347368aeb40\",\"transactionId\":\"c97d6251403244bf8e062035f33df932-1751366573-\"},\"result\":{\"details\":{\"liveFace\":{\"value\":\"yes\",\"confidence\":\"high\",\"score\":100},\"qualityChecks\":{\"blur\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":95},\"eyesClosed\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"eyewear\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"multipleFaces\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"bright\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"dull\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"faceOccluded\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"hat\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":95},\"headTurned\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"nudity\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":100}}},\"summary\":{\"action\":\"pass\"}}}}],\"userDetails\":{\"inputImageUrl\":\"aff54159-6ca6-4864-adae-a59e0a0beeb1_d916a384-e3ab-4509-982f-096101bb1b6c_eXl1dkgrbEd3MlUwbEViVGFRVWdqOG9NcWRIeVVuelh2cnNMUFFoK0hlcm5ENGh4cG1tNXdkQVFYV1Y1V3krbHBnbHkwS0ZRaGZkT0taRnB3OUJYM0d4WU8vT3ZQYkJadmdhSXQvUkxJSEVzVE9yUWxYanZVNlViSEEwUkRoay9sMDA4cjZFZnVzMi80d1ZVRlJGVGRacHVZTHJ4QjdrSXYwcWdncFU4elc5RTZSSHkwNkFXWVFiSGpJcU1RWVMrVXAwPQ==\",\"selfieAction\":\"pass\",\"facematchAction\":\"fail\",\"selfieAttempts\":\"1\"}}",
							"loginSource": "web-akulaku",
							"maxSuccessFmlcRateLimit": null,
							"mfaType": "FMLC",
							"status": "failed",
							"transactionId": "c97d6251403244bf8e062035f33df932-1751366573-",
							"trigger": "akulaku",
							"workflowID": "akulaku_faceAuthentication"
						}
						},
						{
						"customerID": "XNA_SYmx-qAUDFB3TTcacH1wbelFGuG15EpemIBeWQ0O2io=",
						"eventTimestamp": "1751366732899585800",
						"metadata": {
							"controlTriggered": null,
							"cooldownPeriod": null,
							"facialMatchResult": "{}",
							"failReason": null,
							"fmlcFaceMatchScore": null,
							"fmlcSuccessCount": null,
							"groupID": null,
							"latestSelfie": "user_uploaded_documents//verifications/liveness-failed/bbb432f9-8fea-4524-b4fc-18865ca3556e_c97d6251403244bf8e062035f33df932-1751366573-_c97d6251403244bf8e062035f33df932-1751366573--20250701104532.jpg",
							"livenessResult": "{\"applicationStatus\":\"auto_declined\",\"results\":[{\"attempts\":\"1\",\"moduleId\":\"module_selfie\",\"imageUrl\":\"https://prod-audit-portal-idn.s3.ap-southeast-3.amazonaws.com/gkyc-ap-southeast-3/checkLiveness/2025-07-01/pd2htx/1751366584283-65236648-41ea-4317-b6bf-6347368aeb40/image.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256\\u0026X-Amz-Credential=ASIAZRKK5ZMR3QC6QW4K%2F20250701%2Fap-southeast-3%2Fs3%2Faws4_request\\u0026X-Amz-Date=20250701T104531Z\\u0026X-Amz-Expires=900\\u0026X-Amz-Security-Token=IQoJb3JpZ2luX2VjENn%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCmFwLXNvdXRoLTEiSDBGAiEAzKx1eyy9ZeSo%2FyTk9puKba7uUDmHo%2B0zb4zw8IeF9UUCIQCjj%2BE%2BggHn7dyF068xr80Do83lUO%2B6GlzED4Im5pxMuCrHBQjS%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F8BEAEaDDY1NTY3NjUyNTM0NyIMbE3RHmVbwlUoMtlRKpsF7gNyi6cnSxAsdFVjva%2Fe2UKTSnZz7JW4%2BqrQ9lpQQTiuXYzU9r%2BQKepB5x11UEgiOAdTWONfF%2BMA8BzMtb4tDmMRgGQyhVbzCu2rOU8v8oiFEQ35DypEEX53eh4tW6ddBb2CmpCnW8%2BP8xQjxWGCMinvpvhLNtKcdARIGtVSzas6JnQmwZML51zBAIXtK6lSu%2FoZOzCyoCX6StrpC%2FD5CmB7bF4JMYv%2B%2Fcm7g2YnDtZjzEI407tsehG1nkm%2B3sTXuTHX16JS0WlF5OuxlWORafMDiCUewJokrP5gBQZefsEPZ187WRK072U7EeHKGzTRG%2BrJZ3BQK9sLzBpaxa39PmfxLG7jjboBIKLhMWaHkD%2BONerbArYl8wPcBiYb9Gr4LHX3loMd0%2B5qfOzWC%2F0uXeUHHZcHaR6z%2FTdgfpSuX1OArq8tr8OVlh1ss2XEsfylZn2aZcnL1%2F7lEtTQfHiqRAFYGmOYjnlPZHTG%2Fc2oXbrWF2BdwVtkHhXUDIbOBosdhbpBNkFmAlZiIx5xF6YF3C%2FGIra%2Baq6tdPKZf00I0uCtfUx%2B%2BrecSIYBaiVDi4yBE7iagyGI3QsMf%2BQSjTq9ETa6Un2ze4pUh5aS%2B77KIFuKZzq%2FSx6RWWbfhnZN7c65d%2FyYGhO5dcudJHhX94dvtIHb0RtoL3HhyvmKpZAO8OscjXgSAnjmqTcfZkBrRUXOZnqnhV6I4RLy1vJELFdyIXEgsYqcv%2FV9Ttru2mspqJwypH0WvmpErwx2tvvePR3AE4F6loYQuwSWvdmlD0Spw0gbLnXpGaj6%2BDWxr9%2Btb8tgL0tUeZ0VvLYqxQ%2BlL79ppKA%2FL1x0NJHll5tnQKBCl3ucfxrhKGSeyDYAfhC0cc8JhENZaYsEH41QWDDxu47DBjqwAfnuLcyi0kE0c4mPIqGsU2LRen3uSNqXoLn4oPaAa17qPSNxXI%2BXJekfcEC4fnAZjnzpHP9tWWwzxlo9safgDgoa%2FBfnHXuRdu0ly1g6Y2yNrwnebABRUjIhV%2BBeBTt0SGUYUHWe9m%2B%2Fbbnbt1at6ukRMQRrLcHs%2BhLZjFql%2BdaJcgt%2BAzbfUsuIeQAkwG0w1yFBMt%2BK%2FVo9JBAogzIzsFvZjgzIp4CLtWxNqzcqbHTE\\u0026X-Amz-Signature=7902e4da4e91382d1875a5efc274691b43e96848fe7ee2121e70a25b3f29988d\\u0026X-Amz-SignedHeaders=host\",\"livenessSpoof\":\"\",\"apiResponse\":{\"statusCode\":200,\"status\":\"success\",\"metadata\":{\"requestId\":\"1751366584283-65236648-41ea-4317-b6bf-6347368aeb40\",\"transactionId\":\"c97d6251403244bf8e062035f33df932-1751366573-\"},\"result\":{\"details\":{\"liveFace\":{\"value\":\"yes\",\"confidence\":\"high\",\"score\":100},\"qualityChecks\":{\"blur\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":95},\"eyesClosed\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"eyewear\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"multipleFaces\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"bright\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"dull\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"faceOccluded\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"hat\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":95},\"headTurned\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":0},\"nudity\":{\"value\":\"no\",\"confidence\":\"high\",\"score\":100}}},\"summary\":{\"action\":\"pass\"}}}}],\"userDetails\":{\"inputImageUrl\":\"aff54159-6ca6-4864-adae-a59e0a0beeb1_d916a384-e3ab-4509-982f-096101bb1b6c_eXl1dkgrbEd3MlUwbEViVGFRVWdqOG9NcWRIeVVuelh2cnNMUFFoK0hlcm5ENGh4cG1tNXdkQVFYV1Y1V3krbHBnbHkwS0ZRaGZkT0taRnB3OUJYM0d4WU8vT3ZQYkJadmdhSXQvUkxJSEVzVE9yUWxYanZVNlViSEEwUkRoay9sMDA4cjZFZnVzMi80d1ZVRlJGVGRacHVZTHJ4QjdrSXYwcWdncFU4elc5RTZSSHkwNkFXWVFiSGpJcU1RWVMrVXAwPQ==\",\"selfieAction\":\"pass\",\"facematchAction\":\"fail\",\"selfieAttempts\":\"1\"}}",
							"loginSource": "web-akulaku",
							"maxSuccessFmlcRateLimit": null,
							"mfaType": "FMLC",
							"status": "failed",
							"transactionId": "c97d6251403244bf8e062035f33df932-1751366573-",
							"trigger": "akulaku",
							"workflowID": "akulaku_faceAuthentication"
						}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID:     "b6643abc-cd1c-4d35-95d4-70d3eceff27e",
					PageSize:       2,
					Endpoint:       constants.CustomerJournalLogTypeMFA,
					Identifier:     "+62880003001047",
					IdentifierType: "PHONENUMBER",
				}).Return(mockJournalResponse, nil).Once()

				// mock for GetSelfieImagePresignedURL
				mockCustomerExperience.On("GetSelfieImagePresignedURL", mock.Anything, mock.Anything).Return(&customerExperience.GetSelfieImagePresignedURLResponse{
					Data: "https://example.com/selfie/123456.jpg",
				}, nil).Once()

				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
					"items": [
						{
							"applications": [
								{
									"selfieFile": [
										{
											"URL": "https://example.com/selfie/123456.jpg",
										}
									]
								}
							]
						}
					]
				}`

				var mockCustomerExperienceResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockCustomerExperienceResponse)

				// mock for GetCustomerOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, mock.Anything).Return(mockCustomerExperienceResponse, nil).Times(2)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "mfaLog",
						"type": "table",
						"label": "MFA Log",
						"children": [
							{
								"key": "mfaLogTimestamp",
								"type": "datetime",
								"label": "Timestamp"
							},
							{
								"key": "mfaLogMfaType",
								"type": "oneliner",
								"label": "MFA Type"
							},
							{
								"key": "mfaLogStatus",
								"type": "status",
								"label": "Status"
							},
							{
								"key": "mfaLogCooldownPeriod",
								"type": "datetime",
								"label": "Cooldown Period"
							},
							{
								"key": "mfaLogLoginSource",
								"type": "oneliner",
								"label": "Login Source"
							},
							{
								"key": "mfaLogFmlcFailedReason",
								"type": "oneliner",
								"label": "FMLC Failed Reason"
							},
							{
								"key": "mfaLogFmlcTransactionId",
								"type": "oneliner",
								"label": "FMLC Transaction ID"
							},
							{
								"key": "mfaLogFmlcMatchScore",
								"type": "oneliner",
								"label": "FMLC Match Score"
							},
							{
								"key": "mfaLogControlTriggered",
								"type": "oneliner",
								"label": "Control Triggered"
							},
							{
								"key": "mfaLogSelfieImage",
								"type": "image",
								"label": "Selfie Image"
							},
							{
								"key": "mfaLogBaseSelfie",
								"type": "sensitiveImage",
								"label": "Compare with Base Selfie"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"mfaLog": {
							"data": [
								{
									"mfaLogBaseSelfie": "",
									"mfaLogControlTriggered": null,
									"mfaLogCooldownPeriod": "",
									"mfaLogFmlcFailedReason": "",
									"mfaLogFmlcMatchScore": null,
									"mfaLogFmlcTransactionId": "c97d6251403244bf8e062035f33df932-1751366573-",
									"mfaLogLoginSource": "web-akulaku",
									"mfaLogMfaType": "FMLC",
									"mfaLogSelfieImage": "",
									"mfaLogStatus": "failed",
									"mfaLogTimestamp": "2025-07-01T17:45:36Z"
								},
								{
									"mfaLogBaseSelfie": "",
									"mfaLogControlTriggered": null,
									"mfaLogCooldownPeriod": "",
									"mfaLogFmlcFailedReason": "",
									"mfaLogFmlcMatchScore": null,
									"mfaLogFmlcTransactionId": "c97d6251403244bf8e062035f33df932-1751366573-",
									"mfaLogLoginSource": "web-akulaku",
									"mfaLogMfaType": "FMLC",
									"mfaLogSelfieImage": "",
									"mfaLogStatus": "failed",
									"mfaLogTimestamp": "2025-07-01T17:45:32Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiWE5BX1NZbXgtcUFVREZCM1RUY2FjSDF3YmVsRkd1RzE1RXBlbUlCZVdRME8yaW89IiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2NzMyODk5NTg1ODAwIn0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiWE5BX1NZbXgtcUFVREZCM1RUY2FjSDF3YmVsRkd1RzE1RXBlbUlCZVdRME8yaW89IiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUxMzY2NzM2MDI4NjE1NjA4In0="
							}
						},
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting device login info", func() {
			It("should successfully return device login info results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "deviceLoginInfo"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("deviceLoginInfo", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Device Login Info", "deviceLoginInfo", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Device ID", "deviceLoginInfoDeviceId", "oneliner", 1, 1).
						AddRow(2, "Device Brand", "deviceLoginInfoDeviceBrand", "oneliner", 1, 2).
						AddRow(3, "Device Model", "deviceLoginInfoDeviceModel", "oneliner", 1, 3).
						AddRow(4, "OS Name", "deviceLoginInfoOsName", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"deviceInfo": {
									"deviceID": "device-123456",
									"deviceBrand": "Samsung",
									"deviceModel": "Galaxy S23",
									"osName": "Android",
									"osVersion": "13.0"
								},
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"deviceInfo": {
									"deviceID": "device-abcdef",
									"deviceBrand": "Apple",
									"deviceModel": "iPhone 15 Pro",
									"osName": "iOS",
									"osVersion": "17.4"
								},
								"status": "FAILED",
								"failReason": "Invalid credentials"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeLogin,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "deviceLoginInfo",
						"type": "table",
						"label": "Device Login Info",
						"children": [
							{
								"key": "deviceLoginInfoDeviceId",
								"type": "oneliner",
								"label": "Device ID"
							},
							{
								"key": "deviceLoginInfoDeviceBrand",
								"type": "oneliner",
								"label": "Device Brand"
							},
							{
								"key": "deviceLoginInfoDeviceModel",
								"type": "oneliner",
								"label": "Device Model"
							},
							{
								"key": "deviceLoginInfoOsName",
								"type": "oneliner",
								"label": "OS Name"
							}
						]
					},
					"data": {
						"cif": null,
						"deviceLoginInfo": {
							"data": [
								{
									"deviceLoginInfoDeviceBrand": "Samsung",
									"deviceLoginInfoDeviceId": "device-123456",
									"deviceLoginInfoDeviceModel": "Galaxy S23",
									"deviceLoginInfoOsName": "Android"
								},
								{
									"deviceLoginInfoDeviceBrand": "Apple",
									"deviceLoginInfoDeviceId": "device-abcdef",
									"deviceLoginInfoDeviceModel": "iPhone 15 Pro",
									"deviceLoginInfoOsName": "iOS"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("user has valid permissions and getting facial matching log", func() {
			It("should successfully return facial matching log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "facialMatchingLivenessCheckLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("facialMatchingLivenessCheckLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Facial Matching & Liveness Check Log", "facialMatchingLivenessCheckLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Triggered Date", "facialMatchingTriggeredDate", "oneliner", 1, 1).
						AddRow(2, "Trigger", "facialMatchingTrigger", "oneliner", 1, 2).
						AddRow(3, "Type", "facialMatchingType", "oneliner", 1, 3).
						AddRow(4, "Status", "facialMatchingStatus", "oneliner", 1, 4).
						AddRow(5, "Failed Reason", "facialMatchingFailedReason", "oneliner", 1, 5))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"type": "",
								"trigger": "POST /identity/v1/user/login",
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"type": "",
								"trigger": "POST /identity/v1/user/login",
								"status": "FAILED",
								"failReason": "Invalid credentials"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeFacialAndLiveness,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "facialMatchingLivenessCheckLog",
						"type": "table",
						"label": "Facial Matching \u0026 Liveness Check Log",
						"children": [
							{
								"key": "facialMatchingTriggeredDate",
								"type": "oneliner",
								"label": "Triggered Date"
							},
							{
								"key": "facialMatchingTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "facialMatchingType",
								"type": "oneliner",
								"label": "Type"
							},
							{
								"key": "facialMatchingStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "facialMatchingFailedReason",
								"type": "oneliner",
								"label": "Failed Reason"
							}
						]
					},
					"data": {
						"cif": null,
						"facialMatchingLivenessCheckLog": {
							"data": [
								{
									"facialMatchingFailedReason": "",
									"facialMatchingStatus": "SUCCESS",
									"facialMatchingTrigger": "Login",
									"facialMatchingTriggeredDate": "2024-07-05T08:43:16Z",
									"facialMatchingType": ""
								},
								{
									"facialMatchingFailedReason": "Invalid credentials",
									"facialMatchingStatus": "FAILED",
									"facialMatchingTrigger": "Login",
									"facialMatchingTriggeredDate": "2024-05-08T14:45:13Z",
									"facialMatchingType": ""
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting interaction log", func() {
			It("should successfully return interaction log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "interactionLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("interactionLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Interaction Log", "interactionLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Delivery Date", "interactionLogDeliveryDate", "oneliner_datetime", 1, 1).
						AddRow(2, "Trigger", "interactionLogTrigger", "oneliner", 1, 2).
						AddRow(3, "Content", "interactionLogContent", "oneliner", 1, 3).
						AddRow(4, "Type", "interactionLogType", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"trigger": "SYSTEM",
								"content": {
									"title": "Your Transaction is Successful!",
									"value": ""
								},
								"type": "NOTIFICATION",
								"channel": "PUSH",
								"status": "DELIVERED",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"trigger": "USER",
								"content": {
									"value": "Your OTP code is 123456"
								},
								"type": "OTP",
								"channel": "SMS",
								"status": "FAILED",
								"failReason": "[ERR_DELIVERY_FAILED] Failed to deliver SMS, invalid phone number"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeInteraction,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "interactionLog",
						"type": "table",
						"label": "Interaction Log",
						"children": [
							{
								"key": "interactionLogDeliveryDate",
								"type": "oneliner_datetime",
								"label": "Delivery Date"
							},
							{
								"key": "interactionLogTrigger",
								"type": "oneliner",
								"label": "Trigger"
							},
							{
								"key": "interactionLogContent",
								"type": "oneliner",
								"label": "Content"
							},
							{
								"key": "interactionLogType",
								"type": "oneliner",
								"label": "Type"
							}
						]
					},
					"data": {
						"cif": null,
						"interactionLog": {
							"data": [
								{
									"interactionLogContent": "Your Transaction is Successful!",
									"interactionLogDeliveryDate": "2024-07-05T08:43:16Z",
									"interactionLogTrigger": "SYSTEM",
									"interactionLogType": "NOTIFICATION"
								},
								{
									"interactionLogContent": "Your OTP code is 123456",
									"interactionLogDeliveryDate": "2024-05-08T14:45:13Z",
									"interactionLogTrigger": "USER",
									"interactionLogType": "OTP"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting marketing preference log", func() {
			It("should successfully return marketing preference log results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "marketingPreferenceLog"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("marketingPreferenceLog", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Marketing Preference Log", "marketingPreferenceLog", nil, 1, 3, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "marketingPreferenceLogTimestamp", "oneliner_datetime", 1, 1).
						AddRow(2, "Channel", "marketingPreferenceLogChannel", "oneliner", 1, 2).
						AddRow(3, "Preference", "marketingPreferenceLogPreference", "oneliner", 1, 3).
						AddRow(4, "Status", "marketingPreferenceLogStatus", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0="
					},
					"data": [
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"consentType": "PUSH",
								"consentValue": "True",
								"status": "SUCCESS",
								"failReason": ""
							}
						},
						{
							"customerID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"consentType": "EMAIL",
								"consentValue": "False",
								"status": "FAILED",
								"failReason": "[ERR_UPDATE] Failed to update preference"
							}
						}
					]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "c0575d1f-97a6-494d-8228-42711ae1fdea",
					PageSize:   5,
					Endpoint:   constants.CustomerJournalLogTypeMarketingPreference,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "marketingPreferenceLog",
						"type": "table",
						"label": "Marketing Preference Log",
						"children": [
							{
								"key": "marketingPreferenceLogTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "marketingPreferenceLogChannel",
								"type": "oneliner",
								"label": "Channel"
							},
							{
								"key": "marketingPreferenceLogPreference",
								"type": "oneliner",
								"label": "Preference"
							},
							{
								"key": "marketingPreferenceLogStatus",
								"type": "oneliner",
								"label": "Status"
							}
						]
					},
					"data": {
						"cif": null,
						"marketingPreferenceLog": {
							"data": [
								{
									"marketingPreferenceLogChannel": "PUSH",
									"marketingPreferenceLogPreference": "Turn Off",
									"marketingPreferenceLogStatus": "SUCCESS",
									"marketingPreferenceLogTimestamp": "2024-07-05T08:43:16Z"
								},
								{
									"marketingPreferenceLogChannel": "EMAIL",
									"marketingPreferenceLogPreference": "Turn Off",
									"marketingPreferenceLogStatus": "FAILED",
									"marketingPreferenceLogTimestamp": "2024-05-08T14:45:13Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzE1MTU0MzExOTMwMTQ4Mjk2In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzIwMTQzNzk2Mjc4MjQ0NzM0In0="
							}
						},
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting marketing preference current", func() {
			It("should successfully return marketing preference current results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"identifierType": "SAFE_ID",
						"key": "marketingPreferenceCurrent"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("marketingPreferenceCurrent", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Marketing Preference Current", "marketingPreferenceCurrent", nil, 1, 3, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRoleWithParent)).WithArgs(1, 1, 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Push Notification", "marketingPreferenceCurrentPushNotification", "oneliner", 1, 1).
						AddRow(2, "Email", "marketingPreferenceCurrentEmail", "oneliner", 1, 2).
						AddRow(3, "SMS", "marketingPreferenceCurrentSms", "oneliner", 1, 3).
						AddRow(4, "Whatsapp", "marketingPreferenceCurrentWhatsapp", "oneliner", 1, 4))

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"publicID": "ID179555577230"
						}
					}
				}`

				var mockResponse any
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				//// mock for GetConsentListInternal
				mockCustomerJourneyExperiencePreference.On("GetConsentListInternal", mock.Anything, mock.Anything).Return(&customerJourneyExperiencePreference.GetUserConsentResponse{
					Marketing: []customerJourneyExperiencePreference.ConsentData{
						{
							ConsentType: "PUSH",
							Value:       true,
						},
						{
							ConsentType: "EMAIL",
							Value:       false,
						},
						{
							ConsentType: "SMS",
							Value:       true,
						},
						{
							ConsentType: "WHATSAPP",
							Value:       false,
						},
					},
				}, nil)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "marketingPreferenceCurrent",
						"type": "section",
						"label": "Marketing Preference Current",
						"children": [
							{
								"key": "marketingPreferenceCurrentPushNotification",
								"type": "oneliner",
								"label": "Push Notification"
							},
							{
								"key": "marketingPreferenceCurrentEmail",
								"type": "oneliner",
								"label": "Email"
							},
							{
								"key": "marketingPreferenceCurrentSms",
								"type": "oneliner",
								"label": "SMS"
							},
							{
								"key": "marketingPreferenceCurrentWhatsapp",
								"type": "oneliner",
								"label": "Whatsapp"
							}
						]
					},
					"data": {
						"cif": null,
						"marketingPreferenceCurrentEmail": "Turn Off",
						"marketingPreferenceCurrentPushNotification": "Turn On",
						"marketingPreferenceCurrentSms": "Turn On",
						"marketingPreferenceCurrentWhatsapp": "Turn Off",
						"meta": null,
						"safeID": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
