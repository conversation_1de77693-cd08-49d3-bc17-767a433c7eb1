package api

import (
	"database/sql"
	"fmt"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
)

var _ = Describe("Get Customer Segments API", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get customer segments", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				Expect(err).ShouldNot(HaveOccurred())

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock DB rows
				rows := sqlmock.NewRows([]string{"id", "is_active", "created_by", "created_at", "updated_by", "updated_at", "name"}).
					AddRow(1, true, 1, sql.NullTime{Valid: true}, 2, sql.NullTime{Valid: true}, "Retail").
					AddRow(2, true, 1, sql.NullTime{Valid: true}, 2, sql.NullTime{Valid: true}, "BIZ")
				mocker.ExpectQuery(regexp.QuoteMeta("SELECT id, is_active, created_by, created_at, updated_by, updated_at, name FROM customer_segment")).WillReturnRows(rows)

				res, err := client.Get(GetCustomerSegments)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				// Optionally, check the response body for expected JSON
			})
		})
	})

	Context("Error request", func() {
		When("internal server error occurs", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				Expect(err).ShouldNot(HaveOccurred())

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				mocker.ExpectQuery(regexp.QuoteMeta("SELECT id, is_active, created_by, created_at, updated_by, updated_at, name FROM customer_segment")).WillReturnError(sql.ErrConnDone)

				res, err := client.Get(GetCustomerSegments)
				fmt.Println("res.Body: " + res.Body.String)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
			})
		})
	})
})
