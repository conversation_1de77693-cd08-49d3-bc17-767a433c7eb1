package api

import (
	"encoding/json"
	"errors"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"

	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	accountServiceMock "gitlab.super-id.net/bersama/core-banking/account-service/api/mock"
	customerExperience "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	customerExperienceMock "gitlab.super-id.net/bersama/onboarding/customer-experience/api/mock"
	customerMaster "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2"
	customerMasterMock "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Customers", func() {
	BeforeEach(func() {
		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockAccountService = &accountServiceMock.AccountService{}

		config := &logic.MockProcessConfig{
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AccountService:     mockAccountService,
			AppConfig:          service.AppConfig,
		}
		logic.MockInitLogic(config)
		auditTrailLogic.MockInitLogic(service.AppConfig)
		permissionManagementLogic.MockInitLogic(service.AppConfig)
	})

	Context("GetCustomers", func() {
		When("user has valid permissions", func() {
			It("should successfully return customer search results", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "+*************",
    				"identifierType": "PHONE_NUMBER",
   					"page": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				// expectedResponseForCustomerDetailsByIdentifierV2 ...
				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
				    "isLastPage": true,
					"items": [
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
								"NIK": "****************",
								"addresses": null,
								"alias": "",
								"contacts": [{
									"phoneNumber": "+*************"
								}],
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"fullName": "ade",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"applications": [{
								"ID": "",
								"channel": "",
								"status": "SOFT_REJECTED",
								"statusRemarks": "",
								"ktpFile": null,
								"selfieFile": null,
								"expiresAt": "0001-01-01T00:00:00Z",
								"createdAt": "2024-11-25T04:01:05Z",
								"updatedAt": "2024-11-25T04:02:26Z"
							}]
						},
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107a",
								"NIK": "3009007274171607",
								"addresses": null,
								"alias": "",
								"contacts": [{
									"phoneNumber": "+*************"
								}],
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"fullName": "testing",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"applications": [{
								"ID": "",
								"channel": "",
								"status": "APPROVED",
								"statusRemarks": "",
								"ktpFile": null,
								"selfieFile": null,
								"expiresAt": "0001-01-01T00:00:00Z",
								"createdAt": "2024-11-25T04:01:05Z",
								"updatedAt": "2024-11-25T04:02:26Z"
							}]
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockResponse)

				// mock for GetCustomersOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "+*************",
					IdentifierType: "PHONE_NUMBER",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"isLastPage": true,
					"customers": [
					  {
						"applicationStatus": "SOFT_REJECTED",
						"cif": "ID195849217102",
						"createdAt": "2024-11-25 04:01:05 +0000 UTC",
						"customerStatus": "ONBOARDED",
						"dateOfBirth": "1991-07-16",
						"fullName": "ade",
						"nik": "****************",
						"phoneNumber": "+*************",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"updatedAt": "2024-11-25 04:02:26 +0000 UTC"
					  },
					  {
						"applicationStatus": "APPROVED",
						"cif": "ID195849217102",
						"createdAt": "2024-11-25 04:01:05 +0000 UTC",
						"customerStatus": "ONBOARDED",
						"dateOfBirth": "1991-07-16",
						"fullName": "testing",
						"nik": "3009007274171607",
						"phoneNumber": "+*************",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107a",
						"updatedAt": "2024-11-25 04:02:26 +0000 UTC"
					  }
					]
				  }`

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"+*************", "PHONE_NUMBER", auditTrail.Title, "VALID", auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user does not have permissions", func() {
			It("should return forbidden error", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(response.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})

		When("user has valid permissions and search by cif", func() {
			It("should successfully return error", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "ID125716748913",
    				"identifierType": "CIF",
   					"page": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Mrs Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG"
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "ID125716748913",
					IdentifierType: customerMaster.IdentifierType_CIF,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Contacts, customerMaster.CustomerData_Identities},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"isLastPage": true,
					"customers": [
					  {
						"cif": "ID198980161816",
						"dateOfBirth": "1998-08-18",
						"fullName": "Mrs Nathanael Buckridge",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"status": "ONBOARDED"
					  }
					]
				  }`

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"ID125716748913", "CIF", auditTrail.Title, "VALID", auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions but customer experience returns error", func() {
			It("should return error", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "+*************",
					"identifierType": "PHONE_NUMBER",
					"page": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				// mock GetCustomerOps to return error
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "+*************",
					IdentifierType: "PHONE_NUMBER",
					Page:           1,
				}).Return(nil, errors.New("internal server error")).Once()

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"+*************", "PHONE_NUMBER", auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(500))
			})
		})

		When("request body is invalid", func() {
			It("should return bad request error", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "",
					"identifierType": "INVALID_TYPE",
					"page": 0
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"", "INVALID_TYPE", auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(400))
			})
		})

		When("user has valid permissions and search by NIK", func() {
			It("should successfully return customer search results", func() {
				db, mocker, _ := sqlmock.New()
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "****************",
					"identifierType": "ID_NUMBER",
					"page": 1
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG"
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "****************",
					IdentifierType: customerMaster.IdentifierType_ID_NUMBER,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Contacts, customerMaster.CustomerData_Identities},
				}).Return(mockResponse, nil).Once()

				expectedResponseBody := `{
					"isLastPage": true,
					"customers": [
					  {
						"cif": "ID198980161816",
						"dateOfBirth": "1998-08-18",
						"fullName": "Nathanael Buckridge",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"status": "ONBOARDED"
					  }
					]
				  }`

				auditTrail := resources.SampleDataUserPermissionsCustomerSearchAuditTrail(resources.SampleDataUser())
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					"****************", "ID_NUMBER", auditTrail.Title, "VALID", auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				response, err := client.Get(GetCustomers, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponseBody))
			})
		})
	})
})
