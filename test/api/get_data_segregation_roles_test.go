package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Data Segregation Roles", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get data segregation role list", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}
				args := []any{constants.CustomerSearch}
				getRoles, additionalArgs := commonStorage.BuildQuery(TestQueryGetRolesByElementCode, defaultConditions...)
				args = append(args, additionalArgs...)
				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountRolesByElementCode)).WithArgs(string(constants.CustomerSearch)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getRoles)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowGetDataSegregationRoles(mocker, resources.SampleDataSegregationRoles()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 3,
					"data": [
					  {
						"id": 1,
						"name": "IAM"
					  },
					  {
						"id": 2,
						"name": "Core Banking Ops"
					  },
					  {
						"id": 3,
						"name": "AML Ops"
					  }
					]
			  	}`

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get data segregation roles with request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "IAM",
					"sortBy": {
						"column": "name",
						"sort": "ASC"
					}
				}`)
				countCondition := []commonStorage.QueryCondition{
					commonStorage.Like("r.name", "%IAM%", commonStorage.QueryConditionTypeWHERE),
				}

				getListCondition := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.Like("r.name", "%IAM%", commonStorage.QueryConditionTypeWHERE),
					commonStorage.AscendingOrder("r.name"),
				}

				getCountQuery, countArgs := commonStorage.BuildQuery(TestQueryGetCountRolesByElementCode, countCondition...)
				getListQuery, listArgs := commonStorage.BuildQuery(TestQueryGetRolesByElementCode, getListCondition...)

				finalCountArgs := append([]any{constants.CustomerSearch}, countArgs...)
				finalListArgs := append([]any{constants.CustomerSearch}, listArgs...)

				data := resources.SampleDataSegregationRoles()
				expectedRow := []*permissionManagementStorage.RoleDTO{data[0]}
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(getCountQuery)).WithArgs(convertArgsToDriverArgs(finalCountArgs)...).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getListQuery)).WithArgs(convertArgsToDriverArgs(finalListArgs)...).WillReturnRows(resources.ConvertToRowGetDataSegregationRoles(mocker, expectedRow))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 1,
					"data": [
					  {
						"id": 1,
						"name": "IAM"
					  }
					]
				}`

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("unauthorized request by invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
		When("forbidden request due to invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				delete(permDetail.Permissions, string(constants.DataSegregation))
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("get role list with invalid sort by", func() {
			It("return 400", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "IAM",
					"sortBy": {
						"column": "invalid-sort-by",
						"sort": "ASC"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "badRequest",
					"message": "failed to validate request Key: 'GetDataSegregationRoleListRequest.SortBy' Error:Field validation for 'SortBy' failed on the 'data_segregation_sort' tag"
				}`

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get role list with invalid offset", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 5
				}`)

				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountRolesByElementCode)).WithArgs(string(constants.CustomerSearch)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "badRequest",
					"message": "offset is more than total data"
				}`

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Error request", func() {
		When("error when get count roles", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}
				args := []any{constants.CustomerSearch}
				getRoles, additionalArgs := commonStorage.BuildQuery(TestQueryGetRolesByElementCode, defaultConditions...)
				args = append(args, additionalArgs...)
				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountRolesByElementCode)).WithArgs(string(constants.CustomerSearch)).WillReturnError(sql.ErrConnDone)
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getRoles)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowGetDataSegregationRoles(mocker, resources.SampleDataSegregationRoles()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchCountDataErrorResponse))
			})
		})
		When("error when get list roles", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}
				args := []any{constants.CustomerSearch}
				getRoles, additionalArgs := commonStorage.BuildQuery(TestQueryGetRolesByElementCode, defaultConditions...)
				args = append(args, additionalArgs...)
				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetCountRolesByElementCode)).WithArgs(string(constants.CustomerSearch)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getRoles)).WithArgs(driverArgs...).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetDataSegregationRoles, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchListDataErrorResponse))
			})
		})
	})
})
