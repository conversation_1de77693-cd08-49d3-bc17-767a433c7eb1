package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Data Segregation", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get data segregation list - tab", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1
				}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.EqualTo("s.status", 1),
					commonStorage.IsNull("s.parent_segregation_id"),
					commonStorage.AscendingOrder("s.id"),
				}

				getData, _ := commonStorage.BuildQuery(TestQueryGetDataSegregationByParentID, defaultConditions...)
				mocker.ExpectQuery(regexp.QuoteMeta(getData)).WithArgs(int64(1), int64(1)).WillReturnRows(resources.ConvertToRowGetDataSegregationByParentID(mocker, resources.SampleGetDataSegregationTab()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"data": [
					  {
						"id": 1,
						"name": "Transaction Related Data",
						"status": 1,
						"hasChild": true
					  },
					  {
						"id": 2,
						"name": "Customer Related Data",
						"status": 1,
						"hasChild": true
					  },
					  {
						"id": 3,
						"name": "Onboarding Details",
						"status": 1,
						"hasChild": true
					  }
					]
				}`

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get data segregation list - section", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1,
					"parentID": 2
				}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.EqualTo("s.status", 1),
					commonStorage.EqualTo("s.parent_segregation_id", int64(2)),
					commonStorage.AscendingOrder("s.id"),
				}

				getData, addArgs := commonStorage.BuildQuery(TestQueryGetDataSegregationByParentID, defaultConditions...)
				args := append([]any{int64(1)}, addArgs...)
				mocker.ExpectQuery(regexp.QuoteMeta(getData)).WithArgs(convertArgsToDriverArgs(args)...).WillReturnRows(resources.ConvertToRowGetDataSegregationByParentID(mocker, resources.SampleGetDataSegregationSection()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"data": [
					  {
						"id": 4,
						"name": "Customer Details",
						"status": 1,
						"hasChild": true
					  },
					  {
						"id": 5,
						"name": "Address Details",
						"status": 1,
						"hasChild": true
					  },
					  {
						"id": 6,
						"name": "Contact Details",
						"status": 1,
						"hasChild": true
					  },
					  {
						"id": 7,
						"name": "Beneficiary Details",
						"status": 1,
						"hasChild": true
					  }
					]
				}`

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get data segregation list - search key", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1,
					"parentID": 2,
					"searchKey": "Customer",
					"offset": 0,
					"limit": 10
				}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(11),
					commonStorage.Offset(0),
					commonStorage.Like("s.name", "%Customer%", commonStorage.QueryConditionTypeWHERE),
					commonStorage.AscendingOrder("s.id"),
				}

				getData, addArgs := commonStorage.BuildQuery(TestQueryGetDataSegregationBySearchKey, defaultConditions...)
				args := append([]any{int64(1)}, addArgs...)
				mocker.ExpectQuery(regexp.QuoteMeta(getData)).WithArgs(convertArgsToDriverArgs(args)...).WillReturnRows(resources.ConvertToRowGetDataSegregationBySearchKey(mocker, resources.SampleGetDataSegregationSearchByKey()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"isLastPage": true,
					"data": [
					  {
						"id": 10,
						"name": "Customer Name",
						"status": 1
					  },
					  {
						"id": 11,
						"name": "Customer ID",
						"status": 1
					  },
					  {
						"id": 13,
						"name": "Customer NIK",
						"status": 1
					  },
					  {
						"id": 16,
						"name": "Customer Emergency Contact",
						"status": 1
					  }
					]
				}`

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("unauthorized request by invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1
				}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
		When("forbidden request due to invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1
				}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				delete(permDetail.Permissions, string(constants.DataSegregation))
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("get role segregation with invalid sort by", func() {
			It("return 400", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"roleID": 1,
					"searchKey": "Customer",
					"sortBy": {
						"column": "invalid-sort-by",
						"sort": "ASC"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "badRequest",
					"message": "failed to validate request Key: 'GetDataSegregationRequest.SortBy' Error:Field validation for 'SortBy' failed on the 'data_segregation_sort' tag"
				}`

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Error request", func() {
		When("error get data segregation list - tab", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"roleID": 1
				}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.IsNull("s.parent_segregation_id"),
					commonStorage.AscendingOrder("s.id"),
				}

				getData, _ := commonStorage.BuildQuery(TestQueryGetDataSegregationByParentID, defaultConditions...)
				mocker.ExpectQuery(regexp.QuoteMeta(getData)).WithArgs(int64(1)).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetDataSegregation, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchListDataErrorResponse))
			})
		})
	})
})
