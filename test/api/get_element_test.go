package api

import (
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Update Element", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("GetElement", func() {
		When("request list element with limit", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
				"limit": 10,
				"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.DescendingOrder("e.id"),
				}
				getElementQuery, args := commonStorage.BuildQuery(TestQueriesGetElementList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				mocker.ExpectQuery(regexp.QuoteMeta(getElementQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetElementList(mocker, resources.SampleDataGetElementList()))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetElement)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
            "elements": [
              {
                "id": 1,
                "name": "name-1",
                "code": "code-1",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1,
                "hasTicketing": true
              },
              {
                "id": 2,
                "name": "name-2",
                "code": "code-2",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-2",
                "updatedBy": "updated_by-2",
                "status": 1,
                "hasTicketing": true
              },
              {
                "id": 3,
                "name": "name-3",
                "code": "code-3",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-3",
                "updatedBy": "updated_by-3",
                "status": 1,
                "hasTicketing": true
              }
            ],
            "count": 3
          }`

				res, err := client.Get(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element without limit", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{}
				getElementQuery, args := commonStorage.BuildQuery(TestQueriesGetElementList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				mocker.ExpectQuery(regexp.QuoteMeta(getElementQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetElementList(mocker, resources.SampleDataGetElementList()))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetElement)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
            "elements": [
              {
                "id": 1,
                "name": "name-1",
                "code": "code-1",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1,
                "hasTicketing": true
              },
              {
                "id": 2,
                "name": "name-2",
                "code": "code-2",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-2",
                "updatedBy": "updated_by-2",
                "status": 1,
                "hasTicketing": true
              },
              {
                "id": 3,
                "name": "name-3",
                "code": "code-3",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-3",
                "updatedBy": "updated_by-3",
                "status": 1,
                "hasTicketing": true
              }
            ],
            "count": 3
          }`

				res, err := client.Get(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element filter by name", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
				"searchKey": "name-1",
				"limit": 10,
				"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.DescendingOrder("e.id"),
					commonStorage.Like("e.name", "%name-1%", commonStorage.QueryConditionTypeWHERE),
				}
				getElementQuery, args := commonStorage.BuildQuery(TestQueriesGetElementList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				elementSampel := resources.SampleDataGetElementList()
				elements := []*storage.ElementListDTO{elementSampel[0]}
				mocker.ExpectQuery(regexp.QuoteMeta(getElementQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetElementList(mocker, elements))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetElement)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))

				expectedResponse := `{
            "elements": [
              {
                "id": 1,
                "name": "name-1",
                "code": "code-1",
                "moduleID": 1,
                "defaultPriorityID": 1,
                "createdAt": "2024-12-12 10:00:00 +0000 UTC",
                "updatedAt": "2024-12-12 10:00:00 +0000 UTC",
                "createdBy": "created_by-1",
                "updatedBy": "updated_by-1",
                "status": 1,
                "hasTicketing": true
              }
            ],
            "count": 1
          }`

				res, err := client.Get(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element filter by module_id", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
				"moduleID": 1,
				"limit": 10,
				"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.DescendingOrder("e.id"),
					commonStorage.EqualTo("e.module_id", 1),
				}
				getElementQuery, args := commonStorage.BuildQuery(TestQueriesGetElementList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				elements := resources.SampleDataGetElementList()
				mocker.ExpectQuery(regexp.QuoteMeta(getElementQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetElementList(mocker, elements))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetElement)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
					"elements": [
						{
							"id": 1,
							"name": "name-1",
							"code": "code-1",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-1",
							"updatedBy": "updated_by-1",
							"status": 1,
							"hasTicketing": true
						},
						{
							"id": 2,
							"name": "name-2",
							"code": "code-2",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-2",
							"updatedBy": "updated_by-2",
							"status": 1,
							"hasTicketing": true
						},
						{
							"id": 3,
							"name": "name-3",
							"code": "code-3",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-3",
							"updatedBy": "updated_by-3",
							"status": 1,
							"hasTicketing": true
						}
					],
					"count": 3
				}`

				res, err := client.Get(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request list element filter by has_ticketing", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{
				"hasTicketing": true,
				"limit": 10,
				"offset": 0
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.DescendingOrder("e.id"),
					commonStorage.EqualTo("e.has_ticketing", true),
				}
				getElementQuery, args := commonStorage.BuildQuery(TestQueriesGetElementList, defaultConditions...)
				driverArgs := convertArgsToDriverArgs(args)
				elements := resources.SampleDataGetElementList()
				mocker.ExpectQuery(regexp.QuoteMeta(getElementQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetElementList(mocker, elements))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetElement)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				expectedResponse := `{
					"elements": [
						{
							"id": 1,
							"name": "name-1",
							"code": "code-1",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-1",
							"updatedBy": "updated_by-1",
							"status": 1,
							"hasTicketing": true
						},
						{
							"id": 2,
							"name": "name-2",
							"code": "code-2",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-2",
							"updatedBy": "updated_by-2",
							"status": 1,
							"hasTicketing": true
						},
						{
							"id": 3,
							"name": "name-3",
							"code": "code-3",
							"moduleID": 1,
							"defaultPriorityID": 1,
							"createdAt": "2024-12-12 10:00:00 +0000 UTC",
							"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
							"createdBy": "created_by-3",
							"updatedBy": "updated_by-3",
							"status": 1,
							"hasTicketing": true
						}
					],
					"count": 3
				}`

				res, err := client.Get(Element, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("request element by id", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByID)).
					WithArgs(element.ID).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing"}).
						AddRow(element.ID, element.Name, resources.SampleDefaultTimestamp.Time, element.CreatedBy.Int64, resources.SampleDefaultTimestamp.Time, element.UpdatedBy.Int64, element.ModuleID, element.DefaultPriorityID, element.Code, element.Status, element.HasTicketing))

				expectedResponse := ` {
            "element": {
              "id": 1,
              "name": "Test Element",
              "code": "code",
              "moduleID": 1,
              "defaultPriorityID": 1,
              "createdAt": "2024-12-12 17:00:00 +0700 +07",
              "updatedAt": "2024-12-12 17:00:00 +0700 +07",
              "createdBy": "1",
              "updatedBy": "1",
              "status": 1,
              "hasTicketing": true
            }
          }`

				res, err := client.Get(fmt.Sprintf("%s/%d", Element, element.ID), body, xfccHeader)
				fmt.Println("RESERR" + res.Body.String)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("error element by id", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByID)).
					WithArgs(element.ID).
					WillReturnError(fmt.Errorf("failed to get element by ID(%d)", element.ID))

				expectedResponse := ` {
            "code": "internalServerError",
            "message": "failed to get element by ID",
            "errors": [
              {
                "errorCode": "internalServerError",
                "message": "failed to get element by ID(1)"
              },
              {
                "errorCode": "internalServerError",
                "message": "failed to get element by ID"
              }
            ]
          }`

				res, err := client.Get(fmt.Sprintf("%s/%d", Element, element.ID), body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("not found element by id", func() {
			It("return 404", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				_, err = jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer ")
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				element := resources.SampleDataElement()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElementByID)).
					WithArgs(element.ID).
					WillReturnRows(sqlmock.NewRows([]string{}))

				expectedResponse := `{
            "code": "resourceNotFound",
            "message": "failed to get element by ID",
            "errors": [
              {
                "errorCode": "resourceNotFound",
                "message": "sql: no rows in result set"
              },
              {
                "errorCode": "resourceNotFound",
                "message": "failed to get element by ID"
              }
            ]
          }`

				res, err := client.Get(fmt.Sprintf("%s/%d", Element, element.ID), body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(404))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

	})
})
