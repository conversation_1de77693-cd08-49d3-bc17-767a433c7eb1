package api

import (
	"encoding/json"
	"log"
	"regexp"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Export Ticket", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("GetTicketExport", func() {
			It("should successfully return export data", func() {

				db, mocker, err := sqlmock.New()
				Expect(err).To(BeNil())

				defer db.Close()

				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
				    	"limit": 10,
							"searchKey": "block",
    					"elementID": 5,
    					"statusID": 3,
							"column":["Ticket ID", "Ticket Type", "Module"]
				   	}`)

				// mock DB & Redis
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesEligibleElements)).
					WithArgs(constants.BitwiseValueGeneralRead, constants.BitwiseValueGeneralRead, "test-user-id").
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code"}).
						AddRow(1, "Block Account", time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 4, time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 11, 1, 1, "BLOCK_ACCOUNT").
						AddRow(2, "Unblock Account", time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 4, time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 11, 1, 1, "UNBLOCK_ACCOUNT"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetTicketCount)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetTicketList)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "element_id", "priority_id", "deadline_time", "ticket_status_id", "data", "created_at", "updated_at", "created_by", "updated_by", "source", "assignee_user_name", "element_name", "priority_name", "status_name", "module_name"}).
						AddRow(13, 5, 1, time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 3, `{"accountID": "************"}`, time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), time.Date(2025, time.June, 4, 3, 6, 4, 0, time.Local), 26, 26, 1, "test user", "BLOCK_ACCOUNT", "High", "In Progress Checker", "Ops Portal"))

				expectedResponse := `{
    			"dataExport": "VGlja2V0IElELFRpY2tldCBUeXBlLE1vZHVsZQoxMyxCTE9DS19BQ0NPVU5ULE9wcyBQb3J0YWwK"
				}`

				response, err := client.Post(ExportTicket, body, xfccHeader)

				log.Println("===========================================", response)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
