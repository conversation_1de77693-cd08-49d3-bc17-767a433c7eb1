package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Feature Flag List", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get feature flag list with empty request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}

				getFeatureFlagListQuery, args := commonStorage.BuildQuery(TestQueriesGetFeatureFlags, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountFeatureFlags)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock feature flag list
				mocker.ExpectQuery(regexp.QuoteMeta(getFeatureFlagListQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetFeatureFlagList(mocker, resources.SampleDataGetFeatureFlagList()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedResponse := `{
					"count": 2,
					"data": [
						{
							"name": "Test Feature Flag",
							"value": 1,
							"description": "test feature flag",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by"
						},
						{
							"name": "Test Feature Flag 2",
							"value": 1,
							"description": "test feature flag",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by"
						}
					]
				}`

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get feature flag list with request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					}
				}`)

				// construct conditions
				getFeatureFlagCondition := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.Like("r.name", "%Test%", int64(1)),
					commonStorage.AscendingOrder("r.created_at"),
				}

				countCondition := []commonStorage.QueryCondition{
					commonStorage.Like("r.name", "%Test%", int64(1)),
				}

				getFeatureFlagListQuery, getFeatureFlagArgs := commonStorage.BuildQuery(TestQueriesGetFeatureFlags, getFeatureFlagCondition...)
				countQuery, countFeatureFlagArgs := commonStorage.BuildQuery(TestQueriesGetCountFeatureFlags, countCondition...)

				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(countQuery)).WithArgs(convertArgsToDriverArgs(countFeatureFlagArgs)...,
				).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock feature flag list
				mocker.ExpectQuery(regexp.QuoteMeta(getFeatureFlagListQuery)).WithArgs(convertArgsToDriverArgs(getFeatureFlagArgs)...,
				).WillReturnRows(resources.ConvertToRowsGetFeatureFlagList(mocker, resources.SampleDataGetFeatureFlagList()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedResponse := `{
					"count": 2,
					"data": [
						{
							"name": "Test Feature Flag",
							"value": 1,
							"description": "test feature flag",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by"
						},
						{
							"name": "Test Feature Flag 2",
							"value": 1,
							"description": "test feature flag",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by"
						}
					]
				}`

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("unauthorized request by invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ExpectedAuthorizationFeatureFlagError))
			})
		})
		When("unauthorized request due invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsFeatureFlagDetail()
				delete(permDetail.Permissions, "FEATURE_FLAG")
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ExpectedUserPermissionFeatureFlagError))
			})
		})
		When("get feature flag list with invalid filter", func() {
			It("return 400", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "test_invalid_filter",
							"value": [ "1", "2" ]
						}
					]
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedRes := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'GetFeatureFlagListRequest.Filter' Error:Field validation for 'Filter' failed on the 'ff_filter' tag"
						}
					]
				}`
				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("get feature flag list with invalid sortBy", func() {
			It("return 400", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "test_invalid_sort_by",
						"sort": "ASC"
					}
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedRes := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'GetFeatureFlagListRequest.SortBy' Error:Field validation for 'SortBy' failed on the 'ff_sort' tag"
						}
					]
				}`
				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("Error request", func() {
		When("error when get count data", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountFeatureFlags)).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchCountDataErrorResponse))
			})
		})
		When("error when get feature flag list", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}

				getFeatureFlagListQuery, args := commonStorage.BuildQuery(TestQueriesGetFeatureFlags, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountFeatureFlags)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock feature flag list
				mocker.ExpectQuery(regexp.QuoteMeta(getFeatureFlagListQuery)).WithArgs(driverArgs...).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsFeatureFlagDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "internalServerError",
					"message": "failed to get feature flag list",
					"errors": [
					  {
						"errorCode": "internalServerError",
						"message": "failed to fetch list data"
					  }
					]
				  }`

				res, err := client.Post(GetFeatureFlagList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
