package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Options", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("GetOptions", func() {
		When("user has valid permissions", func() {
			It("should successfully return modules options", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "modules"
			   }`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModules)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "status"}).
						AddRow(1, "Module 1", time.Time{}, "", time.Time{}, "", int32(1)).AddRow(2, "Module 2", time.Time{}, "", time.Time{}, "", int32(1)))

				expectedResponse := `{
			       "data": [
			           {
			               "id": 1,
			               "name": "Module 1"
			           },
			           {
			               "id": 2,
			               "name": "Module 2"
			           }
			       ]
			   }`

				response, err := client.Get(fmt.Sprintf(GetOptions, "modules"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return elements options", func() {
				db, mocker, _ := sqlmock.New()
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "elements"
			   }`)
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElements)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}).
						AddRow(1, "Elements 1", time.Time{}, "", time.Time{}, "", 1, 1, 1, 1, true, -1, -1).AddRow(2, "Elements 2", time.Time{}, "", time.Time{}, "", 1, 1, 1, 1, true, -1, -1))

				expectedResponse := `{
			       "data": [
					{
						"id": 1,
						"name": "Elements 1",
						"moduleId": 1
					},
				  	{
						"id": 2,
						"name": "Elements 2",
						"moduleId": 1
				    }
			       ]
			   }`

				response, err := client.Get(fmt.Sprintf(GetOptions, "elements"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return permissions options", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "permissions"
			   }`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetPermissions)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "bitwise_value", "description", "module_id", "created_at", "created_by", "updated_at", "updated_by"}).
						AddRow(1, "Permissions 1", "1", "Description", "1", time.Time{}, 1, time.Time{}, 1).AddRow(2, "Permissions 2", "1", "Description", "1", time.Time{}, 1, time.Time{}, 1))

				expectedResponse := `{
			       "data": [
			           {
			               "id": 1,
			               "name": "Permissions 1",
						   "moduleId": 1
			           },
			           {
			               "id": 2,
			               "name": "Permissions 2",
						   "moduleId": 1
			           }
			       ]
			   }`

				response, err := client.Get(fmt.Sprintf(GetOptions, "permissions"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return roles options", func() {
				db, mocker, _ := sqlmock.New()
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "roles"
			   }`)
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetRoles)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "created_by", "updated_at", "updated_by"}).
						AddRow(1, "Role 1", 1, time.Time{}, 1, time.Time{}, 1).AddRow(2, "Role 2", 1, time.Time{}, 1, time.Time{}, 1))

				expectedResponse := `{
			       "data": [
					{
						"id": 1,
						"name": "Role 1"
					},
				  	{
						"id": 2,
						"name": "Role 2"
				    }
			       ]
			   }`

				response, err := client.Get(fmt.Sprintf(GetOptions, "roles"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return reasons options", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "reasons"
			   	}`)

				expectedResponse := `{
			       "data": [
					{
						"id": 1,
						"name": "Reason 1"
					},
				  	{
						"id": 2,
						"name": "Reason 2"
				    }
			       ]
			   	}`

				response, err := client.Get(fmt.Sprintf(GetOptions, "reasons"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return ticketFilter options", func() {
				db, mocker, _ := sqlmock.New()
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "ticketFilter"
			   }`)
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModules)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "status"}).
						AddRow(1, "Module 1", time.Time{}, "", time.Time{}, "", int32(1)).AddRow(2, "Module 2", time.Time{}, "", time.Time{}, "", int32(1)))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetElements)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing"}).
						AddRow(1, "Elements 1", time.Time{}, "", time.Time{}, "", 1, 1, 1, 1, true).AddRow(2, "Elements 2", time.Time{}, "", time.Time{}, "", 1, 1, 1, 1, true))

				expectedResponse := `{
			    "data": {
              "modules": [
                {
                  "id": 1,
                  "name": "Module 1"
                },
                {
                  "id": 2,
                  "name": "Module 2"
                }
              ],
              "elements": [
                {
                  "id": 1,
                  "name": "Elements 1",
                  "moduleId": 1
                },
                {
                  "id": 2,
                  "name": "Elements 2",
                  "moduleId": 1
                }
              ],
              "sourceSystem": [
                {
                  "name": "ONEDASH",
                  "key": "ONEDASH"
                },
                {
                  "name": "AML SERVICE",
                  "key": "AML_SERVICE"
                }
              ],
              "integrationStatus": [
                {
                  "name": "Success",
                  "key": "success"
                },
                {
                  "name": "Failed",
                  "key": "failed"
                },
                {
                  "name": "Partially Failed",
                  "key": "partiallyFailed"
                },
                {
                  "name": "In Progress",
                  "key": "inProgress"
                }
              ]
            }
			   }`

				response, err := client.Get(fmt.Sprintf(GetOptions, "ticketFilter"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("database returns error", func() {
			It("should successfully return roles options", func() {
				db, mocker, _ := sqlmock.New()
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			       "type": "roles"
			   }`)
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetRoles)).
					WillReturnError(errors.New("test error"))

				response, err := client.Get(fmt.Sprintf(GetOptions, "roles"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(500))
			})
		})

		When("request body is invalid", func() {
			It("should return bad request error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
		           "type": "invalid_type"
		       }`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
					Return(string(binaryData), nil)

				response, err := client.Get(fmt.Sprintf(GetOptions, "invalid_type"), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(400))
			})
		})
	})
})
