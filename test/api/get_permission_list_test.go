package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get Permission List", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get permission list with empty request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("p.id"),
				}

				getPermissionQuery, args := commonStorage.BuildQuery(TestQueriesGetPermissionList, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetPermission)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getPermissionQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetPermissionList(mocker, resources.SampleDataGetPermissionList()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 3,
					"data": [
					  {
						"id": 1,
						"name": "Read",
						"description": "Read for Module User Management",
						"createdAt": "2024-12-12 10:00:00 +0000 UTC",
						"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
						"createdBy": "Test User",
						"updatedBy": "Test User",
						"status": 1,
						"moduleID": 1,
						"moduleName": "User Management",
						"bitwise": 1
					  },
					  {
						"id": 2,
						"name": "Create",
						"description": "Create for Module User Management",
						"createdAt": "2024-12-12 10:00:00 +0000 UTC",
						"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
						"createdBy": "Test User",
						"updatedBy": "Test User",
						"status": 1,
						"moduleID": 1,
						"moduleName": "User Management",
						"bitwise": 2
					  },
					  {
						"id": 3,
						"name": "Update",
						"description": "Update for Module User Management",
						"createdAt": "2024-12-12 10:00:00 +0000 UTC",
						"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
						"createdBy": "Test User",
						"updatedBy": "Test User",
						"status": 1,
						"moduleID": 1,
						"moduleName": "User Management",
						"bitwise": 4
					  }
					]
				  }`

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get permission list with request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Read",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "status",
							"value": [
								1
							]
						},
						{
							"column": "module_id",
							"value": [
								1
							]
						}
					]
				}`)

				statusFilter := []interface{}{1.0}
				moduleIDFilter := []interface{}{1.0}
				countCondition := []commonStorage.QueryCondition{
					commonStorage.Like("p.name", "%Read%", commonStorage.QueryConditionTypeOR),
					commonStorage.In("p.status", statusFilter...),
					commonStorage.In("p.module_id", moduleIDFilter...),
				}

				getListCondition := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.Like("p.name", "%Read%", commonStorage.QueryConditionTypeOR),
					commonStorage.In("p.status", statusFilter...),
					commonStorage.In("p.module_id", moduleIDFilter...),
					commonStorage.AscendingOrder("p.created_at"),
				}

				getPermissionCountQuery, countArgs := commonStorage.BuildQuery(TestQueriesCountGetPermission, countCondition...)
				getPermissionListQuery, permissionArgs := commonStorage.BuildQuery(TestQueriesGetPermissionList, getListCondition...)

				finalCountArgs := convertArgsToDriverArgs(countArgs)
				data := resources.SampleDataGetPermissionList()
				expectedRow := []*permissionManagementStorage.PermissionListDTO{data[0]}
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(getPermissionCountQuery)).WithArgs(finalCountArgs...).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(1))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getPermissionListQuery)).WithArgs(convertArgsToDriverArgs(permissionArgs)...).WillReturnRows(resources.ConvertToRowsGetPermissionList(mocker, expectedRow))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 1,
					"data": [
					  {
						"id": 1,
						"name": "Read",
						"description": "Read for Module User Management",
						"createdAt": "2024-12-12 10:00:00 +0000 UTC",
						"updatedAt": "2024-12-12 10:00:00 +0000 UTC",
						"createdBy": "Test User",
						"updatedBy": "Test User",
						"status": 1,
						"moduleID": 1,
						"moduleName": "User Management",
						"bitwise": 1
					  }
					]
				  }`

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("unauthorized request by invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
		When("forbidden request due to invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				delete(permDetail.Permissions, string(constants.ModuleConfig))
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("get permission list with invalid filter", func() {
			It("return 400", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Read",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "invalid-filter",
							"value": [
								1
							]
						},
						{
							"column": "module_id",
							"value": [
								1
							]
						}
					]
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "badRequest",
					"message": "failed to validate request Key: 'GetPermissionListRequest.Filter' Error:Field validation for 'Filter' failed on the 'permissions_filter' tag"
				}`

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get permission list with invalid sort by", func() {
			It("return 400", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Read",
					"sortBy": {
						"column": "invalid-sort-by",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "status",
							"value": [
								1
							]
						},
						{
							"column": "module_id",
							"value": [
								1
							]
						}
					]
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "badRequest",
					"message": "failed to validate request Key: 'GetPermissionListRequest.SortBy' Error:Field validation for 'SortBy' failed on the 'permissions_sort_by' tag"
				}`

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Error request", func() {
		When("error when get count permission", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("p.id"),
				}

				getPermissionQuery, args := commonStorage.BuildQuery(TestQueriesGetPermissionList, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetPermission)).WillReturnError(sql.ErrConnDone)
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getPermissionQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetPermissionList(mocker, resources.SampleDataGetPermissionList()))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchCountDataErrorResponse))
			})
		})
		When("error when get list permission", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("p.id"),
				}

				getPermissionQuery, args := commonStorage.BuildQuery(TestQueriesGetPermissionList, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesCountGetPermission)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(3))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getPermissionQuery)).WithArgs(driverArgs...).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(GetPermissionList, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchListDataErrorResponse))
			})
		})
	})
})
