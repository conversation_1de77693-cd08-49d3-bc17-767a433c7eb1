package api

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get User Permission", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("success get user permission", func() {
			It("return 200", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				userDetail := resources.SampleDataUserPermissionsDetail()
				binaryData, _ := json.Marshal(userDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				var resp api.GetUserPermissionsResponse
				_ = json.Unmarshal([]byte(res.Body.String), &resp)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(resp.UserId).Should(Equal(userDetail.UserID))
			})
		})
		When("success get user permission via db", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				userDetail := resources.SampleDataUserPermissionsDetail()
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, resources.SampleDataUser()))
				elemPermission := resources.SampleDataForGetListElementPermission()
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(userDetail.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				var resp api.GetUserPermissionsResponse
				_ = json.Unmarshal(res.Body.Bytes, &resp)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(resp.UserId).Should(Equal(userDetail.UserID))
				Expect(len(resp.Permissions)).Should(Equal(len(elemPermission)))
			})
		})
	})
	Context("Invalid request", func() {
		When("invalid token", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, token)
				body := hcl.JSON(`{}`)

				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenInvalidAuthResponse))
			})
		})
		When("invalid user id", func() {
			It("return 404", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.ResourceNotFound.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ResourceNotFoundUserResponse))
			})
		})
	})
	Context("Error request", func() {
		When("Error get info from redis", func() {
			It("return 500", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", errors.New("failed to connect redis"))

				expectedRes := `{"code":"internalServerError","message":"failed to connect redis"}`
				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error get db handle", func() {
			It("return 500", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(nil, errors.New("failed to get database handle"))

				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				expectedRes := `{"code":"internalServerError","message":"failed to get database handle"}`
				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error get user from db", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				userDetail := resources.SampleDataUserPermissionsDetail()
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrConnDone)
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(userDetail.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ErrorConnectionGetUserResponse))
			})
		})
		When("error get element permission from db", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				userDetail := resources.SampleDataUserPermissionsDetail()
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, resources.SampleDataUser()))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(userDetail.ID).WillReturnError(sql.ErrConnDone)

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				expectedRes := `{
					"code": "internalServerError",
					"message": "failed to get user permission",
					"errors": [
					  {
						"errorCode": "internalServerError",
						"message": "sql: connection is already closed"
					  }
					]
				  }`
				res, err := permissionManagemeneClient.Get(GetUserPermission, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})
