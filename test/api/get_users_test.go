package api

import (
	"database/sql"
	"encoding/json"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonConstants "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/constants"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Get User List", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get user list with empty request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}

				getUserListQuery, args := commonStorage.BuildQuery(TestQueriesGetUserList, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountUsers)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getUserListQuery)).WithArgs(driverArgs...).WillReturnRows(resources.ConvertToRowsGetUserList(mocker, resources.SampleDataGetUserList()))
				// mock get user role
				for _, row := range resources.SampleDataGetUserList() {
					mocker.ExpectQuery(TestQueriesGetUserRole).WithArgs(row.ID).WillReturnRows(
						mocker.NewRows([]string{"id", "name"}).AddRow(1, "IAM").AddRow(2, "Core Banking Maker"))
				}

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 2,
					"data": [
						{
							"id": 1,
							"name": "Test User",
							"email": "<EMAIL>",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by",
							"status": 1,
							"roles": [
								"IAM",
								"Core Banking Maker"
							],
							"userID": "test-user-id"
						},
						{
							"id": 2,
							"name": "Test User 2",
							"email": "<EMAIL>",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by",
							"status": 1,
							"roles": [
								"IAM",
								"Core Banking Maker"
							],
							"userID": "test-user-id-2"
						}
					]
				}`

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("get user list with request", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "role_id",
							"value": [ "1", "2" ]
						}
					]
				}`)

				// construct conditions
				getUsersCondition := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.Like("r.name", "%Test%", int64(5)),
					commonStorage.Like("r.email", "%Test%", int64(5)),
					commonStorage.AscendingOrder("r.created_at"),
				}

				countCondition := []commonStorage.QueryCondition{
					commonStorage.Like("r.name", "%Test%", int64(5)),
					commonStorage.Like("r.email", "%Test%", int64(5)),
				}

				//construct query user list
				queryUserList := `SELECT DISTINCT r.id, r.name, r.status, r.email, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by, r.user_id FROM users r
				LEFT JOIN users u ON r.created_by = u.id 
				LEFT JOIN users u2 ON r.updated_by = u2.id	
				INNER JOIN (
						SELECT DISTINCT user_id FROM users_roles WHERE role_id IN (`
				queryCountUsers := `SELECT DISTINCT count(*) FROM users r
    				INNER JOIN (
        				SELECT DISTINCT user_id FROM users_roles WHERE role_id IN (`

				roleIds := []string{"1", "2"}
				var args []any
				for i, role := range roleIds {
					if i > 0 {
						queryUserList += ", "
						queryCountUsers += ", "
					}
					queryUserList += "?"
					queryCountUsers += "?"
					args = append(args, role)
				}

				queryUserList += `)) AS ur ON r.id = ur.user_id`
				queryCountUsers += `)) AS ur ON r.id = ur.user_id`

				getUserListQuery, getUserArgs := commonStorage.BuildQuery(queryUserList, getUsersCondition...)
				countQuery, countUserArgs := commonStorage.BuildQuery(queryCountUsers, countCondition...)

				getUserArgs = append(args, getUserArgs...)
				countUserArgs = append(args, countUserArgs...)

				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(countQuery)).WithArgs(convertArgsToDriverArgs(countUserArgs)...,
				).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getUserListQuery)).WithArgs(convertArgsToDriverArgs(getUserArgs)...,
				).WillReturnRows(resources.ConvertToRowsGetUserList(mocker, resources.SampleDataGetUserList()))
				// mock get user role
				for _, row := range resources.SampleDataGetUserList() {
					mocker.ExpectQuery(TestQueriesGetUserRole).WithArgs(row.ID).WillReturnRows(
						mocker.NewRows([]string{"id", "name"}).AddRow(1, "IAM").AddRow(2, "Core Banking Maker"))
				}

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"count": 2,
					"data": [
						{
							"id": 1,
							"name": "Test User",
							"email": "<EMAIL>",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by",
							"status": 1,
							"roles": [
								"IAM",
								"Core Banking Maker"
							],
							"userID": "test-user-id"
						},
						{
							"id": 2,
							"name": "Test User 2",
							"email": "<EMAIL>",
							"createdAt": "0001-01-01 00:00:00 +0000 UTC",
							"updatedAt": "0001-01-01 00:00:00 +0000 UTC",
							"createdBy": "test-created-by",
							"updatedBy": "test-updated-by",
							"status": 1,
							"roles": [
								"IAM",
								"Core Banking Maker"
							],
							"userID": "test-user-id-2"
						}
					]
				}`

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Invalid request", func() {
		When("unauthorized request by invalid user", func() {
			It("return 401", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// return empty from redis
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// no user id on db
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
			})
		})
		When("unauthorized request due invalid access", func() {
			It("return 403", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				permDetail := resources.SampleDataUserPermissionsDetail()
				delete(permDetail.Permissions, "USER_MANAGEMENT")
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})
		When("get user list with invalid filter", func() {
			It("return 400", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "created_at",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "test_invalid_filter",
							"value": [ "1", "2" ]
						}
					]
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedRes := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'GetUsersRequest.Filter' Error:Field validation for 'Filter' failed on the 'users_filter' tag"
						}
					]
				}`
				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("get user list with invalid sortBy", func() {
			It("return 400", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"offset": 0,
					"limit": 10,
					"searchKey": "Test",
					"sortBy": {
						"column": "test_invalid_sort_by",
						"sort": "ASC"
					},
					"filter": [
						{
							"column": "role_id",
							"value": [ "1", "2" ]
						}
					]
				}`)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedRes := `{
					"code": "badRequest",
					"message": "failed to validate request",
					"errors": [
						{
							"errorCode": "badRequest",
							"message": "failed to validate request Key: 'GetUsersRequest.SortBy' Error:Field validation for 'SortBy' failed on the 'users_sort' tag"
						}
					]
				}`
				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("Error request", func() {
		When("error when get count data", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountUsers)).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(resources.FailedFetchCountDataErrorResponse))
			})
		})
		When("error when get user list", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				defaultConditions := []commonStorage.QueryCondition{
					commonStorage.Limit(commonConstants.DefaultLimitValue),
					commonStorage.Offset(0),
					commonStorage.AscendingOrder("r.id"),
				}

				getUserListQuery, args := commonStorage.BuildQuery(TestQueriesGetUserList, defaultConditions...)

				driverArgs := convertArgsToDriverArgs(args)
				// mock count
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetCountUsers)).WillReturnRows(mocker.NewRows([]string{"count(*)"}).AddRow(2))
				// mock user list
				mocker.ExpectQuery(regexp.QuoteMeta(getUserListQuery)).WithArgs(driverArgs...).WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				expectedResponse := `{
					"code": "internalServerError",
					"message": "failed to fetch list data"
				}`

				res, err := permissionManagemeneClient.Post(GetUsers, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(500))
				Expect(res.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
