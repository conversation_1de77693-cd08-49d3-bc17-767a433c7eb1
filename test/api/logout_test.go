package api

import (
	"database/sql"
	"errors"
	"log"
	"regexp"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Logout", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("success logout", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUserExpiryRefresh)).WithArgs("", user.ID, user.UserID).WillReturnResult(sqlmock.NewResult(2, 1))

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(true, nil)

				auditTrail := resources.SampleDataLogoutUser(user.UserID, user.ID)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(resources.SuccessResponse))
			})
		})
		When("success request even audit trail is fail", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUserExpiryRefresh)).WithArgs("", user.ID, user.UserID).WillReturnResult(sqlmock.NewResult(2, 1))

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(true, nil)

				auditTrail := resources.SampleDataLogoutUser(user.UserID, user.ID)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnError(sql.ErrConnDone)

				expectedRes := `{"status":"Success"}`
				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("Invalid request", func() {
		When("invalid request due to invalid token format", func() {
			It("return 403", func() {

				// invalid token
				token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MzMzODM5MDgsImlkIjoieHl6QWJzZENoIn0.T4C1pMPigQPcP2PPd8L0E_wFRF4yAHlo4tFYv-vPIF4"

				xfccHeader := hcl.Header(constants.CtxAuthorization, token)
				body := hcl.JSON(`{}`)

				expectedRes := `{
					"code":"forbidden",
					"message":"Invalid token format"
				}`
				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("invalid user id", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnError(sql.ErrNoRows)

				expectedRes := `{"code":"resourceNotFound","message":"User is not found"}`
				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.ResourceNotFound.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("Error request", func() {
		When("error logout due fail update expiry token", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUserExpiryRefresh)).WithArgs("", user.ID, user.UserID).WillReturnError(sql.ErrConnDone)

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(true, nil)

				expectedRes := `{
					"code": "internalServerError",
					"message": "sql: connection is already closed"
				}`
				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("error logout due to fail delete redis key", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdateUserExpiryRefresh)).WithArgs("", user.ID, user.UserID).WillReturnResult(sqlmock.NewResult(2, 1))

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(true, errors.New("redis server error"))

				auditTrail := resources.SampleDataLogoutUser(user.UserID, user.ID)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				expectedRes := `{"code":"internalServerError","message":"redis server error"}`
				res, err := permissionManagemeneClient.Post(Logout, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})
