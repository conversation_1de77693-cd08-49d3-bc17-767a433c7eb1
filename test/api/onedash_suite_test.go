package api

import (
	"database/sql/driver"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
	auditTrailHandlers "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/handlers"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementHandlers "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/handlers"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	data2 "gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/common/testauto/servustest"
	paymentOpsTrfMock "gitlab.myteksi.net/dakota/payment-ops-trf/api/mock"
	payAuthzMock "gitlab.myteksi.net/dakota/payment/pay-authz/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	txLimitMock "gitlab.myteksi.net/dakota/transaction-limit/api/mock"
	accountServiceBersamaMock "gitlab.super-id.net/bersama/core-banking/account-service/api/mock"
	productMasterMock "gitlab.super-id.net/bersama/core-banking/product-master/api/mock"
	customerJournalMock "gitlab.super-id.net/bersama/corex/customer-journal/api/mock"
	customerJourneyExperiencePreferenceMock "gitlab.super-id.net/bersama/corex/customer-journey-experience/api/mock"
	txHistoryMock "gitlab.super-id.net/bersama/deposit/transaction-history/api/mock"
	amlServiceCustomerMock "gitlab.super-id.net/bersama/fintrust/aml-service/api/mock"
	customerExperienceMock "gitlab.super-id.net/bersama/onboarding/customer-experience/api/mock"
	customerMasterMock "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2/mock"

	customerExperienceHTTPMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp/mocks"
	customerPortalMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerportal/mocks"
	grabIDMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid/mock"
	hedwigMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig/mocks"
	jcMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/handlers"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// List of cons API
const (
	GetUsers                = "/api/v1/users"
	Login                   = "/api/v1/login"
	CreateUser              = "/api/v1/user"
	UpdateUserStatus        = "/api/v1/user/status"
	FeatureFlag             = "/api/v1/feature-flag"
	GetFeatureFlagList      = "/api/v1/feature-flag/list"
	GetPermissionList       = "/api/v1/permissions"
	CreatePermission        = "/api/v1/permission"
	UpdatePermission        = "/api/v1/permission"
	UpdatePermissionStatus  = "/api/v1/permission/status"
	UpdateUser              = "/api/v1/user"
	Logout                  = "/api/v1/logout"
	RefreshLogin            = "/api/v1/refresh-login"
	GetUserPermission       = "/api/v1/users/permission"
	CustomerSearch          = "api/v1/customer/search"
	GetCustomers            = "api/v1/customers/search"
	GetDataSegregationRoles = "/api/v1/data-segregation/roles"
	GetDataSegregation      = "/api/v1/data-segregation"
	UpdateDataSegregation   = "/api/v1/data-segregation"
	GetOptions              = "/api/v1/options/%s"
	CreateModule            = "/api/v1/modules"
	CustomerSearchDataPoint = "/api/v1/customers/search/data-point"
	CreateRole              = "/api/v1/roles"
	UpdateRole              = "/api/v1/roles"
	GetListRoles            = "/api/v1/roles/list"
	Element                 = "/api/v1/elements"
	GetCustomerSegments     = "/api/v1/customer-segments"
	GetTicketRequestors     = "/api/v1/ticket-requestors"
	ExportTicket            = "/api/v1/export/ticket"
	AuditTrail              = "/api/v1/audit-trails"
)

var (
	server                                  servustest.ServerDescriptor
	permissionManagementServer              servustest.ServerDescriptor
	auditTrailServer                        servustest.ServerDescriptor
	setupErr                                error
	service                                 *handlers.OnedashService
	permissionManagementService             *permissionManagementHandlers.PermissionManagementService
	auditTrailService                       *auditTrailHandlers.AuditTrailService
	client                                  *hcl.Client
	permissionManagemeneClient              *hcl.Client
	auditTrailClient                        *hcl.Client
	mockRedis                               *redisMock.Client
	mockStorage                             *mocks.MockDatabaseStore
	mockCustomerPortal                      *customerPortalMock.CustomerPortal
	mockHedwig                              *hedwigMock.Hedwig
	mockAccountService                      *accountServiceBersamaMock.AccountService
	mockPayAuthz                            *payAuthzMock.PayAuthz
	mockPaymentOpsTrf                       *paymentOpsTrfMock.PaymentOpsTransfer
	mockJumpcloud                           *jcMock.JumpCloud
	mockCustomerExperience                  *customerExperienceMock.CustomerExperience
	mockCustomerMaster                      *customerMasterMock.CustomerMaster
	mockProductMaster                       *productMasterMock.ProductMaster
	mockTxHistory                           *txHistoryMock.TxHistory
	mockTxLimit                             *txLimitMock.TransactionLimit
	mockCustomerJournal                     *customerJournalMock.CustomerJournal
	mockCustomerJourneyExperiencePreference *customerJourneyExperiencePreferenceMock.PreferenceCenter
	mockAmlServiceCustomer                  *amlServiceCustomerMock.Customer
	mockCustomerExperienceHTTP              *customerExperienceHTTPMock.CustomerExperienceHTTP
	mockGrabID                              *grabIDMock.GrabID
)

func TestSuiteTest(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Test Suite")
}

var _ = BeforeSuite(func() {
	mockRedis = &redisMock.Client{}
	mockCustomerPortal = &customerPortalMock.CustomerPortal{}
	mockHedwig = &hedwigMock.Hedwig{}
	mockAccountService = &accountServiceBersamaMock.AccountService{}
	mockPayAuthz = &payAuthzMock.PayAuthz{}
	mockPaymentOpsTrf = &paymentOpsTrfMock.PaymentOpsTransfer{}
	mockJumpcloud = &jcMock.JumpCloud{}
	mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
	mockCustomerMaster = &customerMasterMock.CustomerMaster{}
	mockCustomerJournal = &customerJournalMock.CustomerJournal{}
	mockProductMaster = &productMasterMock.ProductMaster{}
	mockTxHistory = &txHistoryMock.TxHistory{}
	mockTxLimit = &txLimitMock.TransactionLimit{}
	mockGrabID = &grabIDMock.GrabID{}
	service = &handlers.OnedashService{
		AppConfig: &config.AppConfig{
			DefaultAppConfig: servus.DefaultAppConfig{
				Data: &servus.DataConfig{
					MySQL: &data.MysqlConfig{
						MysqlMasterSlaveConfig: data.MysqlMasterSlaveConfig{
							Slave:  &data2.MysqlConfig{},
							Master: &data2.MysqlConfig{},
						},
					},
				},
			},
			Locale: config.Locale{HomeCountry: "ID"},
			ServicesConfig: config.ServicesConfig{
				Jumpcloud: &jumpcloud.Config{
					BaseURL: "https://example.com/userinfo",
				},
				TokenKey: config.TokenKey{
					AccessTokenExpiryTime:  30,
					RefreshTokenExpiryTime: 720,
				},
				PaymentServiceConfig: &config.PaymentServiceConfig{
					PartnerID: "test-partner-id",
				},
			},
		},
		AccountService:                            mockAccountService,
		CustomerPortalService:                     mockCustomerPortal,
		CustomerExperienceClient:                  mockCustomerExperience,
		CustomerMasterClient:                      mockCustomerMaster,
		CustomerJournalClient:                     mockCustomerJournal,
		CustomerJourneyExperiencePreferenceClient: mockCustomerJourneyExperiencePreference,
		TransactionLimitClient:                    mockTxLimit,
		Statsd:                                    statsd.NewNoop(),
	}

	permissionManagementLogic.MockInitLogic(service.AppConfig)
	permissionManagementService := &permissionManagementHandlers.PermissionManagementService{
		PermissionManagementProcess: permissionManagementLogic.MockPermissionManagementProcess,
		AppConfig: &config.AppConfig{
			DefaultAppConfig: servus.DefaultAppConfig{
				Data: &servus.DataConfig{
					MySQL: &data.MysqlConfig{
						MysqlMasterSlaveConfig: data.MysqlMasterSlaveConfig{
							Slave:  &data2.MysqlConfig{},
							Master: &data2.MysqlConfig{},
						},
					},
				},
			},
			Locale: config.Locale{HomeCountry: "ID"},
			ServicesConfig: config.ServicesConfig{
				Jumpcloud: &jumpcloud.Config{
					BaseURL: "https://example.com/userinfo",
				},
				TokenKey: config.TokenKey{
					AccessTokenExpiryTime:  30,
					RefreshTokenExpiryTime: 720,
				},
			},
		},
	}

	mockRedis = &redisMock.Client{}
	server = servustest.StartServer(service)
	time.Sleep(time.Second)

	//  create mock for init process
	mockConfig := &logic.MockProcessConfig{}
	mockConfig.AppConfig = service.AppConfig
	mockConfig.CustomerMaster = mockCustomerMaster
	mockConfig.CustomerExperience = mockCustomerExperience
	mockConfig.CustomerJourneyExperiencePreference = mockCustomerJourneyExperiencePreference
	mockConfig.AmlServiceCustomer = mockAmlServiceCustomer
	mockConfig.AccountService = mockAccountService
	mockConfig.ProductMaster = mockProductMaster
	mockConfig.TransactionHistory = mockTxHistory
	mockConfig.GrabID = mockGrabID
	logic.MockInitLogic(mockConfig)
	auditTrailLogic.MockInitLogic(service.AppConfig)

	validations.InitValidator(service.AppConfig.Locale.HomeCountry)
	client, setupErr = hcl.NewClient(server.URL())
	Expect(setupErr).ShouldNot(HaveOccurred())

	permissionManagementServer = servustest.StartServer(permissionManagementService)
	permissionManagemeneClient, setupErr = hcl.NewClient(permissionManagementServer.URL())
	Expect(setupErr).ShouldNot(HaveOccurred())

	auditTrailService := &auditTrailHandlers.AuditTrailService{}
	auditTrailServer = servustest.StartServer(auditTrailService)
	auditTrailClient, setupErr = hcl.NewClient(auditTrailServer.URL())
	Expect(setupErr).ShouldNot(HaveOccurred())
})

func convertArgsToDriverArgs(args []any) []driver.Value {
	driverArgs := make([]driver.Value, len(args))
	for i, arg := range args {
		driverArgs[i] = arg
	}
	return driverArgs
}
