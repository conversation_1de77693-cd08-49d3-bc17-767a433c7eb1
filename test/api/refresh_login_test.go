package api

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Refresh Login Test", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("success refresh login", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.RefreshTokenExpiryTime, service.AppConfig.TokenKey.RefreshTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				req := &api.RefreshLoginRequest{
					RefreshToken: "Bearer " + token,
				}
				body := hcl.JSON(req)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				user.RefreshToken = sql.NullString{String: token, Valid: true}
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				var resp api.RefreshLoginResponse
				_ = json.Unmarshal([]byte(res.Body.String), &resp)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(resp.Status).Should(Equal(constants.SuccessStatus))
				Expect(resp.Token).ShouldNot(BeNil())
			})
		})
	})
	Context("Invalid request", func() {
		When("invalid request due to invalid token format", func() {
			It("return 200", func() {
				req := &api.RefreshLoginRequest{
					RefreshToken: "invalid-token-test",
				}
				body := hcl.JSON(req)

				expectedRes := `{"code":"forbidden","message":"Invalid token format"}`
				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("invalid request due to missing token", func() {
			It("return 400", func() {
				req := &api.RefreshLoginRequest{}
				body := hcl.JSON(req)

				expectedRes := `{
					"code":"badRequest",
					"message":"failed to validate request Key: 'RefreshLoginRequest.RefreshToken' Error:Field validation for 'RefreshToken' failed on the 'required' tag"
				}`
				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("invalid request due invalid user id", func() {
			It("return 404", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.RefreshTokenExpiryTime, service.AppConfig.TokenKey.RefreshTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				req := &api.RefreshLoginRequest{
					RefreshToken: "Bearer " + token,
				}
				body := hcl.JSON(req)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				user.RefreshToken = sql.NullString{String: token, Valid: true}
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnError(sql.ErrNoRows)

				expectedRes := `{"code":"resourceNotFound","message":"User is not found"}`
				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.ResourceNotFound.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("invalid request due to unmatched refresh token", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.RefreshTokenExpiryTime, service.AppConfig.TokenKey.RefreshTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				req := &api.RefreshLoginRequest{
					RefreshToken: "Bearer " + token,
				}
				body := hcl.JSON(req)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// mock the db query
				user := resources.SampleDataUser()
				user.RefreshToken = sql.NullString{String: "invalidated-token", Valid: true}
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(user.UserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, user))

				expectedRes := `{"code":"badRequest","message":"Invalid refresh token"}`
				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
	Context("error request", func() {
		When("error refresh login when get database handle", func() {
			It("return 200", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.RefreshTokenExpiryTime, service.AppConfig.TokenKey.RefreshTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				req := &api.RefreshLoginRequest{
					RefreshToken: "Bearer " + token,
				}
				body := hcl.JSON(req)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(nil, errors.New("failed to connect to DB"))

				expectedRes := `{"code":"internalServerError","message":"failed to connect to DB"}`
				res, err := permissionManagemeneClient.Post(RefreshLogin, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})
