// Package api : this file is for API testing query
package api

const (
	TestQueriesGetUserByEmail string = "SELECT id, name, email, password, status, user_id, created_at, updated_at, created_by, updated_by FROM users WHERE email = ?"

	TestQueriesUpdateUserExpiry string = "UPDATE users SET refresh_token = ?, updated_at = now(), updated_by = ? WHERE user_id = ?"

	TestQueriesCreateAuditTrails string = `
	INSERT INTO audit_trails 
	(identifier, identifier_type, title, description, activity_type, reference_id, created_by, created_at) 
	VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`
	TestQueriesGetUserByUserID string = "SELECT id, name, email, password, created_at, updated_at, created_by, updated_by, status, user_id, refresh_token FROM users WHERE user_id = ?"

	TestQueriesGetListElementPermission string = `select 
		    ro.role_name,
		    rp.element_id,
		    rp.element_code,
		    rp.bitwise_value,
		    rp.module_name
		from 
		(
			select ur.role_id, r.name as role_name from users_roles ur 
			inner join roles r on ur.role_id = r.id 
			where r.status = 1 AND ur.user_id = ?
		) as ro 
		left join (
			select rep.role_id, rep.element_id, rep.bitwise_value, e.code as element_code, m.name as module_name from roles_elements_permissions rep 
			inner join elements e on rep.element_id = e.id 
			inner join modules m on e.module_id = m.id 
		) as rp 
		on ro.role_id = rp.role_id;`

	TestQueriesGetUserList string = `SELECT DISTINCT r.id, r.name, r.status, r.email, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by, r.user_id FROM users r
    LEFT JOIN users u ON r.created_by = u.id 
	LEFT JOIN users u2 ON r.updated_by = u2.id	
    LEFT JOIN (
			SELECT DISTINCT user_id FROM users_roles) AS ur ON r.id = ur.user_id`

	TestQueriesGetCountUsers string = `SELECT DISTINCT count(*) FROM users r
    LEFT JOIN ( SELECT DISTINCT user_id FROM users_roles) AS ur ON r.id = ur.user_id`

	TestQueriesGetUserRole string = `SELECT r.ID, r.name FROM roles r 
		INNER JOIN users_roles ur ON r.id = ur.role_id AND r.status = 1
		WHERE ur.user_id = ?`

	TestQueriesCreateUser string = `INSERT INTO users (name, email, password, status, user_id, created_at, updated_at, created_by, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	TestQueriesCreateUserRole string = `INSERT INTO users_roles (role_id, created_at, created_by, user_id) VALUES (?, ?, ?, ?)`

	TestQueriesUpdateUserStatus string = `UPDATE users SET status = ?, updated_at = now(), updated_by = ? WHERE user_id = ?`

	// Feature flag
	TestQueriesCreateFeatureFlag string = `INSERT INTO feature_flag (name, value, description, status, created_at, created_by, updated_at, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	TestQueriesUpdateFeatureFlag string = `UPDATE feature_flag SET updated_at = ?, updated_by = ?, value = ?, description = ? WHERE name = ? and status = 1`

	TestQueriesDeleteFeatureFlag string = `DELETE FROM feature_flag WHERE name = ?`

	TestQueriesGetFeatureFlag string = `SELECT name, value, description FROM feature_flag`

	TestQueriesGetCountFeatureFlags string = `SELECT count(*) FROM feature_flag r`

	TestQueriesGetFeatureFlags string = `select r.name, r.value, r.description, r.status, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by from feature_flag r
    left join users u ON r.created_by = u.id 
	left join users u2 ON r.updated_by = u2.id`

	TestQueriesGetPermissionList string = `SELECT p.id, p.name, p.description, p.status, p.bitwise_value, p.created_at, p.updated_at, u.name as created_by, u2.name as updated_by, p.module_id, m.name as module_name from permissions p
	INNER JOIN modules m ON p.module_id = m.id
	LEFT JOIN users u ON p.created_by = u.id
	LEFT JOIN users u2 ON p.updated_by = u2.id`

	TestQueriesCountGetPermission string = `SELECT count(*) FROM permissions p`

	TestQueryGetCountPermissionForDuplicateChecking string = `SELECT count(*) from permissions 
	WHERE (name = ? OR bitwise_value = ?) AND module_id = ?`

	TestQueryCreatePermission string = `INSERT INTO permissions (name, description, created_at, updated_at, created_by, updated_by, bitwise_value, module_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	TestQueryGetPermissionByID string = `SELECT id, name, bitwise_value, description, module_id, status, created_at, updated_at, created_by, updated_by from permissions
	WHERE id = ?`

	TestQueryGetRolesByPermissionUsed string = `SELECT DISTINCT(r.name) as name FROM roles_elements_permissions rep
    INNER JOIN roles r ON rep.role_id = r.id
	WHERE element_id IN (
	    SELECT id from elements WHERE module_id = ?
	) AND (rep.bitwise_value & ?) = ?`

	TestQueryUpdatePermission string = `UPDATE permissions SET name = ?, description = ?, bitwise_value = ?, status = ?, module_id = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	TestQueryUpdateUser string = `UPDATE users
	SET updated_at = ?, updated_by = ?, name = ?, email = ?, status = ?
	WHERE user_id = ?`

	TestQueryDeleteUserRole string = `DELETE FROM users_roles
	WHERE user_id = ? and role_id = ?`

	TestQueryUpdateUserExpiryRefresh string = `UPDATE users
	SET refresh_token = ?,
	    updated_at = now(),
	    updated_by = ?
	WHERE user_id = ?`

	TestQueryUpdatePermissionStatus string = `UPDATE permissions SET status = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	TestQueryGetSegregationByRole string = `
	SELECT DISTINCT s.id, s.name, s.keyword, s.type, s.parent_segregation_id, s.order FROM segregations s
	LEFT JOIN roles_segregations rs ON s.id = rs.segregation_id
    `

	TestQueryGetSegregationByRoleWithParent string = `
		SELECT DISTINCT s.id, s.name, s.keyword, s.type, s.parent_segregation_id, s.order FROM segregations s 
		LEFT JOIN roles_segregations rs ON s.id = rs.segregation_id 
		WHERE rs.role_id IN (?) AND s.status = ? AND s.parent_segregation_id = ? ORDER BY s.order ASC
  `

	TestQueryGetSegregationDetail string = `
	SELECT s.id, s.name, s.keyword, s.parent_segregation_id, s.order, s.level, s.status, s.type
		FROM segregations s
	`

	TestQueryGetCountRolesByElementCode string = `
	WITH element AS (
		SELECT e.id from elements e
		WHERE e.code = ? AND e.status = 1
	),
	element_roles as (
		SELECT DISTINCT ur.role_id from roles_elements_permissions ur
		WHERE ur.element_id IN (select id from element)
	)
	SELECT count(*) FROM roles r
	INNER JOIN element_roles er
	ON r.id = er.role_id AND r.status = 1
	`

	TestQueryGetRolesByElementCode string = `
	WITH element AS (
		SELECT e.id from elements e
		WHERE e.code = ? AND e.status = 1
	),
	element_roles as (
		SELECT DISTINCT ur.role_id from roles_elements_permissions ur
		WHERE ur.element_id IN (select id from element)
	)
	SELECT r.id, r.name, r.status FROM roles r
	INNER JOIN element_roles er
	ON r.id = er.role_id AND r.status = 1
	`

	TestQueryGetDataSegregationByParentID string = `
	SELECT 
		s.id, 
		s.name, 
		s.parent_segregation_id, 
		CASE
			WHEN exists (SELECT 1 from segregations WHERE parent_segregation_id = s.id) THEN TRUE
			ELSE FALSE
		END as has_child,
	    CASE 
	    	WHEN rs.id IS NULL THEN 0
			ELSE 1
		END as status
	FROM segregations s
	LEFT JOIN roles_segregations rs ON rs.segregation_id = s.id AND rs.role_id = ?
	`

	TestQueryGetDataSegregationBySearchKey string = `
	WITH segregation_child AS (
		SELECT s.id, s.name, s.parent_segregation_id FROM segregations s
		WHERE s.status = 1 AND NOT EXISTS (SELECT 1 from segregations s2 where s2.parent_segregation_id = s.id)
	)
	SELECT 
		s.id, 
		s.name, 
		s.parent_segregation_id, 
		s2.name as parent_name,
		CASE 
			WHEN rs.id IS NULL THEN 0
			ELSE 1
		END as status
	from segregation_child s
	LEFT JOIN segregations s2 ON s2.id = s.parent_segregation_id
	LEFT JOIN roles_segregations rs ON rs.segregation_id = s.id AND rs.role_id = ?
	`

	TestQueryGetRoleByID string = "SELECT id, name, status, created_at, updated_at, created_by, updated_by from roles where id = ?"

	TestQueryCheckEligibilityDefault = `
	SELECT s.id,
		s.parent_segregation_id,
		s.name,
		CASE
		WHEN rs.id %s THEN TRUE
		ELSE FALSE
		END as eligible_update
	FROM segregations s
	LEFT JOIN roles_segregations rs
	ON s.id = rs.segregation_id AND rs.role_id = ?`

	TestQueryInsertRoleSegregation string = "INSERT IGNORE INTO roles_segregations (role_id, segregation_id) VALUES (?, ?)"

	TestQueriesGetModules string = `
	SELECT DISTINCT m.id, m.name, m.created_at, u.name as created_by, m.updated_at, u2.name as updated_by, m.status 
		FROM modules m
		LEFT JOIN users u ON m.created_by = u.id 
		LEFT JOIN users u2 ON m.updated_by = u2.id`

	TestQueriesGetElements string = `SELECT e.id, e.name, e.created_at, u.name as created_by, e.updated_at, u2.name as updated_by, e.module_id, e.default_priority_id, e.code, e.status, e.has_ticketing, e.default_ticket_requestor_id, e.default_customer_segment_id
		FROM elements e
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN users u2 ON e.updated_by = u2.id`

	TestQueriesGetPermissions string = `SELECT id, name, bitwise_value, description, module_id, created_at, created_by, updated_at, updated_by FROM permissions`

	TestQueriesGetRoles string = `SELECT id, name, status, created_at, created_by, updated_at, updated_by FROM roles
		where status = 1`

	TestQueriesGetModuleByName string = `SELECT id, name, status, created_at, created_by, updated_at, updated_by 
             FROM modules 
             WHERE name = ?`

	TestQueriesInsertModule string = `INSERT INTO modules (name, created_at, created_by, updated_at, updated_by, status)
		VALUES (?, ?, ?, ?, ?, ?)`

	TestQueriesGetElementsById string = `SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id
					FROM elements WHERE id = ?`

	TestQueriesGetElementsCount string = `SELECT COUNT(id) FROM elements e WHERE e.module_id = ? AND e.has_ticketing = ?`

	TestQueriesGetTicketChain string = `
		SELECT id, created_at, created_by, updated_at, updated_by, current_status_id, next_status_id, element_id, action_name, bitwise_required
		FROM ticket_chain 
		WHERE element_id = ?
	`

	TestQueriesGetPermissionsWithModuleId string = `
	SELECT id, name, bitwise_value, description, module_id, status, created_at, updated_at, created_by, updated_by 
	from permissions
	WHERE module_id = ?`

	TestQueriesGetElementByCodeExcludeID             = `SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE code = ? AND id != ?`
	TestQueryGetCountRoleForDuplicateChecking string = `SELECT count(*) from roles WHERE (name = ? OR bitwise_value = ?) AND module_id = ?`

	TestQueryCreateRole string = `INSERT INTO roles (name, status, created_at, created_by, updated_at, updated_by) VALUES (?, ?, ?, ?, ?, ?)`

	TestQueryUpdateRole string = `UPDATE roles SET name = ?, status = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	TestQueryTotalBitwisePermission string = `SELECT COALESCE(sum(bitwise_value),0) as total_bitwise from permissions`

	TestQueryCreateRolesElementsPermissions string = `
		INSERT INTO roles_elements_permissions (
			role_id,
			element_id,
			bitwise_value,
			created_at,
			created_by
		) VALUES (
			?, ?, ?, ?, ?
		)
	`

	TestQueryGetPermissionByRoleID string = `SELECT id, element_id, role_id, bitwise_value, created_at, updated_at, created_by, updated_by from roles_elements_permissions
	where role_id = ?`

	TestQueryUpdateRolesElementsPermissions string = `
		UPDATE roles_elements_permissions
		SET bitwise_value = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	TestQueryDeleteRolesElementsPermissions string = `DELETE from roles_elements_permissions`

	TestQueryRemoveUserRoleByRoleID string = `DELETE from users_roles
	WHERE role_id = ?`

	TestQueriesGetUserByRoleID string = `
	SELECT id, name, email, password, created_at, updated_at, created_by, updated_by, status, user_id, refresh_token FROM users
	WHERE id IN (
		SELECT user_id from users_roles
		WHERE role_id = ?
	);
	`

	TestQueriesGetCountRoles string = `SELECT count(*) FROM roles`

	TestQueriesGetListRoles string = `
	select r.id, r.name, r.status, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by from roles r 
	left join users u ON r.created_by = u.id 
	left join users u2 ON r.updated_by = u2.id`

	TestQueriesGetElementByCode string = `SELECT id, name, module_id, code, status, created_at, created_by, updated_at, updated_by, 
             default_priority_id, has_ticketing, default_ticket_requestor_id, default_customer_segment_id 
             FROM elements 
             WHERE code = ?`

	TestQueriesInsertElement string = `INSERT INTO elements (name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	TestQueriesGetTicketChainByElementID = `SELECT id, created_at, created_by, updated_at, updated_by, current_status_id, next_status_id, element_id, action_name, bitwise_required
		FROM ticket_chain 
		WHERE element_id = ?`

	TestQueriesGetPermissionByModuleID = `SELECT id, name, bitwise_value, description, module_id, status, created_at, updated_at, created_by, updated_by from permissions
	WHERE module_id = ?`

	TesCreateInsertTicketChain = `INSERT INTO ticket_chain (created_at, created_by, current_status_id, next_status_id, element_id, action_name, bitwise_required)
			VALUES (?, ?, ?, ?, ?, ?, ?)`

	TestQueriesUpdateElement string = `UPDATE elements
				SET name = ?, updated_at = ?, updated_by = ?, module_id = ?, default_priority_id = ?, code = ?, status = ?, has_ticketing = ?, default_customer_segment_id = ?, default_ticket_requestor_id = ?
				WHERE id = ?`

	TestQueriesGetElementList string = `SELECT e.id, e.name, e.created_at, u.name as created_by, e.updated_at, u2.name as updated_by, e.module_id, e.default_priority_id, e.code, e.status, e.has_ticketing, e.default_ticket_requestor_id, e.default_customer_segment_id
		FROM elements e
        LEFT JOIN users u ON e.created_by = u.id
        LEFT JOIN users u2 ON e.updated_by = u2.id`

	TestQueriesCountGetElement string = `SELECT COUNT(id)
		FROM elements e`

	TestQueriesGetElementByID = `SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id 
		FROM elements
		WHERE id = ?`

	TestQueriesGetModuleByNameExcludeID string = `SELECT id, name, status, created_at, created_by, updated_at, updated_by 
             FROM modules 
             WHERE name = ? AND id != ?`

	TestQueriesUpdateModuleByID string = `UPDATE modules
						 SET name = ?, updated_at = ?, updated_by = ?, status = ?
						 WHERE id = ?`

	TestQueriesGetModuleList string = `SELECT DISTINCT m.id, m.name, m.created_at, u.name as created_by, m.updated_at, u2.name as updated_by, m.status
        FROM modules m
		LEFT JOIN users u ON m.created_by = u.id
		LEFT JOIN users u2 ON m.updated_by = u2.id`

	TestQueriesGetModulesCount string = `SELECT COUNT(DISTINCT m.id) FROM modules m`

	TestQueriesEligibleElements string = `
		SELECT e.id, e.name, e.created_at, e.created_by, e.updated_at, e.updated_by, e.module_id, e.default_priority_id, e.code
		FROM elements e
		INNER JOIN roles_elements_permissions rep on rep.element_id = e.id
		INNER JOIN users_roles ur on ur.role_id = rep.role_id
		INNER JOIN users u on ur.user_id = u.id
		WHERE (rep.bitwise_value & ?) = ?
		AND u.user_id = ?
	`

	TestQueriesGetTicketCount string = `SELECT COUNT(*) FROM tickets t
	INNER JOIN elements e ON t.element_id = e.id`

	TestQueriesGetTicketList string = `
		SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, 
		       t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, 
		       t.source, u.name as assignee_user_name, e.name as element_name,
			    p.name as priority_name, s.name as status_name, m.name as module_name
		FROM tickets t
		LEFT JOIN users u ON t.assignee_user_id = u.id
		INNER JOIN elements e ON t.element_id = e.id
		INNER JOIN modules m ON e.module_id = m.id
		INNER JOIN ticket_priority p ON t.priority_id = p.id
		INNER JOIN ticket_status s ON t.ticket_status_id = s.id
	`
)
