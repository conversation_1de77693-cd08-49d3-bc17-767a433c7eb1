package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Create Module", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("UpdateModule", func() {
		When("user has valid permissions", func() {
			It("should successfully update module", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			    "name": "Test Module",
					"status": 1
			  }`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				module := resources.SampleDataModule()

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModuleByNameExcludeID)).
					WithArgs(module.Name, module.ID).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "created_by", "updated_at", "updated_by"}))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesUpdateModuleByID)).WithArgs(
					module.Name, sqlmock.AnyArg(), sqlmock.AnyArg(), module.Status, module.ID).WillReturnResult(sqlmock.NewResult(1, 1))

				auditTrail := resources.SampleDataCreateModuleAuditTrail(resources.SampleDataUserPermissionsDetail().ID)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					sqlmock.AnyArg(), auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				expectedResponse := `{
			    "id": 1
			  }`

				response, err := client.Put(fmt.Sprintf("%s/%d", CreateModule, module.ID), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("module name already exists", func() {
			It("should return bad request error", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			    	"name": "Test Module",
						"status": 1
			   	}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				module := resources.SampleDataModule()

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModuleByName)).
					WithArgs(module.Name, module.ID).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "created_by", "updated_at", "updated_by"}).
						AddRow(module.ID, module.Name, module.Status, time.Now(), module.CreatedBy.Int64, time.Time{}, module.UpdatedBy.Int64))

				expectedResponse := fmt.Sprintf(`{
					"code": "badRequest",
					"message": "failed to update module",
					"errors": [
					  {
						"errorCode": "badRequest",
						"message": "module with name '%s' already exists"
					  }
					]
				  }`, module.Name)

				response, err := client.Put(fmt.Sprintf("%s/%d", CreateModule, module.ID), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(400))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user does not have permission", func() {
			It("should return unauthorized error", func() {
				db, _, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			    	"name": "test module",
					"status": 1
			   	}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserNoPermissionDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				module := resources.SampleDataModule()

				expectedResponse := `{
            "code": "forbidden",
            "message": "failed to update module",
            "errors": [
              {
                "errorCode": "forbidden",
                "message": "User is not authorized to perform this action"
              }
            ]
          }`

				response, err := client.Put(fmt.Sprintf("%s/%d", CreateModule, module.ID), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(403))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("database connection fails", func() {
			It("should return internal server error", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			    	"name": "test module",
					"status": 1
			   	}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(nil, errors.New("database connection fails"))

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				module := resources.SampleDataModule()

				expectedResponse := `{
            "code": "internalServerError",
            "message": "failed to update module",
            "errors": [
              {
                "errorCode": "internalServerError",
                "message": "database connection fails"
              }
            ]
          }`

				response, err := client.Put(fmt.Sprintf("%s/%d", CreateModule, module.ID), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(500))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("module update fails", func() {
			It("should return internal server error", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
			    "name": "Test Module",
					"status": 1
			  }`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				module := resources.SampleDataModule()

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetModuleByNameExcludeID)).
					WithArgs(module.Name, module.ID).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status", "created_at", "created_by", "updated_at", "updated_by"}))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesUpdateModuleByID)).WithArgs(
					module.Name, sqlmock.AnyArg(), sqlmock.AnyArg(), module.Status, module.ID).
					WillReturnError(errors.New("update fails"))

				expectedResponse := `{
            "code": "internalServerError",
            "message": "failed to update module",
            "errors": [
              {
                "errorCode": "internalServerError",
                "message": "update fails"
              }
            ]
          }`

				response, err := client.Put(fmt.Sprintf("%s/%d", CreateModule, module.ID), body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(500))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
