package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Update Permission Status", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		desc := CurrentGinkgoTestDescription()
		result := testResultPassed
		if desc.Failed {
			result = testResultFailed
		}
		fmt.Printf("Update Permission Status: '%s' => %s\n", desc.TestText, result)
	})

	It("should return 200 when success", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		body := hcl.JSON(`{
			"id": 4,
			"status": 0
		}`)

		data := resources.SampleDataForCreatePermission()
		// mock db
		mocker.ExpectQuery(TestQueryGetPermissionByID).WithArgs(data.ID).WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).WillReturnRows(mocker.NewRows([]string{"name"}))

		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdatePermissionStatus)).WithArgs(0, sqlmock.AnyArg(), data.UpdatedBy.Int64, data.ID).WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Update Permission Status", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

		//expected response
		expectedResponse := `{
			"status": "Success"
		}`

		res, err := permissionManagemeneClient.Post(UpdatePermissionStatus, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(200))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 403: failed to create permission due to invalid access", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		body := hcl.JSON(`{
			"id": 4,
			"status": 0
		}`)

		data := resources.SampleDataForCreatePermission()
		// mock db
		mocker.ExpectQuery(TestQueryGetPermissionByID).WithArgs(data.ID).WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))
		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).WillReturnRows(mocker.NewRows([]string{"name"}))

		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdatePermissionStatus)).WithArgs(0, sqlmock.AnyArg(), data.UpdatedBy.Int64, data.ID).WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Update Permission Status", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		permDetail := resources.SampleDataUserPermissionsDetail()
		delete(permDetail.Permissions, string(constants.ModuleConfig))
		binaryData, _ := json.Marshal(permDetail)
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).
			Return(string(binaryData), nil)

		expectedResponse := `{
			"code": "forbidden",
			"message": "User is not authorized to perform this element action"
		}`

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(expectedResponse))
	})

	It("should return 401 when unauthorized", func() {
		db, mocker, err := sqlmock.New()
		if err != nil {
			log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
		}

		// create token
		token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
			"id": resources.TestUserID,
		}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
		if err != nil {
			log.Fatalf("an error '%s' was not expected when create jwt token", err)
		}

		xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
		body := hcl.JSON(`{
			"id": 4,
			"status": 0
		}`)

		data := resources.SampleDataForCreatePermission()

		// return empty from redis
		mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return("", nil)
		// no user id on db
		mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnError(sql.ErrNoRows)

		// mock db
		mocker.ExpectQuery(TestQueryGetPermissionByID).
			WithArgs(data.ID).
			WillReturnRows(resources.SampleRowGetPermissionByID(mocker, data))

		mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetRolesByPermissionUsed)).
			WithArgs(data.ModuleID, data.BitwiseValue, data.BitwiseValue).
			WillReturnRows(mocker.NewRows([]string{"name"}))

		mocker.ExpectExec(regexp.QuoteMeta(TestQueryUpdatePermissionStatus)).
			WithArgs(0, sqlmock.AnyArg(), data.UpdatedBy.Int64, data.ID).
			WillReturnResult(sqlmock.NewResult(4, 1))

		// mock audit trail
		auditTrail := resources.SampleDataAuditTrailPermission("Update Permission Status", data.ID, data.CreatedBy.Int64, data.Name)

		mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
			sqlmock.AnyArg(), // identifier
			auditTrail.IdentifierType,
			auditTrail.Title,
			auditTrail.Description,
			auditTrail.ActivityType,
			auditTrail.ReferenceID,
			auditTrail.CreatedBy,
			sqlmock.AnyArg(), // created_at
		).WillReturnResult(sqlmock.NewResult(1, 1))

		mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

		res, err := permissionManagemeneClient.Put(UpdatePermission, body, xfccHeader)
		Expect(err).ShouldNot(HaveOccurred())
		Expect(res.StatusCode).Should(Equal(api.Unauthorized.HTTPStatusCode()))
		Expect(res.Body.String).Should(MatchJSON(resources.UnauthorizedUserErrorResponse))
	})
})
