// Package resources ...
package resources

import (
	"database/sql"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/audittrail/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	// TestAuditTrail ...
	TestAuditTrail = "test-audit_trail"
)

var (
	// testIdentifier ...
	testIdentifier = "identifier"

	// testIdentifierType ...
	testIdentifierType = "IDENTIFIER_TYPE"

	// testActivityType ...
	testActivityType = "activity_type"

	// testTitle ...
	testTitle = "title"

	// testDescription ...
	testDescription = "description"

	// testReferenceID ...
	testReferenceID = "reference_id"

	// testExtraParams ...
	testExtraParams = map[string]any{
		"extra_params": "extra_params",
	}

	// testJSONExtraParams ...
	testJSONExtraParams = storage.DataJSON{
		"extra_params": "extra_params",
	}
)

// SamplePayloadAuditTrail ...
func SamplePayloadAuditTrail() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrail ...
func SampleDataAuditTrail() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutExtraParams ...
func SamplePayloadAuditTrailWithoutExtraParams() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutExtraParams ...
func SampleDataAuditTrailWithoutExtraParams() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
	}
}

// SamplePayloadAuditTrailWithoutMetadata ...
func SamplePayloadAuditTrailWithoutMetadata() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutMetadata ...
func SampleDataAuditTrailWithoutMetadata() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutTitle ...
func SamplePayloadAuditTrailWithoutTitle() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutTitle ...
func SampleDataAuditTrailWithoutTitle() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutDescription ...
func SamplePayloadAuditTrailWithoutDescription() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutDescription ...
func SampleDataAuditTrailWithoutDescription() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutReferenceID ...
func SamplePayloadAuditTrailWithoutReferenceID() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		ActivityType:   testActivityType,
		Description:    &testDescription,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutReferenceID ...
func SampleDataAuditTrailWithoutReferenceID() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		ActivityType:   testActivityType,
		Description:    sql.NullString{String: testDescription},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithNull ...
func SamplePayloadAuditTrailWithNull() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		ActivityType:   testActivityType,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithNull ...
func SampleDataAuditTrailWithNull() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		ActivityType:   testActivityType,
	}
}

// SamplePayloadAuditTrailWithoutIdentifier ...
func SamplePayloadAuditTrailWithoutIdentifier() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutIdentifier ...
func SampleDataAuditTrailWithoutIdentifier() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutIdentifierType ...
func SamplePayloadAuditTrailWithoutIdentifierType() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:   testIdentifier,
		Title:        &testTitle,
		Description:  &testDescription,
		ActivityType: testActivityType,
		ReferenceID:  &testReferenceID,
		ExtraParams:  testExtraParams,
		CreatedBy:    1,
	}
}

// SampleDataAuditTrailWithoutIdentifierType ...
func SampleDataAuditTrailWithoutIdentifierType() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:           1,
		CreatedAt:    sql.NullTime{Time: time.Now()},
		CreatedBy:    1,
		Identifier:   testIdentifier,
		Title:        sql.NullString{String: testTitle},
		Description:  sql.NullString{String: testDescription},
		ActivityType: testActivityType,
		ReferenceID:  sql.NullString{String: testReferenceID},
		ExtraParams:  &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutActivityType ...
func SamplePayloadAuditTrailWithoutActivityType() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
		CreatedBy:      1,
	}
}

// SampleDataAuditTrailWithoutActivityType ...
func SampleDataAuditTrailWithoutActivityType() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		CreatedBy:      1,
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}

// SamplePayloadAuditTrailWithoutCreatedBy ...
func SamplePayloadAuditTrailWithoutCreatedBy() *api.AuditTrailRequest {
	return &api.AuditTrailRequest{
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          &testTitle,
		Description:    &testDescription,
		ActivityType:   testActivityType,
		ReferenceID:    &testReferenceID,
		ExtraParams:    testExtraParams,
	}
}

// SampleDataAuditTrailWithoutCreatedBy ...
func SampleDataAuditTrailWithoutCreatedBy() *storage.AuditTrailDTO {
	return &storage.AuditTrailDTO{
		ID:             1,
		CreatedAt:      sql.NullTime{Time: time.Now()},
		Identifier:     testIdentifier,
		IdentifierType: testIdentifierType,
		Title:          sql.NullString{String: testTitle},
		Description:    sql.NullString{String: testDescription},
		ActivityType:   testActivityType,
		ReferenceID:    sql.NullString{String: testReferenceID},
		ExtraParams:    &testJSONExtraParams,
	}
}
