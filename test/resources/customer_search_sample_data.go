// Package resources ...
package resources

import (
	"database/sql"
	"time"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	// TestCustomerSearch ...
	TestCustomerSearch = "test-customer-search"
)

// SampleDataCustomerSearch ...
func SampleDataCustomerSearch() *storage.SegregationDTO {
	return &storage.SegregationDTO{
		ID:   1,
		Name: TestCustomerSearch,
	}
}

// SampleDataUserPermissionsCustomerSearch ...
func SampleDataUserPermissionsCustomerSearch() *permissionManagementStorage.UserPermissionsDetail {
	return &permissionManagementStorage.UserPermissionsDetail{
		ID:      1,
		Name:    "Test User",
		Email:   "<EMAIL>",
		UserID:  TestCustomerSearch,
		Modules: []string{"ADMIN_CONFIG"},
		Roles:   []string{"ADMIN"},
		Permissions: map[string]int64{
			"CUSTOMER_SEARCH": 1,
		},
	}
}

// SampleDataUserPermissionsCustomerSearchAuditTrail ...
func SampleDataUserPermissionsCustomerSearchAuditTrail(user *permissionManagementStorage.UserDTO) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     user.UserID,
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		Title:        "Customer Search",
		Description:  "INVALID",
		ActivityType: constants.CustomerSearchActivity,
	}
}

// ExpectedResponseGetAccountList ...
var ExpectedResponseGetAccountList = `{
    "accounts": [
        {
            "id": "************",
            "productVariantID": "casa_account_default",
            "accountType": "CASA",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": ***********
            },
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2024-06-04T03:06:05Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************",
            "productVariantID": "term_deposit_default",
            "accountType": "TERM_DEPOSIT",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": ********
            },
            "productSpecificParameters": {
                "accountApplicableFeatures": "",
                "accountType": "TERM_DEPOSIT",
                "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
                "applicableInterestRate": "6.25",
                "creditAccountId": "************",
                "expectedTDInterest": "171.23",
                "maturityDate": "2024-07-12",
                "maturityInstruction": "PRINCIPAL_ONLY_ARO",
                "rateSpread": "",
                "tdClosedDate": "",
                "tdCreditedAccount": "************",
                "tdFailedReason": "",
                "tdMaturityDate": "2024-07-12",
                "tdMaturityInstructionType": "PRINCIPAL_ONLY_ARO",
                "tdPrincipalAmount": "500000",
                "tdSourceOfFund": "************",
                "tdStartDate": "2024-07-10",
                "tdTenor": "2",
                "tdTenorType": "days",
                "tdUpdatedPrincipalAmount": ""
            },
            "features": {},
            "openingTimestamp": "2024-07-10T09:05:23Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************001",
            "parentAccountID": "************",
            "productVariantID": "casa_pocket_default",
            "accountType": "CASA",
            "accountName": "Saku 1",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": **********
            },
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2024-07-18T03:20:35Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************",
            "parentAccountID": "************",
            "productVariantID": "pocket_ovo",
            "accountType": "CASA",
            "accountName": "OVO Nabung",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": *********
            },
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2025-02-17T09:17:33Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************004",
            "parentAccountID": "************",
            "productVariantID": "microsaver",
            "accountType": "CASA",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": 0
            },
            "productSpecificParameters": {
                "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
                "tierDescription": "Celengan kamu baru 0-25% terisi, nih. Semangat terus nabungnya, yaa!",
                "tierDetailIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/detail_icon_level_1.png",
                "tierId": "1",
                "tierName": "Ay-am Pengen Cuan",
                "tierSocialMediaUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/social_media_share_level_1.png",
                "tierSortId": "1",
                "tierStatusIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/status_icon_level_1.png"
            },
            "features": {},
            "openingTimestamp": "2025-03-04T06:21:55Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        }
    ],
    "maxChildAccountLimit": 8,
    "totalAccountBalance": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": ********
        }
    },
    "totalEstimatedInterest": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": 17123
        }
    }
}`

// ExpectedResponseAccountDetailMainAccount ...
var ExpectedResponseAccountDetailMainAccount = `{
    "account": {
        "id": "************",
        "cifNumber": "ID179555577230",
        "permittedCurrencies": [
            "IDR"
        ],
        "availableBalance": {
            "currencyCode": "IDR",
            "val": ***********
        },
        "status": "ACTIVE",
        "productID": "RETAIL_SAVINGS",
        "productVariantID": "casa_account_default",
        "productSpecificParameters": {
            "accountApplicableFeatures": "{\"MAIN_ACCOUNT\": true}",
            "accountType": "",
            "applicableHoldcodes": "[]",
            "linkedMainAccountId": "",
            "rateSpread": ""
        },
        "openingTimestamp": "2024-06-04T03:06:05Z",
        "closingTimestamp": "0001-01-01T00:00:00Z",
        "applicableInterestRate": {
            "baseInterestRate": 5,
            "totalInterestRate": 5
        }
    }
}`

// ExpectedResponsePiggybankAccount ...
var ExpectedResponsePiggybankAccount = `{
    "account": {
        "id": "************004",
        "parentAccountID": "************",
        "cifNumber": "ID179555577230",
        "permittedCurrencies": [
            "IDR"
        ],
        "availableBalance": {
            "currencyCode": "IDR",
            "val": 0
        },
        "status": "ACTIVE",
        "productID": "RETAIL_SAVINGS",
        "productVariantID": "microsaver",
        "productSpecificParameters": {
            "accountApplicableFeatures": "{\"MICROSAVER\": true}",
            "accountType": "",
            "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
            "isBoosterFlagEnabled": "true",
            "linkedMainAccountId": "",
            "microsaverFundsCreditAccount": "************",
            "microsaverFundsSourceAccount": "************",
            "nominalAmount": "10000",
            "rateSpread": "",
            "tierDescription": "Celengan kamu baru 0-25% terisi, nih. Semangat terus nabungnya, yaa!",
            "tierDetailIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/detail_icon_level_1.png",
            "tierDisabledStatusIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/disabled_status_icon_level_1.png",
            "tierId": "1",
            "tierName": "Ay-am Pengen Cuan",
            "tierSocialMediaUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/social_media_share_level_1.png",
            "tierSortId": "1",
            "tierStatusIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/status_icon_level_1.png"
        },
        "openingTimestamp": "2025-03-04T06:21:55Z",
        "closingTimestamp": "0001-01-01T00:00:00Z",
        "applicableInterestRate": {
            "baseInterestRate": 10,
            "totalInterestRate": 10
        }
    }
}`

// ExpectedResponseTermDepositAccount ...
var ExpectedResponseTermDepositAccount = `{
    "account": {
        "id": "************",
        "cifNumber": "ID179555577230",
        "permittedCurrencies": [
            "IDR"
        ],
        "availableBalance": {
            "currencyCode": "IDR",
            "val": ********
        },
        "status": "ACTIVE",
        "productID": "TERM_DEPOSIT",
        "productVariantID": "term_deposit_default",
        "productSpecificParameters": {
            "accountApplicableFeatures": "",
            "accountType": "TERM_DEPOSIT",
            "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
            "applicableInterestRate": "6.25",
            "creditAccountId": "************",
            "expectedTDInterest": "171.23",
            "maturityDate": "2024-07-12",
            "maturityInstruction": "PRINCIPAL_ONLY_ARO",
            "rateSpread": "",
            "tdAplicableInterest": "6.25",
            "tdClosedDate": "",
            "tdCreditedAccount": "************",
            "tdFailedReason": "",
            "tdMaturityDate": "2024-07-12",
            "tdMaturityInstructionType": "PRINCIPAL_ONLY_ARO",
            "tdPrincipalAmount": "500000",
            "tdSourceOfFund": "************",
            "tdStartDate": "2024-07-10",
            "tdTenor": "2",
            "tdTenorType": "days",
            "tdUpdatedPrincipalAmount": ""
        },
        "openingTimestamp": "2024-07-10T09:05:23Z",
        "closingTimestamp": "0001-01-01T00:00:00Z",
        "applicableInterestRate": {}
    }
}`

// ExpectedResponseTermDepositParamHistory ...
var ExpectedResponseTermDepositParamHistory = `{
    "depositsParameterHistory": [
        {
            "id": "200277",
            "accountID": "************",
            "parameterKey": "tdMaturityInstructionType",
            "newParameterValue": "PRINCIPAL_ONLY_ARO",
            "updatedAt": "2024-07-10 09:05:24",
            "updatedBy": "MOBILE"
        }
    ]
}`

// ExpectedResponseTermDepositRenewalHistory ...
var ExpectedResponseTermDepositRenewalHistory = `{
    "length": 1,
    "depositsParameterRenewal": [
        {
            "depositsRenewalHistory": {
                "tdAplicableInterest": "6.25",
                "tdMaturityDate": "2024-07-12",
                "tdMaturityInstructionType": "PRINCIPAL_ONLY_ARO",
                "tdUpdatedPrincipalAmount": "0",
                "termDepositAccountID": "************",
                "valueTimestamp": "2024-07-10 09:05:24"
            }
        }
    ]
}`

// ExpectedCustomerLeanResponse ...
var ExpectedCustomerLeanResponse = `{
	"customer": {
			"data": {
			"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
			"dateOfBirth": "1988-08-18",
			"gender": "MALE",
			"maritalStatus": "SINGLE",
			"motherMaidenName": "superbank",
			"name": "Efren Ruecker Sr",
			"nationality": "WNI",
			"placeOfBirth": "PEMALANG",
			"publicID": "ID179555577230",
			"relatedCounterPartyInd": false,
			"startDate": "2024-06-04T03:06:04Z",
			"status": "ONBOARDED",
			"type": "NAT_PERSON"
		}
	}
}`

// ExpectedResponseGetAccountListMainCasaAndPocket ...
var ExpectedResponseGetAccountListMainCasaAndPocket = `{
    "accounts": [
        {
            "id": "************",
            "productVariantID": "casa_account_default",
            "accountType": "CASA",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": ***********
            },
			"applicableInterestRate": {},
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2024-06-04T03:06:05Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************001",
            "parentAccountID": "************",
            "productVariantID": "casa_pocket_default",
            "accountType": "CASA",
            "accountName": "Saku 1",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": **********
            },
			"applicableInterestRate": {},
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2024-07-18T03:20:35Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        },
        {
            "id": "************",
            "parentAccountID": "************",
            "productVariantID": "pocket_ovo",
            "accountType": "CASA",
            "accountName": "OVO Nabung",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": *********
            },
			"applicableInterestRate": {},
            "productSpecificParameters": {
                "applicableHoldcodes": "[]"
            },
            "features": {
                "credit": true,
                "debit": true
            },
            "openingTimestamp": "2025-02-17T09:17:33Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        }
    ],
    "maxChildAccountLimit": 8,
    "totalAccountBalance": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": ********
        }
    },
    "totalEstimatedInterest": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": 17123
        }
    }
}`

// ExpectedResponseGetAccountListPiggybank ...
var ExpectedResponseGetAccountListPiggybank = `{
    "accounts": [
        {
            "id": "************004",
            "parentAccountID": "************",
            "productVariantID": "microsaver",
            "accountType": "CASA",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": 0
            },
			"applicableInterestRate": {},
            "productSpecificParameters": {
                "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
                "tierDescription": "Celengan kamu baru 0-25% terisi, nih. Semangat terus nabungnya, yaa!",
                "tierDetailIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/detail_icon_level_1.png",
                "tierId": "1",
                "tierName": "Ay-am Pengen Cuan",
                "tierSocialMediaUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/social_media_share_level_1.png",
                "tierSortId": "1",
                "tierStatusIconUrl": "https://idbank-stg-backend-assets.s3.ap-southeast-3.amazonaws.com/microsaver/v2/status_icon_level_1.png"
            },
            "features": {},
            "openingTimestamp": "2025-03-04T06:21:55Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        }
    ],
    "maxChildAccountLimit": 8,
    "totalAccountBalance": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": ********
        }
    },
    "totalEstimatedInterest": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": 17123
        }
    }
}`

// ExpectedResponseGetAccountListTermDeposit ...
var ExpectedResponseGetAccountListTermDeposit = `{
    "accounts": [
        {
            "id": "************",
            "productVariantID": "term_deposit_default",
            "accountType": "TERM_DEPOSIT",
            "status": "ACTIVE",
            "availableBalance": {
                "currencyCode": "IDR",
                "val": ********
            },
            "productSpecificParameters": {
                "accountApplicableFeatures": "",
                "accountType": "TERM_DEPOSIT",
                "applicableHoldcodes": "[\"WHOLE_BALANCE_HOLD\"]",
                "applicableInterestRate": "6.25",
                "creditAccountId": "************",
                "expectedTDInterest": "171.23",
                "maturityDate": "2024-07-12",
                "maturityInstruction": "PRINCIPAL_ONLY_ARO",
                "rateSpread": "",
                "tdClosedDate": "",
                "tdCreditedAccount": "************",
                "tdFailedReason": "",
                "tdMaturityDate": "2024-07-12",
                "tdMaturityInstructionType": "PRINCIPAL_ONLY_ARO",
                "tdPrincipalAmount": "500000",
                "tdSourceOfFund": "************",
                "tdStartDate": "2024-07-10",
                "tdTenor": "2",
                "tdTenorType": "days",
                "tdUpdatedPrincipalAmount": ""
            },
			"applicableInterestRate": {},
            "features": {},
            "openingTimestamp": "2024-07-10T09:05:23Z",
            "closingTimestamp": "0001-01-01T00:00:00Z"
        }
    ],
    "maxChildAccountLimit": 8,
    "totalAccountBalance": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": ********
        }
    },
    "totalEstimatedInterest": {
        "termDepositDefault": {
            "currencyCode": "IDR",
            "val": 17123
        }
    }
}`
