package resources

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// SampleDataSegregationRoles ...
func SampleDataSegregationRoles() []*permissionManagementStorage.RoleDTO {
	return []*permissionManagementStorage.RoleDTO{
		{
			ID:     1,
			Name:   "IAM",
			Status: 1,
		},
		{
			ID:     2,
			Name:   "Core Banking Ops",
			Status: 1,
		},
		{
			ID:     3,
			Name:   "AML Ops",
			Status: 1,
		},
	}
}

// ConvertToRowGetDataSegregationRoles ...
func ConvertToRowGetDataSegregationRoles(mocker sqlmock.Sqlmock, roleList []*permissionManagementStorage.RoleDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "status"})

	for _, row := range roleList {
		mockRow.AddRow(row.ID, row.Name, row.Status)
	}
	return mockRow
}

// SampleGetDataSegregationTab ...
func SampleGetDataSegregationTab() []*storage.DataSegregationListDTO {
	return []*storage.DataSegregationListDTO{
		{
			ID:       1,
			Name:     "Transaction Related Data",
			Status:   1,
			HasChild: true,
		},
		{
			ID:       2,
			Name:     "Customer Related Data",
			Status:   1,
			HasChild: true,
		},
		{
			ID:       3,
			Name:     "Onboarding Details",
			Status:   1,
			HasChild: true,
		},
	}
}

// SampleGetDataSegregationSection ...
func SampleGetDataSegregationSection() []*storage.DataSegregationListDTO {
	return []*storage.DataSegregationListDTO{
		{
			ID:       4,
			Name:     "Customer Details",
			Status:   1,
			HasChild: true,
		},
		{
			ID:       5,
			Name:     "Address Details",
			Status:   1,
			HasChild: true,
		},
		{
			ID:       6,
			Name:     "Contact Details",
			Status:   1,
			HasChild: true,
		},
		{
			ID:       7,
			Name:     "Beneficiary Details",
			Status:   1,
			HasChild: true,
		},
	}
}

// SampleGetDataSegregationSearchByKey ...
func SampleGetDataSegregationSearchByKey() []*storage.DataSegregationListDTO {
	return []*storage.DataSegregationListDTO{
		{
			ID:                  10,
			Name:                "Customer Name",
			ParentSegregationID: sql.NullInt64{Int64: 3},
			ParentName:          sql.NullString{String: "Customer Details"},
			Status:              1,
		},
		{
			ID:                  11,
			Name:                "Customer ID",
			ParentSegregationID: sql.NullInt64{Int64: 3},
			ParentName:          sql.NullString{String: "Customer Details"},
			Status:              1,
		},
		{
			ID:                  13,
			Name:                "Customer NIK",
			ParentSegregationID: sql.NullInt64{Int64: 3},
			ParentName:          sql.NullString{String: "Customer Details"},
			Status:              1,
		},
		{
			ID:                  16,
			Name:                "Customer Emergency Contact",
			ParentSegregationID: sql.NullInt64{Int64: 4},
			ParentName:          sql.NullString{String: "Lending"},
			Status:              1,
		},
	}
}

// ConvertToRowGetDataSegregationByParentID ...
func ConvertToRowGetDataSegregationByParentID(mocker sqlmock.Sqlmock, data []*storage.DataSegregationListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "parent_segregation_id", "has_child", "status"})

	for _, row := range data {
		mockRow.AddRow(row.ID, row.Name, row.ParentSegregationID, row.HasChild, row.Status)
	}
	return mockRow
}

// ConvertToRowGetDataSegregationBySearchKey ...
func ConvertToRowGetDataSegregationBySearchKey(mocker sqlmock.Sqlmock, data []*storage.DataSegregationListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "parent_segregation_id", "parent_name", "status"})

	for _, row := range data {
		mockRow.AddRow(row.ID, row.Name, row.ParentSegregationID, row.ParentName, row.Status)
	}
	return mockRow
}

// SampleDataGetRoleByID ...
func SampleDataGetRoleByID() *permissionManagementStorage.RoleDTO {
	return &permissionManagementStorage.RoleDTO{
		ID:        1,
		Name:      "IAM",
		Status:    1,
		CreatedAt: sql.NullTime{Time: time.Now()},
		CreatedBy: sql.NullInt64{Int64: 1},
		UpdatedAt: sql.NullTime{Time: time.Now()},
		UpdatedBy: sql.NullInt64{Int64: 1},
	}
}

// ConvertRowGetRoleByID ...
func ConvertRowGetRoleByID(mocker sqlmock.Sqlmock, data *permissionManagementStorage.RoleDTO) *sqlmock.Rows {
	return mocker.NewRows([]string{"id", "name", "status", "created_at", "updated_at", "created_by", "updated_by"}).AddRow(
		data.ID, data.Name, data.Status, data.CreatedAt, data.UpdatedAt, data.CreatedBy, data.UpdatedBy)
}

// SampleDataCheckEligibilitySegregationDefault ...
func SampleDataCheckEligibilitySegregationDefault() []*storage.DataSegregationEligibility {
	return []*storage.DataSegregationEligibility{
		{
			ID:                  4,
			Name:                "Transaction History",
			ParentSegregationID: sql.NullInt64{},
			EligibleUpdate:      true,
		},
		{
			ID:                  5,
			Name:                "Transaction ID",
			ParentSegregationID: sql.NullInt64{},
			EligibleUpdate:      true,
		},
	}
}

// ConvertToRowDataSegregationEligibility ...
func ConvertToRowDataSegregationEligibility(mocker sqlmock.Sqlmock, data []*storage.DataSegregationEligibility) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "parent_segregation_id", "name", "eligible_update"})

	for _, row := range data {
		mockRow.AddRow(row.ID, row.ParentSegregationID, row.Name, row.EligibleUpdate)
	}
	return mockRow
}

// SampleDataActivateDataSegregation ...
func SampleDataActivateDataSegregation(role *permissionManagementStorage.RoleDTO, data *storage.DataSegregationEligibility, action string) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     fmt.Sprint(role.ID),
		IdentifierType: constants.RoleID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: 1,
			Valid: true,
		},
		Title:        fmt.Sprintf("%v data %v:%v successfully", action, data.ID, data.Name),
		Description:  fmt.Sprintf("%s data %v:%v for role %v executed successfully", action, data.ID, data.Name, role.Name),
		ActivityType: constants.DataSegregationConfig,
	}
}
