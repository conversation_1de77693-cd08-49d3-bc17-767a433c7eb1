// Package resources ...
package resources

import (
	"database/sql"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	// TestElement ...
	TestElement = "test-element"
)

// SampleDataUserPermissionsElementDetail ...
func SampleDataUserPermissionsElementDetail() *permissionManagementStorage.UserPermissionsDetail {
	return &permissionManagementStorage.UserPermissionsDetail{
		ID:      1,
		Name:    "Test Element",
		Email:   "<EMAIL>",
		UserID:  "test-element-id",
		Modules: []string{"ADMIN_CONFIG"},
		Roles:   []string{"ADMIN"},
		Permissions: map[string]int64{
			string(constants.ModuleConfig): 7,
		},
	}
}

// SampleDataElement ...
func SampleDataElement() *storage.ElementDTO {
	return &storage.ElementDTO{
		ID:                       1,
		CreatedAt:                sql.NullTime{Time: time.Now()},
		UpdatedAt:                sql.NullTime{Time: time.Now()},
		CreatedBy:                sql.NullInt64{Int64: 1},
		UpdatedBy:                sql.NullInt64{Valid: true, Int64: 1},
		Name:                     "Test Element",
		ModuleID:                 1,
		DefaultPriorityID:        1,
		Code:                     "code",
		Status:                   1,
		HasTicketing:             true,
		DefaultTicketRequestorID: sql.NullInt64{Valid: true, Int64: 1},
		DefaultCustomerSegmentID: sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataTicketChain ...
func SampleDataTicketChain() *storage.TicketChainDTO {
	return &storage.TicketChainDTO{
		ID:              1,
		CreatedAt:       sql.NullTime{Time: time.Now()},
		UpdatedAt:       sql.NullTime{Time: time.Now()},
		CreatedBy:       sql.NullInt64{Int64: 1},
		UpdatedBy:       sql.NullInt64{Valid: true, Int64: 1},
		CurrentStatusID: 1,
		NextStatusID:    2,
		ElementID:       1,
		ActionName:      "action_name",
		BitwiseRequired: 0,
	}
}

// ConvertToRowsGetElementList ...
func ConvertToRowsGetElementList(mocker sqlmock.Sqlmock, permList []*storage.ElementListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_customer_segment_id", "default_ticket_requestor_id"})

	for _, row := range permList {
		mockRow.AddRow(row.ID, row.Name, row.CreatedAt, row.CreatedBy, row.UpdatedAt, row.UpdatedBy, row.ModuleID, row.DefaultPriorityID, row.Code, row.Status, row.HasTicketing, row.DefaultCustomerSegmentID, row.DefaultTicketRequestorID)
	}
	return mockRow
}

// SampleDataGetElementList ...
func SampleDataGetElementList() []*storage.ElementListDTO {
	return []*storage.ElementListDTO{
		{
			ID:                1,
			Name:              "name-1",
			Code:              "code-1",
			ModuleID:          1,
			DefaultPriorityID: 1,
			CreatedAt:         SampleDefaultTimestamp,
			UpdatedAt:         SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-1",
				Valid:  true,
			},
			UpdatedBy: sql.NullString{
				String: "updated_by-1",
				Valid:  true,
			},
			Status:       1,
			HasTicketing: true,
		},
		{
			ID:                2,
			Name:              "name-2",
			Code:              "code-2",
			ModuleID:          1,
			DefaultPriorityID: 1,
			CreatedAt:         SampleDefaultTimestamp,
			UpdatedAt:         SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-2",
				Valid:  true,
			},
			UpdatedBy: sql.NullString{
				String: "updated_by-2",
				Valid:  true,
			},
			Status:       1,
			HasTicketing: true,
		},
		{
			ID:                3,
			Name:              "name-3",
			Code:              "code-3",
			ModuleID:          1,
			DefaultPriorityID: 1,
			CreatedAt:         SampleDefaultTimestamp,
			UpdatedAt:         SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-3",
				Valid:  true,
			},
			UpdatedBy: sql.NullString{
				String: "updated_by-3",
				Valid:  true,
			},
			Status:       1,
			HasTicketing: true,
		},
	}
}
