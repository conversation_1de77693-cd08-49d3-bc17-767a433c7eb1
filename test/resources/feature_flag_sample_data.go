// Package resources ...
package resources

import (
	"database/sql"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

const (
	// TestFeatureFlag ...
	TestFeatureFlag = "test-feature-flag"
)

// SampleDataFeatureFlag ...
func SampleDataFeatureFlag() *storage.FeatureFlagDTO {
	return &storage.FeatureFlagDTO{
		ID:          1,
		CreatedAt:   sql.NullTime{Time: time.Now()},
		UpdatedAt:   sql.NullTime{Time: time.Now()},
		CreatedBy:   sql.NullInt64{Int64: 1},
		UpdatedBy:   sql.NullInt64{Valid: true, Int64: 1},
		Name:        "Test Feature Flag",
		Value:       1,
		Description: "test feature flag",
		Status:      1,
	}
}

// SampleDataUserPermissionsFeatureFlagDetail ...
func SampleDataUserPermissionsFeatureFlagDetail() *permissionManagementStorage.UserPermissionsDetail {
	return &permissionManagementStorage.UserPermissionsDetail{
		ID:      1,
		Name:    "Test User",
		Email:   "<EMAIL>",
		UserID:  "test-user-id",
		Modules: []string{"ADMIN_CONFIG"},
		Roles:   []string{"ADMIN"},
		Permissions: map[string]int64{
			"FEATURE_FLAG": 7,
		},
	}
}

// ExpectedSuccess ...
const ExpectedSuccess = `{"status":"success"}`

// ExpectedUserPermissionFeatureFlagError ...
const ExpectedUserPermissionFeatureFlagError = `{
						"code": "forbidden",
						"message": "User is not authorized to perform this element action"
				  	}`

// ExpectedAuthorizationFeatureFlagError ...
const ExpectedAuthorizationFeatureFlagError = `{
					"code": "unauthorized",
					"message": "failed to validate authorization",
					"errors": [
						{
							"errorCode": "resourceNotFound",
							"message": "User is not found"
						},
						{
							"errorCode": "resourceNotFound",
							"message": "failed to get user by id"
						}
					]
				}`

// SampleDataCreateFeatureFlagAuditTrail ...
func SampleDataCreateFeatureFlagAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.FeatureFlagKey,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Feature flag successfully created",
		Description:  "Feature flag successfully created",
		ActivityType: constants.FeatureFlagConfig,
	}
}

// SampleDataUpdateFeatureFlagAuditTrail ...
func SampleDataUpdateFeatureFlagAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.FeatureFlagKey,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Feature flag successfully updated",
		Description:  "Feature flag successfully updated",
		ActivityType: constants.FeatureFlagConfig,
	}
}

// SampleDataDeleteFeatureFlagAuditTrail ...
func SampleDataDeleteFeatureFlagAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.FeatureFlagKey,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Feature flag successfully deleted",
		Description:  "Feature flag successfully deleted",
		ActivityType: constants.FeatureFlagConfig,
	}
}

// SampleDataGetFeatureFlagList ...
func SampleDataGetFeatureFlagList() []*storage.FeatureFlagListDTO {
	return []*storage.FeatureFlagListDTO{
		{
			Name:        "Test Feature Flag",
			Value:       1,
			Status:      1,
			Description: "test feature flag",
			CreatedAt:   sql.NullTime{Time: time.Now()},
			UpdatedAt:   sql.NullTime{Time: time.Now()},
			CreatedBy:   sql.NullString{Valid: true, String: "test-created-by"},
			UpdatedBy:   sql.NullString{Valid: true, String: "test-updated-by"},
		},
		{
			Name:        "Test Feature Flag 2",
			Value:       1,
			Status:      1,
			Description: "test feature flag",
			CreatedAt:   sql.NullTime{Time: time.Now()},
			UpdatedAt:   sql.NullTime{Time: time.Now()},
			CreatedBy:   sql.NullString{Valid: true, String: "test-created-by"},
			UpdatedBy:   sql.NullString{Valid: true, String: "test-updated-by"},
		},
	}
}

// ConvertToRowsGetFeatureFlagList ...
func ConvertToRowsGetFeatureFlagList(mocker sqlmock.Sqlmock, featureFlagList []*storage.FeatureFlagListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"name", "value", "description", "status", "created_at", "updated_at", "created_by", "updated_by"})

	for _, row := range featureFlagList {
		mockRow.AddRow(row.Name, row.Value, row.Description, row.Status, row.CreatedAt, row.UpdatedAt, row.CreatedBy, row.UpdatedBy)
	}
	return mockRow
}
