package resources

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// SampleDataCreateModuleAuditTrail ...
func SampleDataCreateModuleAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "1",
		IdentifierType: constants.ModuleID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		ActivityType: string(constants.ModuleConfig),
		Title:        fmt.Sprintf("Module %s successfully created", "test module"),
		Description:  fmt.Sprintf("Module %s successfully created", "test module"),
	}
}

// SampleDataUserNoPermissionDetail ...
func SampleDataUserNoPermissionDetail() *permissionManagementStorage.UserPermissionsDetail {
	return &permissionManagementStorage.UserPermissionsDetail{
		ID:     1,
		Name:   "Test User",
		Email:  "<EMAIL>",
		UserID: "test-user-id",
	}
}

// SampleDataModule ...
func SampleDataModule() *storage.ModuleDTO {
	return &storage.ModuleDTO{
		ID:        1,
		CreatedAt: sql.NullTime{Time: time.Now()},
		UpdatedAt: sql.NullTime{Time: time.Now()},
		CreatedBy: sql.NullInt64{Int64: 1},
		UpdatedBy: sql.NullInt64{Valid: true, Int64: 1},
		Name:      "Test Module",
		Status:    1,
	}
}

// ConvertToRowsGetModuleList ...
func ConvertToRowsGetModuleList(mocker sqlmock.Sqlmock, permList []*storage.ModuleListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "status"})

	for _, row := range permList {
		mockRow.AddRow(row.ID, row.Name, row.CreatedAt, row.CreatedBy, row.UpdatedAt, row.UpdatedBy, row.Status)
	}
	return mockRow
}

// SampleDataGetModuleList ...
func SampleDataGetModuleList() []*storage.ModuleListDTO {
	return []*storage.ModuleListDTO{
		{
			ID:        1,
			CreatedAt: SampleDefaultTimestamp,
			UpdatedAt: SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-1",
				Valid:  true,
			},
			UpdatedBy: sql.NullString{
				String: "updated_by-1",
				Valid:  true,
			},
			Name:   "name-1",
			Status: 1,
		},
		{
			ID:        2,
			CreatedAt: SampleDefaultTimestamp,
			UpdatedAt: SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-2",
				Valid:  true,
			},
			Name: "name-2",
			UpdatedBy: sql.NullString{
				String: "updated_by-2",
				Valid:  true,
			},
			Status: 1,
		},
		{
			ID:        3,
			CreatedAt: SampleDefaultTimestamp,
			UpdatedAt: SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "created_by-3",
				Valid:  true,
			},
			Name: "name-3",
			UpdatedBy: sql.NullString{
				String: "updated_by-3",
				Valid:  true,
			},
			Status: 1,
		},
	}
}
