package resources

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// SampleDefaultTimestamp ...
var SampleDefaultTimestamp = sql.NullTime{Time: time.Date(2024, time.December, 12, 10, 0, 0, 0, time.UTC), Valid: true}

// SampleDataGetPermissionList ...
func SampleDataGetPermissionList() []*permissionManagementStorage.PermissionListDTO {
	return []*permissionManagementStorage.PermissionListDTO{
		{
			ID:           1,
			Name:         "Read",
			Description:  "Read for Module User Management",
			Status:       1,
			BitwiseValue: 1,
			CreatedAt:    SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			UpdatedAt: SampleDefaultTimestamp,
			UpdatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			ModuleID:   1,
			ModuleName: "User Management",
		},
		{
			ID:           2,
			Name:         "Create",
			Description:  "Create for Module User Management",
			Status:       1,
			BitwiseValue: 2,
			CreatedAt:    SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			UpdatedAt: SampleDefaultTimestamp,
			UpdatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			ModuleID:   1,
			ModuleName: "User Management",
		},
		{
			ID:           3,
			Name:         "Update",
			Description:  "Update for Module User Management",
			Status:       1,
			BitwiseValue: 4,
			CreatedAt:    SampleDefaultTimestamp,
			CreatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			UpdatedAt: SampleDefaultTimestamp,
			UpdatedBy: sql.NullString{
				String: "Test User",
				Valid:  true,
			},
			ModuleID:   1,
			ModuleName: "User Management",
		},
	}
}

// ConvertToRowsGetPermissionList ...
func ConvertToRowsGetPermissionList(mocker sqlmock.Sqlmock, permList []*permissionManagementStorage.PermissionListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "description", "status", "bitwise_value", "created_at", "updated_at", "created_by", "updated_by", "module_id", "module_name"})

	for _, row := range permList {
		mockRow.AddRow(row.ID, row.Name, row.Description, row.Status, row.BitwiseValue, row.CreatedAt, row.UpdatedAt, row.CreatedBy, row.UpdatedBy, row.ModuleID, row.ModuleName)
	}
	return mockRow
}

// SampleDataForCreateUpdatePermission ...
func SampleDataForCreateUpdatePermission() *permissionManagementStorage.PermissionDTO {
	return &permissionManagementStorage.PermissionDTO{
		ID:           4,
		Name:         "Read",
		Description:  "Read for Admin Configuration",
		Status:       1,
		BitwiseValue: 1,
		ModuleID:     2,
		CreatedAt:    SampleDefaultTimestamp,
		UpdatedAt:    SampleDefaultTimestamp,
		CreatedBy:    sql.NullInt64{Int64: 1},
		UpdatedBy:    sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataForCreatePermission ...
func SampleDataForCreatePermission() *permissionManagementStorage.PermissionDTO {
	return &permissionManagementStorage.PermissionDTO{
		ID:           4,
		Name:         "Read",
		Description:  "Read for Admin Configuration",
		Status:       1,
		BitwiseValue: 1,
		ModuleID:     2,
		CreatedAt:    SampleDefaultTimestamp,
		UpdatedAt:    SampleDefaultTimestamp,
		CreatedBy:    sql.NullInt64{Int64: 1},
		UpdatedBy:    sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataForUpdatePermission ...
func SampleDataForUpdatePermission() *permissionManagementStorage.PermissionDTO {
	return &permissionManagementStorage.PermissionDTO{
		ID:           4,
		Name:         "Read Updated",
		Description:  "Read for Admin Configuration - Updated",
		Status:       1,
		BitwiseValue: 2,
		ModuleID:     2,
		CreatedAt:    SampleDefaultTimestamp,
		UpdatedAt:    SampleDefaultTimestamp,
		CreatedBy:    sql.NullInt64{Int64: 1},
		UpdatedBy:    sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataAuditTrailPermission ...
func SampleDataAuditTrailPermission(title string, id int64, createdBy int64, name string) *storage.AuditTrailsDTO {
	return &storage.AuditTrailsDTO{
		Identifier:     fmt.Sprintf("%d", id),
		IdentifierType: constants.PermissionID,
		Title:          title,
		Description:    fmt.Sprintf("%s for ID %d : %s executed successfully", title, id, name),
		ActivityType:   constants.Permission,
		ReferenceID:    "",
		CreatedBy:      sql.NullInt64{Int64: createdBy, Valid: true},
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
	}
}

// SampleRowGetPermissionByID ...
func SampleRowGetPermissionByID(mocker sqlmock.Sqlmock, data *permissionManagementStorage.PermissionDTO) *sqlmock.Rows {
	return mocker.NewRows([]string{"id", "name", "bitwise_value", "description", "module_id", "status", "created_at", "updated_at", "created_by", "updated_by"}).AddRow(
		data.ID, data.Name, data.BitwiseValue, data.Description, data.ModuleID, data.Status, data.CreatedAt, data.UpdatedAt, data.CreatedBy, data.UpdatedBy)
}
