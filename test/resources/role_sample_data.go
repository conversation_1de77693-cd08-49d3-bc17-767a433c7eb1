package resources

import (
	"database/sql"
	"fmt"
	"time"

	permissionManagementAPI "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// SampleDataForCreateRole ...
func SampleDataForCreateRole() *permissionManagementStorage.RoleDTO {
	return &permissionManagementStorage.RoleDTO{
		ID:        4,
		Name:      "Admin",
		Status:    1,
		CreatedAt: SampleDefaultTimestamp,
		UpdatedAt: SampleDefaultTimestamp,
		CreatedBy: sql.NullInt64{Int64: 1},
		UpdatedBy: sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataForUpdateRole ...
func SampleDataForUpdateRole() *permissionManagementStorage.RoleDTO {
	return &permissionManagementStorage.RoleDTO{
		ID:        4,
		Name:      "Admin Update",
		Status:    1,
		CreatedAt: SampleDefaultTimestamp,
		UpdatedAt: SampleDefaultTimestamp,
		CreatedBy: sql.NullInt64{Int64: 1},
		UpdatedBy: sql.NullInt64{Valid: true, Int64: 1},
	}
}

// SampleDataForRoleElementPermissions ...
func SampleDataForRoleElementPermissions() []*permissionManagementAPI.ElementPermissionsRequest {
	return []*permissionManagementAPI.ElementPermissionsRequest{
		{
			ElementID:      1,
			PermissionsIDs: []int64{0, 1},
		},
		{
			ElementID:      2,
			PermissionsIDs: []int64{2},
		},
	}
}

// SampleDataAuditTrailRole ...
func SampleDataAuditTrailRole(title string, id int64, createdBy int64, name string) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     fmt.Sprintf("%d", id),
		IdentifierType: constants.RoleID,
		Title:          title,
		Description:    fmt.Sprintf("%s for ID %d : %s executed successfully", title, id, name),
		ActivityType:   constants.Role,
		ReferenceID:    "",
		CreatedBy:      sql.NullInt64{Int64: createdBy, Valid: true},
		CreatedAt:      sql.NullTime{Time: time.Now(), Valid: true},
	}
}

// SampleDataExistingRolesPermission ...
func SampleDataExistingRolesPermission() *[]permissionManagementStorage.RoleElementPermissionDTO {
	return &[]permissionManagementStorage.RoleElementPermissionDTO{
		{ID: 1, ElementID: 1, BitwiseValue: 2, RoleID: 4}, // Needs update
		{ID: 2, ElementID: 2, BitwiseValue: 4, RoleID: 4}, // Needs delete
	}
}
