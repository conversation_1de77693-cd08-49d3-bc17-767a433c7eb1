// Package resources ...
package resources

import (
	"database/sql"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	auditTrailStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/storage"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

const (
	// TestUserID ...
	TestUserID = "test-user-id"
	// NewUserID ...
	NewUserID = "new-user-id"
	// SuccessResponse ...
	SuccessResponse = `{"status":"Success"}`
)

// SampleDataUser ...
func SampleDataUser() *permissionManagementStorage.UserDTO {
	// the password is "password"
	return &permissionManagementStorage.UserDTO{
		ID:           1,
		Name:         "Test User",
		Email:        "<EMAIL>",
		Password:     "$2a$10$7dgJ6nw.XKW0.A22VzcpdO1WAkS7ZmUK0HOljIGe6/iV0/AYOStUW",
		Status:       1,
		UserID:       "test-user-id",
		CreatedAt:    sql.NullTime{Time: time.Now()},
		UpdatedAt:    sql.NullTime{Time: time.Now()},
		CreatedBy:    sql.NullInt64{Int64: 1},
		UpdatedBy:    sql.NullInt64{Valid: true, Int64: 1},
		RefreshToken: sql.NullString{Valid: true, String: "test-refresh-token"},
	}
}

// SampleRowsGetUserByEmail ...
func SampleRowsGetUserByEmail(mocker sqlmock.Sqlmock, sampleUserData *permissionManagementStorage.UserDTO) *sqlmock.Rows {
	return mocker.NewRows([]string{"id", "name", "email", "password", "status", "user_id", "created_at", "updated_at", "created_by", "updated_by"}).AddRow(
		sampleUserData.ID, sampleUserData.Name, sampleUserData.Email, sampleUserData.Password, sampleUserData.Status, sampleUserData.UserID, sampleUserData.CreatedAt, sampleUserData.UpdatedAt, sampleUserData.CreatedBy, sampleUserData.UpdatedBy)
}

// SampleRowsGetUserByUserID ...
func SampleRowsGetUserByUserID(mocker sqlmock.Sqlmock, sampleUserData *permissionManagementStorage.UserDTO) *sqlmock.Rows {
	return mocker.NewRows([]string{"id", "name", "email", "password", "created_at", "updated_at", "created_by", "updated_by", "status", "user_id", "refresh_token"}).AddRow(
		sampleUserData.ID, sampleUserData.Name, sampleUserData.Email, sampleUserData.Password, sampleUserData.CreatedAt, sampleUserData.UpdatedAt, sampleUserData.CreatedBy, sampleUserData.UpdatedBy, sampleUserData.Status, sampleUserData.UserID, sampleUserData.RefreshToken)
}

// SampleDataUserPermissionsDetail ...
func SampleDataUserPermissionsDetail() *permissionManagementStorage.UserPermissionsDetail {
	return &permissionManagementStorage.UserPermissionsDetail{
		ID:      1,
		Name:    "Test User",
		Email:   "<EMAIL>",
		UserID:  "test-user-id",
		Modules: []string{"User Management", "Admin Configuration"},
		Roles:   []string{"IAM"},
		Permissions: map[string]int64{
			"USER_MANAGEMENT":  7,
			"ROLE_MANAGEMENT":  7,
			"MODULE_CONFIG":    7,
			"DATA_SEGREGATION": 7,
		},
	}
}

// SampleDataUserLoginAuditTrail ...
func SampleDataUserLoginAuditTrail(user *permissionManagementStorage.UserDTO) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     user.UserID,
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: user.ID,
			Valid: true,
		},
		Title:        "Login",
		Description:  "Login Successful",
		ActivityType: constants.Login,
	}
}

// SampleDataForGetListElementPermission ...
func SampleDataForGetListElementPermission() []*permissionManagementStorage.RoleElementPermissionListDTO {
	return []*permissionManagementStorage.RoleElementPermissionListDTO{
		{
			ElementID:    sql.NullInt64{Int64: 1, Valid: true},
			ElementCode:  sql.NullString{String: "USER_MANAGEMENT", Valid: true},
			BitwiseValue: sql.NullInt64{Int64: 7, Valid: true},
			RoleName:     "IAM",
			ModuleName:   sql.NullString{String: "User Management", Valid: true},
		},
		{
			ElementID:    sql.NullInt64{Int64: 2, Valid: true},
			ElementCode:  sql.NullString{String: "ROLE_MANAGEMENT", Valid: true},
			BitwiseValue: sql.NullInt64{Int64: 7, Valid: true},
			RoleName:     "IAM",
			ModuleName:   sql.NullString{String: "Role Management", Valid: true},
		},
	}
}

// SampleRowsGetListElementPermission ...
func SampleRowsGetListElementPermission(mocker sqlmock.Sqlmock) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"role_name", "element_id", "element_code", "bitwise_value", "module_name"})

	for _, row := range SampleDataForGetListElementPermission() {
		mockRow.AddRow(row.RoleName, row.ElementID, row.ElementCode, row.BitwiseValue, row.ModuleName)
	}
	return mockRow
}

// SampleDataGetUserList ...
func SampleDataGetUserList() []*permissionManagementStorage.UserListDTO {
	return []*permissionManagementStorage.UserListDTO{
		{
			ID:           1,
			Name:         "Test User",
			Email:        "<EMAIL>",
			Password:     "$2a$10$7dgJ6nw.XKW0.A22VzcpdO1WAkS7ZmUK0HOljIGe6/iV0/AYOStUW",
			Status:       1,
			UserID:       "test-user-id",
			CreatedAt:    sql.NullTime{Time: time.Now()},
			UpdatedAt:    sql.NullTime{Time: time.Now()},
			CreatedBy:    sql.NullString{Valid: true, String: "test-created-by"},
			UpdatedBy:    sql.NullString{Valid: true, String: "test-updated-by"},
			RefreshToken: sql.NullString{Valid: true, String: "test-refresh-token"},
		},
		{
			ID:           2,
			Name:         "Test User 2",
			Email:        "<EMAIL>",
			Password:     "$2a$10$7dgJ6nw.XKW0.A22VzcpdO1WAkS7ZmUK0HOljIGe6/iV0/AYOStUW",
			Status:       1,
			UserID:       "test-user-id-2",
			CreatedAt:    sql.NullTime{Time: time.Now()},
			UpdatedAt:    sql.NullTime{Time: time.Now()},
			CreatedBy:    sql.NullString{Valid: true, String: "test-created-by"},
			UpdatedBy:    sql.NullString{Valid: true, String: "test-updated-by"},
			RefreshToken: sql.NullString{Valid: true, String: "test-refresh-token"},
		},
	}
}

// ConvertToRowsGetUserList ...
func ConvertToRowsGetUserList(mocker sqlmock.Sqlmock, userList []*permissionManagementStorage.UserListDTO) *sqlmock.Rows {
	mockRow := mocker.NewRows([]string{"id", "name", "status", "email", "created_at", "updated_at", "created_by", "updated_by", "user_id"})

	for _, row := range userList {
		mockRow.AddRow(row.ID, row.Name, row.Status, row.Email, row.CreatedAt, row.UpdatedAt, row.CreatedBy, row.UpdatedBy, row.UserID)
	}
	return mockRow
}

// SampleDataForCreateUpdateUser ...
func SampleDataForCreateUpdateUser() *permissionManagementStorage.UserDTO {
	return &permissionManagementStorage.UserDTO{
		ID:           2,
		Name:         "New User",
		Email:        "<EMAIL>",
		Password:     "$2a$10$55fAOYBbv7iM0g/Jf5KWM.4UPTs/D12YKqmTxo3UQi9.i1ES0An6u",
		Status:       1,
		UserID:       "new-user-id",
		CreatedAt:    sql.NullTime{Time: time.Now()},
		UpdatedAt:    sql.NullTime{Time: time.Now()},
		CreatedBy:    sql.NullInt64{Int64: 1},
		UpdatedBy:    sql.NullInt64{Valid: true, Int64: 1},
		RefreshToken: sql.NullString{Valid: true, String: "test-refresh-token"},
	}
}

// SampleDataCreateUserAuditTrail ...
func SampleDataCreateUserAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Create User",
		Description:  "Create User executed successfully",
		ActivityType: constants.User,
	}
}

// SampleDataUpdateUserStatusAuditTrail ...
func SampleDataUpdateUserStatusAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Update User Status",
		Description:  "Update User Status executed successfully",
		ActivityType: constants.User,
	}
}

// SampleDataUpdateUserAuditTrail ...
func SampleDataUpdateUserAuditTrail(id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     "identifier-id",
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Update User",
		Description:  "Update User executed successfully",
		ActivityType: constants.User,
	}
}

// SampleDataLogoutUser ...
func SampleDataLogoutUser(userID string, id int64) *auditTrailStorage.AuditTrailsDTO {
	return &auditTrailStorage.AuditTrailsDTO{
		Identifier:     userID,
		IdentifierType: constants.UserID,
		CreatedAt: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedBy: sql.NullInt64{
			Int64: id,
			Valid: true,
		},
		Title:        "Logout",
		Description:  "Logout Successful",
		ActivityType: constants.Logout,
	}
}
