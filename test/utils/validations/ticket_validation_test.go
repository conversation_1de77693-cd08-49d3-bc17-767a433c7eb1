package validations

import (
	"testing"

	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	appValidations "gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

func TestSortByValidation(t *testing.T) {
	appValidations.InitValidator("MY")

	tests := []struct {
		name     string
		request  *api.GetTicketListRequest
		expected bool
	}{
		{
			name: "Valid sort column - id",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{
					Column: "id",
					Sort:   api.SortOrder_ASC,
				},
			},
			expected: true,
		},
		{
			name: "Valid sort column - createdAt",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{
					Column: "createdAt",
					Sort:   api.SortOrder_DESC,
				},
			},
			expected: true,
		},
		{
			name: "Invalid sort column",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{
					Column: "createdOn",
					Sort:   api.SortOrder_ASC,
				},
			},
			expected: false,
		},
		{
			name: "Invalid sort column",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{
					Column: "invalid_column",
					Sort:   api.SortOrder_ASC,
				},
			},
			expected: false,
		},
		{
			name: "Invalid sort column - DB column name",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{
					Column: "t.id",
					Sort:   api.SortOrder_ASC,
				},
			},
			expected: false,
		},
		{
			name: "Empty sort column",
			request: &api.GetTicketListRequest{
				SortBy: &api.Sort{},
			},
			expected: true,
		},
		{
			name:     "Nil sort",
			request:  &api.GetTicketListRequest{},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ok, err := appValidations.IsValid(tt.request)
			if ok != tt.expected {
				t.Errorf("Expected %v, got %v, error: %v", tt.expected, ok, err)
			}
		})
	}
}
