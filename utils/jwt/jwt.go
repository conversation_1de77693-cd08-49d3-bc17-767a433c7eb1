// Package jwt provides common functions to generate and parse JWT tokens.
package jwt

import (
	"errors"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// GenerateJWTStringWithClaims generates a JWT token with algorithm RS256.
func GenerateJWTStringWithClaims(claims map[string]interface{}, expiredTimeInMinute int, key string) (string, error) {
	// set expiration time
	mapClaims := jwt.MapClaims(claims)
	mapClaims["exp"] = time.Now().Add(time.Minute * time.Duration(expiredTimeInMinute)).Unix()

	// generate token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, mapClaims)

	// sign token
	tokenString, err := token.SignedString([]byte(key))
	if err != nil {
		return "", errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return tokenString, nil
}

// ParseJWTStringWithClaims parses a JWT token with algorithm RS256.
func ParseJWTStringWithClaims(tokenString string, key string) (map[string]interface{}, error) {
	// separate bearer string
	auth := strings.Split(tokenString, " ")
	if len(auth) != 2 && strings.ToLower(auth[0]) != "bearer" {
		return nil, errorwrapper.Error(apiError.Forbidden, "Invalid token format")
	}

	tokenString = auth[1]

	// parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(key), nil
	})
	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errorwrapper.Error(apiError.Unauthorized, "Expired token")
		}
		if errors.Is(err, jwt.ErrTokenSignatureInvalid) {
			return nil, errorwrapper.Error(apiError.Forbidden, "Invalid token signature")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	// validate token
	if !token.Valid {
		return nil, errorwrapper.Error(apiError.Forbidden, "Invalid token")
	}

	// get claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errorwrapper.Error(apiError.InternalServerError, "Failed to parse claims")
	}

	return claims, nil
}
