// Package secret provides common functions to generate and match password hash.
package secret

import (
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"golang.org/x/crypto/bcrypt"
)

// GeneratePassword generates a password hash.
func GeneratePassword(password string) (hash string, err error) {
	var hashedPassword []byte
	hashedPassword, err = bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	hash = string(hashedPassword)
	return
}

// MatchPassword matches a password with a hash.
func MatchPassword(password string, hash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return errorwrapper.Error(apiError.BadRequest, "Invalid password")
		}
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return nil
}
