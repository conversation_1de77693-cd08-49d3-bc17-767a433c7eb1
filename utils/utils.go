// Package utils defines utility functions for the application.
//
// nolint: golint,goimports
package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/dustin/go-humanize"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetTraceID returns the TraceID from the context to include in logs for tracing
func GetTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	return slog.TraceID(strconv.Itoa(int(span.Context().TraceID())))
}

// MinInt ...
func MinInt(a, b int) int {
	if a > b {
		return b
	}
	return a
}

// SliceContains ...
func SliceContains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// ConvertArrayIntToAny ...
func ConvertArrayIntToAny(arr []int64) []any {
	var anySlice []any

	if len(arr) > 0 {
		for _, v := range arr {
			anySlice = append(anySlice, v)
		}
	}

	return anySlice
}

// ConvertArrayStringToAny ...
func ConvertArrayStringToAny(arr []string) []any {
	var anySlice []any

	if len(arr) > 0 {
		for _, v := range arr {
			anySlice = append(anySlice, v)
		}
	}

	return anySlice
}

// DateAsString always return in format of 2024-12-04 09:01:45 +0000 UTC, if empty return empty string
func DateAsString(date time.Time) string {
	if date.IsZero() {
		return ""
	}

	return date.String()
}

// LoadStruct converts a map or struct to a struct of type s
func LoadStruct(data interface{}, s interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal payload to json: %w", err)
	}

	err = json.Unmarshal(jsonData, &s)
	if err != nil {
		return fmt.Errorf("failed to unmarshal json to struct: %w", err)
	}

	return nil
}

// FormatInterestValue ...
func FormatInterestValue(value string) string {
	if value == "" {
		return value
	}

	return fmt.Sprintf("%s %% p.a", value)
}

// FormatWithPercentage ...
func FormatWithPercentage(value string) string {
	if value == "" {
		return value
	}

	return fmt.Sprintf("%s %%", value)
}

// HumanizeBalance formats a numeric value into a human-readable string with commas and two decimal places.
func HumanizeBalance(value interface{}, divideDecimal bool) string {
	var number float64

	switch v := value.(type) {
	case int64:
		number = float64(v)
	case float64:
		number = v
	case float32:
		number = float64(v)
	case string:
		if v == "" {
			return ""
		}
		number, _ = strconv.ParseFloat(v, 64)
	default:
		return ""
	}

	if divideDecimal {
		number = number / 100
	}

	formatted := fmt.Sprintf("%.2f", number)
	parts := strings.Split(formatted, ".")

	intPart, _ := strconv.ParseInt(parts[0], 10, 64)
	intWithCommas := humanize.Comma(intPart)

	return fmt.Sprintf("%s.%s", intWithCommas, parts[1])
}

// IsValidPhoneNumber validate phone number
func IsValidPhoneNumber(phone string) (bool, string) {
	// Remove whitespace
	phone = strings.TrimSpace(phone)

	// Check if length is between 10 and 15 characters
	if len(phone) < 10 || len(phone) > 15 {
		return false, "Fill with 10 - 15 characters"
	}

	// Check valid prefixes
	if !(strings.HasPrefix(phone, "+628") ||
		strings.HasPrefix(phone, "628") ||
		strings.HasPrefix(phone, "08")) {
		return false, `Start with "+628xxx"/ "628xxx"/ "08xxx"`
	}

	return true, ""
}

// IsValidSafeID validates safeID
func IsValidSafeID(safeID string) bool {
	// Remove any whitespace
	safeID = strings.TrimSpace(safeID)

	// Check exact length of 36 characters
	return len(safeID) == 36
}

// IsValidAccountID validates account ID
func IsValidAccountID(accountID string) (bool, string) {
	// Remove any whitespace
	accountID = strings.TrimSpace(accountID)

	// Check length
	if len(accountID) < 12 || len(accountID) > 15 {
		return false, "Fill with 12 - 15 characters"
	}

	// Check if all characters are numeric
	_, err := strconv.Atoi(accountID)
	if err != nil {
		return false, "Only numerics are allowed"
	}

	return true, ""
}

// IsValidCIF validates CIF
func IsValidCIF(cif string) (bool, string) {
	// Remove any whitespace
	cif = strings.TrimSpace(cif)

	// Check length
	if len(cif) != 14 {
		return false, "Fill with 14 characters"
	}

	// Check if all characters are numeric
	if !strings.HasPrefix(cif, "ID") {
		return false, `Start with "IDxxx"`
	}

	return true, ""
}

// IsValidName validates name
func IsValidName(name string) bool {
	// Remove any whitespace
	name = strings.TrimSpace(name)

	// Check if length is between 3 and 100 characters
	if len(name) < 3 || len(name) > 100 {
		return false
	}

	return true
}

// IsValidNIK validates NIK
func IsValidNIK(nik string) (bool, string) {
	// Remove any whitespace
	nik = strings.TrimSpace(nik)

	// Check length
	if len(nik) != 16 {
		return false, "Fill with 16 characters"
	}

	_, err := strconv.Atoi(nik)
	if err != nil {
		return false, "Only numerics are allowed"
	}

	return true, ""
}

// SplitAndTrim splits a string by a separator and trims whitespace from each element
func SplitAndTrim(s, sep string) []string {
	if s == "" {
		return []string{}
	}

	rawParts := strings.Split(s, sep)
	parts := make([]string, 0, len(rawParts))

	for _, part := range rawParts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			parts = append(parts, trimmed)
		}
	}

	return parts
}

// ConvertToWIBFormatted handle format date time into WIB
func ConvertToWIBFormatted(input interface{}) string {
	wib := time.FixedZone("WIB", 7*60*60)

	switch v := input.(type) {
	case time.Time:
		return v.In(wib).Format("02 Jan 2006 15:04:05")
	case string:
		t, err := time.Parse(time.RFC3339Nano, v)
		if err != nil {
			return v // fallback to original
		}
		return t.In(wib).Format("02 Jan 2006 15:04:05")
	default:
		return ""
	}
}

// ParsingAndFormatTime ...
func ParsingAndFormatTime(val string, currentFormat string, expectedFormat string, isLocal bool) string {
	parsedTime, err := time.Parse(currentFormat, val)
	if err != nil {
		return ""
	}

	if isLocal {
		loc, _ := time.LoadLocation("Asia/Jakarta")
		return parsedTime.In(loc).Format(expectedFormat)
	}
	return parsedTime.Format(expectedFormat)
}

func ToJSON[T any](params any) (result string, err error) {
	res, err := json.Marshal(params)
	if err != nil {
		log.Println(err)
		return "", err
	}
	return string(res), nil
}

// CreatePlaceholders generates a slice of SQL placeholders for use in IN clauses
func CreatePlaceholders(count int) []string {
	placeholders := make([]string, count)
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return placeholders
}
