package custom

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// BlockUnblockHoldCodesValidation is a custom validator for validating hold codes in block/unblock account request
func BlockUnblockHoldCodesValidation(fl validations.FieldLevel) bool {
	// Get the field value as a slice of strings
	holdCodes, ok := fl.Field().Interface().([]string)
	if !ok {
		slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "error getting hold codes")
		return false
	}

	// Check if the slice is empty
	if len(holdCodes) == 0 {
		slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "hold codes is empty")
		return false
	}

	var checkDuplicate = make(map[accountServiceAPI.ApplicableHoldcode]bool)
	for _, holdCode := range holdCodes {
		// forbid empty hold code
		if holdCode == "" {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "hold code is empty")
			return false
		}

		// forbid invalid hold code
		if !constants.AllowedHoldCodes[accountServiceAPI.ApplicableHoldcode(holdCode)] {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "invalid hold code")
			return false
		}

		// forbid duplicate hold code
		if checkDuplicate[accountServiceAPI.ApplicableHoldcode(holdCode)] {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "duplicate hold code")
			return false
		}
	}
	return true
}
