package custom

import (
	"strings"
	"unicode"

	"gitlab.super-id.net/bersama/common/validations"
)

// CIFValidation validates the CIF (Customer Information File) format.
func CIFValidation(fl validations.FieldLevel) bool {
	cif := fl.Field().String()

	// Check length is 14
	if len(cif) != 14 {
		return false
	}

	// Check starts with "ID"
	if !strings.HasPrefix(cif, "ID") {
		return false
	}

	// Check remaining 8 characters are digits
	for _, char := range cif[2:] {
		if !unicode.IsDigit(char) {
			return false
		}
	}

	return true
}
