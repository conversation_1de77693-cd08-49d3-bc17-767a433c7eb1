package custom

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
)

// DataSegregationSortByValidation ...
func DataSegregationSortByValidation(fl validations.FieldLevel) bool {
	return DefaultSortByValidation(fl, []string{"id", "name"}, "DataSegregationSortByValidation")
}

// ValidateNonEmptySegregationID ...
func ValidateNonEmptySegregationID(fl validations.FieldLevel) bool {
	holdCodes, ok := fl.Field().Interface().([]int64)
	if !ok {
		slog.FromContext(context.Background()).Error("DataSegregationIDsValidation", "error getting data segregation IDs")
		return false
	}

	// Check if the slice is empty
	if len(holdCodes) == 0 {
		slog.FromContext(context.Background()).Error("DataSegregationIDsValidation", "segregation IDs is empty")
		return false
	}

	return true
}
