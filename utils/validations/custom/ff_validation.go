//nolint:golint,typecheck
package custom

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// FeatureFlagFilterValidation ...
func FeatureFlagFilterValidation(fl validations.FieldLevel) bool {
	return DefaultFilterValidation(fl, []string{"status", "value"}, "FeatureFlagFilterValidation")
}

// FeatureFlagSortByValidation ...
func FeatureFlagSortByValidation(fl validations.FieldLevel) bool {
	return DefaultSortByValidation(fl, []string{"id", "name", "value", "created_at", "updated_at"}, "FeatureFlagSortByValidation")
}

// DefaultFilterValidation ...
func DefaultFilterValidation(fl validations.FieldLevel, allowedFilterColumn []string, eventType string) bool {
	filter, ok := fl.Field().Interface().([]api.Filter)
	if !ok {
		slog.FromContext(context.Background()).Error(eventType, "error getting filter")
		return false
	}

	for _, field := range filter {
		if field.Column != "" && !utils.SliceContains(allowedFilterColumn, field.Column) {
			slog.FromContext(context.Background()).Error(eventType, "invalid column")
			return false
		}
	}

	return true
}

// DefaultSortByValidation ...
func DefaultSortByValidation(fl validations.FieldLevel, allowedSortColumn []string, eventType string) bool {
	sort, ok := fl.Field().Interface().(api.Sort)
	if !ok {
		slog.FromContext(context.Background()).Error(eventType, "error getting filter")
		return false
	}

	if sort.Column != "" && !utils.SliceContains(allowedSortColumn, sort.Column) {
		slog.FromContext(context.Background()).Error(eventType, "invalid column")
		return false
	}

	return true
}
