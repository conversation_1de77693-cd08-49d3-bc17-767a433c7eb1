package custom

import (
	"strings"

	"gitlab.super-id.net/bersama/common/validations"
)

// SuperbankEmailValidation validates that email ends with @superbank.id
func SuperbankEmailValidation(fl validations.FieldLevel) bool {
	email := fl.Field().String()

	// Check if it's a valid email and has the correct domain
	return strings.HasSuffix(email, "@superbank.id")
}

// GXBEmailValidation validates that email ends with @gxbank.net
func GXBEmailValidation(fl validations.FieldLevel) bool {
	email := fl.Field().String()

	// Check if it's a valid email and has the correct domain
	return strings.HasSuffix(email, "@gxbank.net") || strings.HasSuffix(email, "@gxbank.my")
}
