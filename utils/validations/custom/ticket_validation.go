package custom

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// TicketSortByValidation validates the sort column for GetTicketListRequest
func TicketSortByValidation(fl validations.FieldLevel) bool {
	// List of allowed sort columns for GetTicketList
	allowedSortColumns := []string{
		"id",
		"caseType",
		"caseSubcategory",
		"customerSegmentName",
		"priorityName",
		"cif",
		"status",
		"createdAt",
		"dueOn",
		"closedOn",
		"assigneeUsername",
		"source",
		"channel",
	}

	sort, ok := fl.Field().Interface().(api.Sort)
	if !ok {
		sortPtr, ok := fl.Field().Interface().(*api.Sort)
		if !ok || sortPtr == nil {
			slog.FromContext(context.Background()).Error("TicketSortByValidation", "error getting sort field")
			return false
		}
		sort = *sortPtr
	}

	// If column is empty, it's valid (will use default sort)
	if sort.Column == "" {
		return true
	}

	if !utils.SliceContains(allowedSortColumns, sort.Column) {
		slog.FromContext(context.Background()).Error("SortByValidation", "invalid sort column")
		return false
	}

	return true
}
