package validations

import (
	"gitlab.super-id.net/bersama/common/validations"
	permissionManagementValidations "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/validations/custom"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations/custom"
)

var (
	aliasRegistrar = map[string]string{
		"account_id":   "required,numeric,min=12",
		"limit":        "omitempty,numeric,gte=0",
		"offset":       "omitempty,numeric,gte=0",
		"binary":       "omitempty,gte=0,lte=1",
		"phone_number": "required,min=10,max=16",
		"nik":          "required,numeric,eq=16",
		"cif":          "required,len=14",
	}

	customFuncRegistrar = map[string]validations.Func{
		"block_unblock_hold_codes": custom.BlockUnblockHoldCodesValidation,
		"roles_filter":             permissionManagementValidations.RolesFilterValidation,
		"roles_sort_by":            permissionManagementValidations.RolesSortByValidation,
		"users_filter":             permissionManagementValidations.UsersFilterValidation,
		"users_sort":               permissionManagementValidations.UsersSortByValidation,
		"ff_filter":                custom.FeatureFlagFilterValidation,
		"ff_sort":                  custom.FeatureFlagSortByValidation,
		"permissions_filter":       permissionManagementValidations.PermissionsFilterValidation,
		"permissions_sort_by":      permissionManagementValidations.PermissionsSortByValidation,
		"permission_bitwise":       permissionManagementValidations.PermissionBitwiseValidation,
		"identifier_type_value":    custom.IdentifierTypeValueValidation,
		"data_segregation_sort":    custom.DataSegregationSortByValidation,
		"non_empty_seg_ids":        custom.ValidateNonEmptySegregationID,
		"cif":                      custom.CIFValidation,
		"ticket_sort":              custom.TicketSortByValidation,
	}

	customFuncRegistrarMY = map[string]validations.Func{
		"email_domain": custom.GXBEmailValidation,
	}

	customFuncRegistrarSB = map[string]validations.Func{
		"email_domain": custom.SuperbankEmailValidation,
	}
)

// InitValidator initializes the bersama validator
func InitValidator(homeCountry string) {
	// register aliases
	for key, value := range aliasRegistrar {
		validations.AddAlias(key, value)
	}

	RegisterValidator(customFuncRegistrar)

	// register custom function based on home country
	switch homeCountry {
	case "MY":
		RegisterValidator(customFuncRegistrarMY)
	case "ID":
		RegisterValidator(customFuncRegistrarSB)
	}

	// compile validators
	err := validations.Load()
	if err != nil {
		// if error then there is some programming error
		panic(err)
	}
}

// IsValid validates the data against the validations defined in the struct tags
func IsValid[T any](data T) (ok bool, err error) {
	return validations.IsValid(data)
}

func RegisterValidator(customRegistrar map[string]validations.Func) {
	// register custom functions
	for key, value := range customRegistrar {
		validations.AddValidation(key, value)
	}
}
